const Package = require ("../models/package.model.js") ; 



exports.findAllPackage = (req, res) => {
    Package.findAllPackage((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving Ville."
      });
    else res.send(data);
  });
 
};

exports.getStatusByPackage = (req, res) => {
  const { id } = req.params; // Récupérer l'id depuis les paramètres de la requête

  Package.getStatusByPackage(id, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Aucun statut trouvé pour le package avec l'id ${id}.`
        });
      } else {
        res.status(500).send({
          message: err.message || "Erreur lors de la récupération du statut du package."
        });
      }
    } else {
      res.status(200).send(data); // Renvoi des données récupérées
    }
  });
};


// Fonction pour transférer les données de Cegid vers la base de données locale
exports.transferPackage = async (req, res) => {
  try {
    // Vérifier si l'application est en production
    if (process.env.NODE_ENV !== 'production') {
      return res.status(403).send({ message: "Accès refusé : cette API est disponible uniquement en production." });
    }

    // 1. Récupérer les données depuis Cegid
    const packages = await Package.findAllPackage();
    if (!packages || packages.length === 0) {
      return res.status(404).send({ message: "Aucune donnée trouvée dans Cegid." });
    }

    // 2. Insérer les données dans la base de données locale
    const insertionResult = await Package.insertPackage(packages);
    if (!insertionResult || insertionResult.insertedBarcodes.length === 0) {
      return res.status(400).send({ message: "Aucune donnée insérée dans la base de données locale." });
    }

    // 3. Supprimer les données insérées de la base Cegid
    await Package.deleteInsertedPackagesFromCegid(insertionResult.insertedBarcodes);

    // Réponse finale
    res.send({
      message: "Transfert des données réussi.",
      insertedBarcodes: insertionResult.insertedBarcodes,
    });
  } catch (error) {
    console.error("Erreur lors du transfert des données :", error);
    res.status(500).send({
      message: "Erreur lors du transfert des données.",
      error: error.message,
    });
  }
};



// Controller pour recevoir les packages
exports.receptionPackage = async (req, res) => {
  const { packages, userId, userName } = req.body; // On récupère les informations du body

  if (!packages || !userId || !userName) {
    return res.status(400).send({ message: 'Packages, userId ,code et userName  sont requis.' });
  }

  try {
    const result = await Package.receptionPackage(packages, userId, userName);
    res.status(200).send(result);
  } catch (error) {
    res.status(500).send({ message: 'Erreur interne', error: error.message });
  }
};


exports.receptionPackageMagasin = async (req, res) => {
  const { packages, userId, userName } = req.body; // On récupère les informations du body

  if (!packages || !userId || !userName) {
    return res.status(400).send({ message: 'Packages, userId ,code et userName  sont requis.' });
  }

  try {
    const result = await Package.receptionPackageMagasin(packages, userId, userName);
    res.status(200).send(result);
  } catch (error) {
    res.status(500).send({ message: 'Erreur interne', error: error.message });
  }
};


// Controller pour rechercher des colis avec filtres
exports.findPackages = (req, res) => {
  const { barcode, startDate, endDate, status } = req.body;
  console.log(req.body);

  // Si barcode est fourni, annule tous les autres filtres
  if (barcode) {
      // Vérification du format du barcode
      if (!/^\d+$/.test(barcode)) {
          return res.status(400).json({ message: "Barcode invalide" });
      }

      // Appel au modèle pour exécuter la requête avec seulement le barcode
      return Package.findPackages(barcode, null, null, null, (err, data) => {
          if (err) {
              res.status(500).json({ message: err.message || "Erreur lors de la recherche des colis." });
          } else {
              res.status(200).json(data);
          }
      });
  }

  // Vérification que startDate et endDate sont fournis si status est spécifié
  if (status && !(startDate && endDate)) {
      return res.status(400).json({ message: "Le statut nécessite des dates de début et de fin." });
  }

  // Vérification des filtres si barcode n'est pas fourni
  if (!barcode && !(startDate && endDate) && !status) {
      return res.status(400).json({ message: "Veuillez fournir au moins un filtre de recherche." });
  }

  // Vérification et nettoyage des entrées
  if (startDate && endDate) {
      if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate) || !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
          return res.status(400).json({ message: "Format de date invalide (YYYY-MM-DD)" });
      }
  }

  if (status) {
      const allowedStatuses = ["en attente", "en transit", "livré", "partiellement livré"];
      if (!allowedStatuses.includes(status)) {
          return res.status(400).json({ message: "Statut invalide" });
      }
  }

  // Appel au modèle pour exécuter la requête SQL sécurisée
  Package.findPackages(null, startDate, endDate, status, (err, data) => {
      if (err) {
          res.status(500).json({ message: err.message || "Erreur lors de la recherche des colis." });
      } else {
          res.status(200).json(data);
      }
  });


};

exports.getBarcodes = async (req, res) => {
  const { ids } = req.body;

  if (!ids || !Array.isArray(ids)) {
    return res.status(400).send({ message: 'Les ids doivent être un tableau d\'identifiants.' });
  }

  try {
    const barcodes = await Package.getBarcodes(ids);
    res.status(200).json(barcodes);
  } catch (error) {
    res.status(500).send({ message: 'Erreur lors de la génération des codes-barres', error: error.message });
  }
};







// Contrôleur pour générer les codes-barres
exports.generateCode = async (req, res) => {
  const { id, count } = req.body;
  if (!id || !count || count <= 0) {
    return res.status(400).send({ message: 'L\'ID et la quantité doivent être fournis.' });
  }

  try {
    const barcodes = await Package.generateCode(id, count);
    res.status(200).json({ barcodes });
  } catch (error) {
    res.status(500).send({ message: 'Erreur lors de la génération des codes-barres', error: error.message });
  }
};


exports.getHistoricalStatusStats = (req, res) => {
  Package.getHistoricalStatusStats((err, data) => {
    if (err) {
      res.status(500).send({ message: 'Erreur lors de la récupération des stats', error: err.message });
    } else {
      res.status(200).json(data);
    }
  });
};

exports.getStatisticPackage = (req, res) => {
  Package.getStatisticPackage((err, data) => {
    if (err) {
      res.status(500).send({ message: 'Erreur lors de la récupération des stats', error: err.message });
    } else {
      res.status(200).json(data);
    }
  });
};



exports.getPackageByStatusAndDate = (req, res) => {
  const { date, status } = req.body;
  console.log(date, status)
  if (!date || !status) {
    return res.status(400).send({ message: 'Les paramètres "date" et "status" sont requis.' });
  }

  Package.getPackageByStatusAndDate(date, status, (err, data) => {
    if (err) {
      res.status(500).send({ message: 'Erreur lors de la récupération des colis', error: err.message });
    } else {
      res.status(200).json(data);
    }
  });
};


exports.findPackageDepartByUser = (req, res) => {
  const { id } = req.params;

  Package.findPackageDepartByUser(id, (err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Une erreur s'est produite lors de la récupération des colis."
      });
    } else {
      res.send(data);
    }
  });
};

exports.findPackageArrivalByUser = (req, res) => {
  const { id } = req.params;

  Package.findPackageArrivalByUser(id, (err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Une erreur s'est produite lors de la récupération des colis."
      });
    } else {
      res.send(data);
    }
  });
};
