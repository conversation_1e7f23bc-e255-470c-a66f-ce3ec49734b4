const db = require("./db.js");
const https = require('https');
const Shipment = {};
require('dotenv').config();



DATAMATRIX_URL= process.env.DATAMATRIX_URL
DATAMATRIX_KEY= process.env.DATAMATRIX_KEY
const getDestinationsByCodeErp = (codesErp) => {
  return new Promise((resolve, reject) => {
    if (!codesErp.length) {
      resolve([]);
      return;
    }

    const placeholders = codesErp.map(() => "?").join(",");
    const sql = `SELECT * FROM destination WHERE code_erp IN (${placeholders})`;

    db.query(sql, codesErp, (err, res) => {
      if (err) {
        console.error("SQL error: ", err);
        reject(err);
      } else {
        resolve(res);
      }
    });
  });
};
const calculateDistance = (url) => {
  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      let data = '';
      response.on('data', (chunk) => {
        data += chunk;
      });
      response.on('end', () => {
        resolve(JSON.parse(data));
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
};
const addCommande = (newShipment) => {
  return new Promise((resolve, reject) => {
    const sql = `
      INSERT INTO commande (
        ajoutee_par, type_chargement, date_depart, date_arrivee,
        type_conditionnement, type_marchandise, volume, quantite, mention, fragile
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      65,                               // ajoutee_par
      "Carton",                           // type_chargement
      newShipment.departureDate,          // date_depart
      newShipment.arrivalDate,            // date_arrivee
      "Carton",                           // type_conditionnement
      "Produit Fini",                     // type_marchandise
      newShipment.volume || 0,             // volume
      newShipment.quantity || 0,           // quantite
      newShipment.desc || null,            // mention
      newShipment.fragile || false         // fragile
    ];

    db.query(sql, values, (err, res) => {
      if (err) {
        console.error("Erreur SQL commande : ", err);
        reject(err);
      } else {
        resolve({ commandeId: res.insertId });
      }
    });
  });
};


const insertShipment = (newShipment) => {
  return new Promise((resolve, reject) => {
    const sql = `
      INSERT INTO point_chargement (
        idCommande, nom_depart, nom_arrivee, volume, estimation, quantite, type_ligne, 
        date_depart, date_arrivee, adapted_volume, from_destination, to_destination,generated_by_erp,status,kilometrage
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?)
    `;

    const values = [
      newShipment.id_commande, 
      newShipment.nom_depart || null,
      newShipment.nom_arrivee || null,
      newShipment.volume || 0,
      newShipment.estimatedVolume || 0,
      newShipment.quantity || 0,
      newShipment.lineType || null,
      newShipment.departureDate || null,
      newShipment.arrivalDate || null,
      newShipment.adaptedVolume || null,
      newShipment.from_destination || null,
      newShipment.to_destination || null,
      true,
      "Valide",
      newShipment.kilometrage || null,

    ];

    db.query(sql, values, (err, res) => {
      if (err) {
        console.error("SQL error in insertShipment: ", err);
        reject(err);
      } else {
        resolve({ shipmentId: res.insertId });
      }
    });
  });
};

const mapLineTypeDescription = (lineType) => {
  switch (lineType) {
    case 1: return "Livraison PF (magasin)";
    case 2: return "Retour articles";
    case 3: return "Transfert PF inter magasin";
    default: return "Type de ligne inconnu";
  }
};


const validateRequiredFields = (newShipment) => {
  const requiredFields = {
    departureCode: "string",
    arrivalCode: "string",
    requesterName: "string",
    volume: "number",
    estimatedVolume: "number",
    quantity: "number",
    lineType: "number",
    departureDate: "string",
    arrivalDate: "string",
  };

  return Object.entries(requiredFields).reduce((errors, [field, type]) => {
    if (newShipment[field] === undefined || newShipment[field] === null) {
      errors.push({ field, error: "missing" });
    } else if (typeof newShipment[field] !== type) {
      errors.push({ field, error: "invalid type" });
    }
    return errors;
  }, []);
};


const validateDepartureAndArrivalCodes = (departureCode, arrivalCode, foundCodes) => {
  const errors = [];
  if (!foundCodes.includes(departureCode)) {
    errors.push({ field: "departureCode", error: "Destination de départ non trouvée" });
  }
  if (!foundCodes.includes(arrivalCode)) {
    errors.push({ field: "arrivalCode", error: "Destination d'arrivée non trouvée" });
  }
  return errors;
};


const stripTime = (date) => {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate());
};

const validateDates = (departureDate, arrivalDate) => {
  const errors = [];
  const today = stripTime(new Date()); // aujourd'hui sans l'heure

  departureDate = stripTime(new Date(departureDate));
  arrivalDate = stripTime(new Date(arrivalDate));

  if (isNaN(departureDate)) {
    errors.push({ field: "departureDate", error: "La date de départ n'est pas valide" });
  }
  if (isNaN(arrivalDate)) {
    errors.push({ field: "arrivalDate", error: "La date d'arrivée n'est pas valide" });
  }
  if (departureDate < today) {
    errors.push({ field: "departureDate", error: "La date de départ doit être égale ou supérieure à la date d'aujourd'hui" });
  }
  if (arrivalDate < departureDate) {
    errors.push({ field: "arrivalDate", error: "La date d'arrivée doit être égale ou supérieure à la date de départ" });
  }
  return errors;
};

const getUpdateFields = (updatedShipment) => {
  const fieldsToUpdate = [];
  const values = [];

  if (updatedShipment.quantite !== undefined) {
    fieldsToUpdate.push("quantite = ?");
    values.push(updatedShipment.quantity || 0);
  }
  if (updatedShipment.date_depart !== undefined) {
    fieldsToUpdate.push("date_depart = ?");
    values.push(updatedShipment.departureDate || null);
  }

  if (updatedShipment.date_arrivee !== undefined) {
    fieldsToUpdate.push("date_arrivee = ?");
    values.push(updatedShipment.arrivalDate || null);
  }

  if (updatedShipment.adaptedVolume !== undefined) {
    fieldsToUpdate.push("estimation = ?");
    values.push(updatedShipment.estimatedVolume || null);
  }

  if (updatedShipment.volume !== undefined) {
    fieldsToUpdate.push("volume = ?");
    values.push(updatedShipment.volume || null); 
  }

  return { fieldsToUpdate, values };
};


Shipment.updateShipment = (shipmentId, updatedShipment) => {
  return new Promise((resolve, reject) => {
    const { fieldsToUpdate, values } = getUpdateFields(updatedShipment);

    if (fieldsToUpdate.length === 0) {
      return reject(new Error("Aucune donnée valide à mettre à jour"));
    }

    values.push(shipmentId);

    // Conditions autorisées
    const allowedStatuses = ['Valide', 'Réservé', 'Expédier', 'Livré'];

    // Générer la clause SQL pour les statuts autorisés
    const statusPlaceholders = allowedStatuses.map(() => '?').join(', ');
    const sql = `
      UPDATE point_chargement
      SET ${fieldsToUpdate.join(', ')}
      WHERE id = ? AND status IN (${statusPlaceholders});
    `;

    // On doit ajouter les statuts autorisés à la liste des valeurs (après shipmentId)
    const queryValues = [...values, ...allowedStatuses];

    db.query(sql, queryValues, (err, res) => {
      if (err) {
        console.error("SQL error in updateShipment: ", err);
        return reject(err);
      }

      if (res.affectedRows === 0) {
        return reject(new Error("Aucune expédition trouvée avec cet ID ou statut non autorisé"));
      }

      resolve({ shipmentId, message: "Expédition mise à jour avec succès" });
    });
  });
};







Shipment.createShipments = async (shipments, result) => {
  try {
    let allErrors = [];
    let validShipments = [];

    if (!Array.isArray(shipments)) {
      result({ message: "Les expéditions doivent être un tableau." }, null);
      return;
    }


    if (allErrors.length > 0) {
      result({ message: "Certaines expéditions n'ont pas pu être créées", details: allErrors }, null);
      return;
    }

    let insertedShipments = [];

    // Traitement des expéditions valides...
    for (const newShipment of shipments) {
      let errors = validateRequiredFields(newShipment);
      
      const departureDate = new Date(newShipment.departureDate);
      const arrivalDate = new Date(newShipment.arrivalDate);
      errors = errors.concat(validateDates(departureDate, arrivalDate));

      if (newShipment.departureCode === newShipment.arrivalCode) {
        errors.push({ field: "departureCode/arrivalCode", error: "Le code de départ et le code d'arrivée ne peuvent pas être identiques." });
      }

      const destinations = await getDestinationsByCodeErp([newShipment.departureCode, newShipment.arrivalCode]);
      const foundCodes = destinations.map((dest) => dest.code_erp);
      errors = errors.concat(validateDepartureAndArrivalCodes(newShipment.departureCode, newShipment.arrivalCode, foundCodes));

      if (errors.length > 0) {
        allErrors.push({
          error: { details: errors }
        });
      } else {

        
        

        const departureInfoDep = destinations.find(dest => dest.code_erp === newShipment.departureCode);
        const departureInfoArr = destinations.find(dest => dest.code_erp === newShipment.arrivalCode);
        
        const fromDestination = await getDestinationDetailsById(departureInfoDep.id);
        const toDestination = await getDestinationDetailsById(departureInfoArr.id);
        console.log("from",fromDestination)
        console.log("from",toDestination)

        let origin = 'délégation de ' + fromDestination.nom_region + ", " + fromDestination.nom_ville + ", Tunisie";
        let destination = 'délégation de ' + toDestination.nom_region + ", " + toDestination.nom_ville + ", Tunisie";

        const kilometrage = await getKilometrage(
          departureInfoDep.id_region,
          departureInfoArr.id_region,
          origin,
          destination
        );
        
        newShipment.description_ligne = mapLineTypeDescription(newShipment.lineType);
        newShipment.lineType = mapLineTypeDescription(newShipment.lineType);
        newShipment.nom_depart = departureInfoDep.nom_locale;
        newShipment.nom_arrivee = departureInfoArr.nom_locale;
        newShipment.from_destination = departureInfoDep.id;
        newShipment.to_destination = departureInfoArr.id;
        newShipment.kilometrage = kilometrage;

        console.log(newShipment)
        const commande = await addCommande(newShipment); 
        newShipment.id_commande = commande.commandeId;

        validShipments.push(newShipment);
      }
    }

    if (allErrors.length > 0) {
      result({ message: "Certaines expéditions n'ont pas pu être créées", details: allErrors }, null);
      return;
    }

    // Insert valid shipments and collect their details
    for (const validShipment of validShipments) {
      const inserted = await insertShipment(validShipment);
      insertedShipments.push({
        shipmentId: inserted.shipmentId,
        departureCode: validShipment.departureCode,
        arrivalCode: validShipment.arrivalCode
      });
    }

    result(null, { 
      message: "Ligne de livraison insérée avec succès",
      createdShipments: insertedShipments
    });

  } catch (err) {
    result({ message: "Erreur interne du serveur", details: err.message }, null);
  }
};



async function getKilometrage(id1, id2, origin, destination) {
  if (id1 === id2) return 3;

  try {
    console.log("🔍 Recherche du kilométrage entre:", id1, id2, origin, destination);

    const distanceFromDB = await getDistanceFromDB(id1, id2);
    if (distanceFromDB !== null) {
      console.log("📦 Distance trouvée en base:", distanceFromDB);
      return distanceFromDB;
    }

    const apiResult = await getDistanceFromAPI(origin, destination);
    const distance = parseDistanceFromAPIResult(apiResult);
    const distanceText = `${distance}Km`;

    console.log("🌍 Distance reçue de l'API:", distanceText);

    await saveDistanceToDB(id1, id2, distanceText);
    return distance;

  } catch (err) {
    console.error("❌ Erreur dans getKilometrage:", err);
    throw err;
  }
}

function saveDistanceToDB(id1, id2, distanceText) {
  return new Promise((resolve, reject) => {
    const query = 'INSERT INTO matrix (id_from, id_to, distance) VALUES (?, ?, ?)';
    db.query(query, [id1, id2, distanceText], (err) => {
      if (err) return reject(err);
      resolve();
    });
  });
}


function parseDistanceFromAPIResult(data) {
  if (
    !data.rows || !data.rows[0] ||
    !data.rows[0].elements || !data.rows[0].elements[0].distance
  ) {
    throw new Error("Données invalides reçues de l'API Distance Matrix");
  }

  const distanceText = data.rows[0].elements[0].distance.text;
  return parseFloat(distanceText.replace("Km", "").trim());
}

function getDistanceFromAPI(origin, destination) {
  const url = `${DATAMATRIX_URL}origins=${encodeURIComponent(origin)}&destinations=${encodeURIComponent(destination)}&key=${DATAMATRIX_KEY}`;
  return calculateDistance(url);
}

function getDistanceFromDB(id1, id2) {
  return new Promise((resolve, reject) => {
    const query = `
      SELECT distance FROM matrix 
      WHERE (id_from = ? AND id_to = ?) OR (id_from = ? AND id_to = ?)
    `;
    db.query(query, [id1, id2, id2, id1], (err, results) => {
      if (err) return reject(err);
      if (results.length === 0) return resolve(null);

      const distanceText = results[0].distance || "0Km";
      const distanceDecimal = parseFloat(distanceText.replace("Km", "").trim());
      resolve(distanceDecimal);
    });
  });
}


  
const getDestinationDetailsById = (idDestination) => {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT 
        d.id AS id_destination,
        d.code_erp,
        r.id AS id_region,
        r.nom_region AS nom_region,
        v.id AS id_ville,
        v.nom_ville AS nom_ville
      FROM 
        destination d
      JOIN 
        region r ON d.id_region = r.id
      JOIN 
        ville v ON r.idVille = v.id
      WHERE 
        d.id = ?
    `;

    db.query(sql, [idDestination], (err, results) => {
      if (err) {
        console.error('Erreur SQL :', err);
        reject(err);
      } else {
        resolve(results[0] || null); // retourne null si pas trouvé
      }
    });
  });
};

  
  
  
  
  
module.exports = Shipment;