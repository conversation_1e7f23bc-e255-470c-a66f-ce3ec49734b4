const sql = require("./db.js");


//constructeur  

const TYPEFACTURATION = function(ville) {
    this.id=typeFacturation.id ;
    this.nom_type = typeFacturation.nom_type ;
    this.TVA = typeFacturation.TVA ;
    this.prix_unit = typeFacturation.prix_unit ;

  } ;



  TYPEFACTURATION.findAll = result => {
    sql.query("SELECT * FROM type_facturation", (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }
  
      //console.log("type_facturation: ", res);
      result(null, res);
    });
  }

module.exports = TYPEFACTURATION ; 

