const sql = require("./db.js");
const <PERSON><PERSON> = function(saison) {
    this.id= saison.id ;
    this.nom = saison.nom ;
    this.value = saison.value ;

  } ;



  Saison.getAll = result => {
    sql.query("SELECT * FROM repere", (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }
  
      //console.log("ville: ", res);
      result(null, res);
    });
  }


Saison.updateQte = (id, qte, result) => {
    sql.query(
        "UPDATE repere SET value = ? WHERE id = ?",
        [qte, id],
        (err, res) => {
            if (err) {
                console.log("error: ", err);
                result(err, null);
                return;
            }

            if (res.affectedRows == 0) {
                // La saison avec l'ID spécifié n'a pas été trouvée
                result({ kind: "not_found" }, null);
                return;
            }

            //console.log("updated saison: ", { id: id, value: qte });
            result(null, { id: id, value: qte });
        }
    );
};



  module.exports = Saison ; 
