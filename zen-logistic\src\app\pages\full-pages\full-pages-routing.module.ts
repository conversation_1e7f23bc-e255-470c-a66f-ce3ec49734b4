import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { UserProfilePageComponent } from "./user-profile/user-profile-page.component";
import { SearchComponent } from './search/search.component';
import { ListUserComponent } from '../list-user/list-user.component';
import { ProfiletransporteurComponent } from '../profiletransporteur/profiletransporteur.component';
import { ListConducteurComponent } from '../list-conducteur/list-conducteur.component';
import { WelcomConducteurComponent } from '../welcom-conducteur/welcom-conducteur.component';
import { WelcomeCamionComponent } from '../welcome-camion/welcome-camion.component';
import { ListCamionComponent } from '../list-camion/list-camion.component';
import { WelcomCommandeComponent } from '../welcom-commande/welcom-commande.component';
import { ListCommandeComponent } from '../list-commande/list-commande.component';
import { ListCommandeAdminComponent } from 'app/list-commande-admin/list-commande-admin.component';
import { ListCommandeExpComponent } from '../list-commande-exp/list-commande-exp.component';
import { ListCommValidExpComponent } from '../list-comm-valid-exp/list-comm-valid-exp.component';
import { ListCommAValiderExpComponent } from '../list-comm-a-valider-exp/list-comm-a-valider-exp.component';
import { MesReservationsComponent } from '../mes-reservations/mes-reservations.component';
import { ListUserTrComponent } from '../list-user-tr/list-user-tr.component';
import { AjouCommComponent } from '../ajou-comm/ajou-comm.component';
import { MesCommResComponent } from '../mes-comm-res/mes-comm-res.component';
import { HomeComponent } from '../home/<USER>';
import { ConducteurComponent } from '../conducteur/conducteur.component';
import { CamionComponent } from '../camion/camion.component';
import { DeconnexionComponent } from '../deconnexion/deconnexion.component';
import { ListConducteurAdminComponent } from '../list-conducteur-admin/list-conducteur-admin.component';
import { ListCamionAdminComponent } from '../list-camion-admin/list-camion-admin.component';
import { ProfileexpediteurComponent } from '../profileexpediteur/profileexpediteur.component';
import { ActivationComponent } from '../content-pages/activation/activation.component';
import { ReclamationComponent } from 'app/reclamation/reclamation.component';
import { GFactureComponent } from 'app/g-facture/g-facture.component';
import { ListFactureComponent } from '../list-facture/list-facture.component';
import { ReactiveFormsModule } from '@angular/forms';
import { ParametrageComponent } from '../parametrage/parametrage.component';
import { SavedModeleComponent } from '../saved-modele/saved-modele.component';
import { CommALivreComponent } from '../comm-a-livre/comm-a-livre.component';
import { ExpeditionsComponent } from '../expeditions/expeditions.component';
import { LigneExpedierComponent } from '../ligne-expedier/ligne-expedier.component';
import { LineDelivredComponent } from '../line-delivred/line-delivred.component';
import { ModifyCmdComponent } from '../modify-cmd/modify-cmd.component';
import { GestionDestinationComponent } from '../gestion-destination/gestion-destination.component';
import { AddUserComponent } from '../add-user/add-user.component';
import { AuthGuard } from 'app/shared/auth/auth-guard.service';
import { VerificationListComponent } from '../verification-list/verification-list.component';
import { AddInvoiceComponent } from '../add-invoice/add-invoice.component';
import { ToBeInvoicedComponent } from '../to-be-invoiced/to-be-invoiced.component';
import { InspectionComponent } from '../inspection/inspection.component';
import { ProfileChefComponent } from '../profile-chef/profile-chef.component';
import { ListChefCommandeComponent } from '../list-chef-commande/list-chef-commande.component';
import { HangarListComponent } from '../hangar-list/hangar-list.component';
import { GsListComponent } from '../gs-list/gs-list.component';
import { CommandeByDestinationComponent } from '../commande-by-destination/commande-by-destination.component';
import { PdfUploadComponent } from '../pdf-upload/pdf-upload.component';
import { ColisageListComponent } from '../colisage-list/colisage-list.component';
import { GFacturefluxComponent } from 'app/g-factureflux/g-factureflux.component';
import { VolumeNotUpdatedComponent } from '../volume-not-updated/volume-not-updated.component';
import { AdjustlinesComponent } from '../adjustlines/adjustlines.component';
import { environment } from 'environments/environment';
import { BarcodeReaderComponent } from '../barcode-reader/barcode-reader.component';
import { ShipmentComponent } from '../shipment/shipment.component';
import { CalendarsComponent } from 'app/calendar/calendar.component';
import { RecpetionComponent } from '../recpetion/recpetion.component';
import { ManagingComponent } from '../managing/managing.component';

const routes: Routes = [   
  {
    path: '',
    children: [

      {
        path: 'activation/:cle',
        component: ActivationComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Activation page'
        }
      },

      {
        path: 'profilexpediteur',
        component: ProfileexpediteurComponent ,
        canActivate: [AuthGuard],
        data: {
          title: 'profilexpediteur page',

        }
      },
      {
        path: 'listconducteuradmin',
        component: ListConducteurAdminComponent ,
        data: {
          title: 'list-conducteur-admin page',
          expectedRole: ['SuperAdmin','Administrateur'] // Spécifiez les rôles attendus ici

        }
      },

      {
        path: 'listcamionadmin',
        component: ListCamionAdminComponent ,
        data: {
          title: 'list-camion-page page',
          expectedRole: ['SuperAdmin','Administrateur'] // Spécifiez les rôles attendus ici

        }
      },

      {
        path: 'deconnexion',
        component: DeconnexionComponent ,
        data: {
          title: 'Camion page'
        }
      },

      

      {
        path: 'camion',
        component: CamionComponent ,
        canActivate: [AuthGuard],

        data: {
          title: 'Camion page',
          expectedRole: ['SuperAdmin'] // Spécifiez les rôles attendus ici

        }
      },

      {
        path: 'conducteur',
        component: ConducteurComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Conducteur page',
          expectedRole: ['SuperAdmin'] // Spécifiez les rôles attendus ici

        }
      },
      
      {
        path: 'home',
        component:HomeComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Home page',
          

        }
      },
      {
        path: 'MesCommRes',
        component:MesCommResComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Commande-reservees page',
          expectedRole: ['Client','Chef Departement','GS','GE',"Depot"] // Spécifiez les rôles attendus ici

        }
      },

      {
        path: 'Ajou-Commande',
        component:AjouCommComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Ajou-commande page',
          expectedRole: ['Client','Chef Departement','GS','GE',"Depot"] // Spécifiez les rôles attendus ici

        } } ,

      {
        path: 'Ajout-Commande',
        component:AjouCommComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Ajout-commande page',
          expectedRole: ['Client','Chef Departement','GS','GE',"Depot"] // Spécifiez les rôles attendus ici

        }
      },

      {
        path: 'listuserTr',
        component:ListUserTrComponent,
        canActivate: [AuthGuard],

       // canActivate: [AuthGuard],
        data: {
          title: 'list-Transporteur page',
          expectedRole: ['SuperAdmin','Administrateur'] // Spécifiez les rôles attendus ici

        }
      },
      {
        path: 'listCommReserves',
        component:MesReservationsComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'list-commande-reservees-tr page',
          expectedRole: ['Client','Chef Departement','GS',"Depot"] // Spécifiez les rôles attendus ici

          
        }
      },
     
      {
        path: 'listCommAvalExp',
        component:ListCommAValiderExpComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'list-commande-valide-exp page',
          expectedRole: ['Client','Chef Departement','GS','GE',"Depot"] // Spécifiez les rôles attendus ici

        }
      },
      
      {
        path: 'listCommValExp',
        component:ListCommValidExpComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'list-commande-valide-exp page',
          expectedRole: ['Client','Chef Departement','GS','GE',"Depot"] // Spécifiez les rôles attendus ici

        }
      },

      {
        path: 'listCommandeExp',
        component:ListCommandeExpComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'list-commande-exp page',
          expectedRole: ['Client','Chef Departement','GS','GE',"Depot"] // Spécifiez les rôles attendus ici

        }
      },
      {
        path: 'listCommandeAdmin',
        component:ListCommandeAdminComponent,
        
        canActivate: [AuthGuard],

        data: {
          title: 'list-commande-admin page'
        }
      },

      {
        path: 'listcommande',
        component:ListCommandeComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'list-commande page',
          expectedRole: ['SuperAdmin','Administrateur'] // Spécifiez les rôles attendus ici

        }
      },

      {
        path: 'welcomecommande',
        component:WelcomCommandeComponent,
        data: {
          title: 'welcom-commande page'
        }
      },

      {
        path: 'listcamion',
        component:ListCamionComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'list-camion page'
        }
      },

      {
        path: 'welcomcamion',
        component:WelcomeCamionComponent,
        data: {
          title: 'list-camion page'
        }
      },

      {
        path: 'welcomconducteur',
        component:WelcomConducteurComponent,
        data: {
          title: 'welcom-conducteur page'
        }
      },

      {
        path: 'listconducteur',
        component:ListConducteurComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'List-conducteur page'
        }
      },
   
      {
        path: 'profiletransporteur',
        component:ProfiletransporteurComponent ,
        
        data: {
          title: 'Profile TR page'
        }
      },  

        

      {
        path: 'listuserExp',
        component:ListUserComponent ,
        canActivate: [AuthGuard],

        data: {
          title: 'List-User page',
          expectedRole: ['SuperAdmin'] // Spécifiez les rôles attendus ici

        }
      },
   
      {
        path: 'profile',
        component: UserProfilePageComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'User Profile Page'
        }
      },
      {
        path: 'search',
        component: SearchComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Search'
        }
      },
      {
        path: 'reclamation',
        component: ReclamationComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Reclamation'
        }
      } ,
      {
        path: 'gfacture',
        component:GFactureComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'gfacture',
          expectedRole: ['FACTURATION','SuperAdmin'] // Spécifiez les rôles attendus ici

        }
      } 
      ,
      {
        path: 'listfacture',
        component:ListFactureComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Liste Facture',
          expectedRole: ['SuperAdmin','FACTURATION'] // Spécifiez les rôles attendus ici

        }
      },

      {
        path: 'parametrage',
        component: ParametrageComponent,
         canActivate: [AuthGuard],
        data: {
          title: 'Parametrage',
          expectedRole: ['SuperAdmin'] // Spécifiez les rôles attendus ici
        }
      },

      {
        path: 'addFacture',
        component: AddInvoiceComponent,
         canActivate: [AuthGuard],
        data: {
          title: 'Parametrage',
          expectedRole: ['SuperAdmin','Gestionnaire','FACTURATION'] // Spécifiez les rôles attendus ici
        }
      }
       ,
      {
        path: 'modifier-cmd',
        component:ModifyCmdComponent,
        data: {
          title: 'Modifier Commande',
          expectedRole: ['Client','Chef Departement','GS','GE',"Depot"] // Spécifiez les rôles attendus ici

        }
      }  ,

      {
        path: 'modele',
        component:SavedModeleComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Modele Enregistrer',
          expectedRole: ['Client','Chef Departement','GS','GE'] // Spécifiez les rôles attendus ici

        }
      }  
      ,

      {
        path: 'comm-a-livre',
        component:CommALivreComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Commande à livrer',
          expectedRole: ['SuperAdmin','Administrateur'] // Spécifiez les rôles attendus ici

        }
      } ,

      {
        path: 'expedition',
        component:ExpeditionsComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Commande à Expédier',
          expectedRole: ['SuperAdmin','Administrateur'] // Spécifiez les rôles attendus ici

        }
      },

      {
        path: 'ligne-expedier',
        component:LigneExpedierComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Mes Lignes Expédier',
          expectedRole: ['Client','Chef Departement','GS','GE',"Depot"] // Spécifiez les rôles attendus ici

        }
      },
      {
        path: 'line-delivred',
        component:LineDelivredComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Mes Lignes Livré',
          expectedRole: ['Client','Chef Departement','GS','GE',"Depot"] // Spécifiez les rôles attendus ici

        }
      },
      {
        path: 'gestion-dest',
        component:GestionDestinationComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Mes Lignes Livré',
          expectedRole: ['SuperAdmin'] // Spécifiez les rôles attendus ici

        }
      },

      {
        path: 'addUser',
        component:AddUserComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Ajouter Utilisteur',
          expectedRole: ['SuperAdmin'] // Spécifiez les rôles attendus ici

        }
      },

      {
        path: 'verif',
        component:VerificationListComponent,
        canActivate: [AuthGuard],

        data: {
          title: 'Liste Verification',
          expectedRole: ['SuperAdmin','Client','Administrateur','Chef Departement',"Inspection","Depot",'GS','GE','FACTURATION'] // Spécifiez les rôles attendus ici
        }
      },
      {
        path: 'VerifyFact',
        component:ToBeInvoicedComponent,
        canActivate: [AuthGuard],


        data: {
          title: 'Ligne à Facturer',
          expectedRole: ['SuperAdmin','Administrateur','FACTURATION'] // Spécifiez les rôles attendus ici

        }
      },

      {
        path: 'inspection',
        component: InspectionComponent,
         canActivate: [AuthGuard],
        data: {
          title: 'Inspection',
          expectedRole: ['SuperAdmin','Inspection','Administrateur','FACTURATION'] // Spécifiez les rôles attendus ici
        }
      },

      {
        path: 'profileChef',
        component: ProfileChefComponent,
         canActivate: [AuthGuard],
        data: {
          expectedRole: ['Chef Departement'] // Spécifiez les rôles attendus ici
        }
      },
      {
        path: 'listchefcommande',
        component: ListChefCommandeComponent,
         canActivate: [AuthGuard],
        data: {
          expectedRole: ['Chef Departement'] // Spécifiez les rôles attendus ici
        }
      },
      {
        path: 'HangarList',
        component: HangarListComponent,
         canActivate: [AuthGuard],
        data: {
          expectedRole: ['Depot','Administrateur'] // Spécifiez les rôles attendus ici
        }
      },
      {
        path: environment.urlChat,
       // component: ChatComponent,
         canActivate: [AuthGuard],
        data: {
          expectedRole: ['Chef Departement','SuperAdmin','Inspection','Administrateur','Client','GS','GE',"Depot"] // Spécifiez les rôles attendus ici
        }
      },
      {
        path: 'gs',
        component: GsListComponent,
         canActivate: [AuthGuard],
        data: {
          title: 'Gestion CMD Magasin',
          expectedRole: ['GS'] // Spécifiez les rôles attendus ici
        }
      },

      {
        path: 'centre',
        component: CommandeByDestinationComponent,
         canActivate: [AuthGuard],
        data: {
          title: 'Suivi Reçeption / Livraison ',
          expectedRole: ['GS','Chef Departement','Depot','Client','MAGASIN'] // Spécifiez les rôles attendus ici
        }
      },

      {
        path: 'pdf-upload',
        component: PdfUploadComponent,
         canActivate: [AuthGuard],
        data: {
          title: 'pdf-upload ',
          expectedRole: ['SuperAdmin','Depot','Inspection','Administrateur',"GS",'Chef Departement',] // Spécifiez les rôles attendus ici
        }
      },{
        path: 'colisageList',
        component: ColisageListComponent,
         canActivate: [AuthGuard],
        data: {
          title: 'Liste Colisage',
          expectedRole: ['SuperAdmin','Depot','Inspection','Administrateur'] // Spécifiez les rôles attendus ici
        }
      }, 
      {
        path: 'generateFluxInvoice',
        component: GFacturefluxComponent,
         canActivate: [AuthGuard],
        data: {
          title: 'Facturation Flux / Entreposage',
          expectedRole: ['FACTURATION',] // Spécifiez les rôles attendus ici
        }
      },
      {
        path: 'pdfNotUploaded',
        component: VolumeNotUpdatedComponent,
         canActivate: [AuthGuard],
        data: {
          title: 'Volume Absent',
          expectedRole: ['SuperAdmin','Administrateur','Inspection','Depot'] // Spécifiez les rôles attendus ici
        }
      }, 
      {
        path: 'AssignLines',
        component: AdjustlinesComponent,
         canActivate: [AuthGuard],
        data: {
          title: 'Ajuster Lignes CMD',
          expectedRole: ['SuperAdmin','Administrateur'] 
        }
      },
      {
        path: 'barcodeReader',
        component:BarcodeReaderComponent,
        data: {
          title: 'Lecteur CAB Colis',
          expectedRole: ["Depot",'MAGASIN'] // Spécifiez les rôles attendus ici

        }
      } ,
      {
        path: 'inspectColis',
        component:ShipmentComponent,
        data: {
          title: 'Inspection Colis',
          expectedRole: ['SuperAdmin','Administrateur','Inspection'] // Spécifiez les rôles attendus ici

        }
      } ,
      {
        path: 'calendar',
        component:CalendarsComponent,
        data: {
          title: 'Suivi des Colis par Date de Création',
          expectedRole: ['SuperAdmin','Administrateur','Inspection'] 

        }
      },
      {
        path: 'reception',
        component:RecpetionComponent,
        data: {
          title: 'Suivi des Colis par Date de Création',
          expectedRole: ['GS','Chef Departement','Depot','Client','MAGASIN']
        }
      }, 
      {
        path: 'managing',
        component: ManagingComponent,
         canActivate: [AuthGuard],
        data: {
          title: 'Gerer Voyage',
          expectedRole: ['FACTURATION',] // Spécifiez les rôles attendus ici
        }
      },





      // {
      //   path: 'gestinspec',
      //   component: InspectionVerificationComponent,
      //    canActivate: [AuthGuard],
      //   data: {
      //     title: 'Gestion Inspection',
      //     expectedRole: ['Inspection'] // Spécifiez les rôles attendus ici
      //   }
      // },
      
      

       
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes),ReactiveFormsModule],
  exports: [RouterModule],
})
export class FullPagesRoutingModule { }


