const Customer = require ("../models/customer.model.js") ;
require('dotenv').config();
multer = require('multer'),
  bodyParser = require('body-parser');
  const PATH = './uploads';

  // Create a Customer
  var storage = multer.diskStorage({
    destination: function (req, file, cb) {
      cb(null, 'uploads/')
    },
    filename: function (req, file, cb) {
      let originalname = file.originalname;
      let ext = originalname.split('.').pop();
      let filename = originalname.split('.').slice(0, -1).join('.');
  
      cb(null,originalname)
    }
  })
   
  exports.upload = multer({ storage: storage })
   
  exports.create = (req, res)=>
 { 
   if(!req.body){
     res.status(400).send({
       message: "Content can not beempty"
     })
   }
  
 
  const customer = new Customer({
  //id: req.body.id,
   sexe: req.body.sexe,
   nom: req.body.nom,
   prenom: req.body.prenom,
   cin: req. body.cin,
   image_cin: req.body.image_cin, 
   type_utilisateur: req.body.type_utilisateur,
   forme_juridique : req.body.forme_juridique,
   raison_sociale : req.body.raison_sociale,
   num_tva : req.body.num_tva,
   email : req.body.email,
   adresse : req.body.adresse,
   mobile : req.body.mobile,
   nom_utilisateur: req.body.nom_utilisateur,
   mot_de_passe : req.body.mot_de_passe, 
   statut: req.body.statut, 
   cle_activation: req.body.cle_activation, 
   client_direct : req.body.client_direct, 

  });


  Customer.create(customer, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while creating the Customer."
      });
    else res.send(data);
  });
}




// Retrieve all Customers from the database.
exports.findAll = (req, res) => {
  Customer.getAll((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving customer."
      });
    else res.send(data);
  });
 
};


// Find a single Customer with a customerId
exports.findOne = (req, res) => {

  Customer.findById(req.params.customerId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Customer with id ${req.params.customerId}.`
        });
      } else {
        res.status(500).send({
          message: "Error retrieving Customer with id " + req.params.customerId
        });
      }
    } else res.send(data);
  });
};

// Update a Customer identified by the customerId in the request
exports.update = (req, res) => {
  // Validate Request
 if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    });
  }



  Customer.updateById(
    req.params.customerId,
    new Customer(req.body),
    (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found Customer with id ${req.params.customerId}.`
          });
        } else {
          res.status(500).send({
            message: "Error updating Customer with id " + req.params.customerId
          });
        }
      } else res.send(data);
    }
  );

};

// Delete a Customer with the specified customerId in the request
exports.delete = (req, res) => {
  Customer.remove(req.params.customerId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Customer with id ${req.params.customerId}.`
        });
      } else {
        res.status(500).send({
          message: "Could not delete Customer with id " + req.params.customerId
        });
      }
    } else res.send({ message: `Customer was deleted successfully!` });
  });
}; 

// Delete all Customers from the database.
exports.deleteAll = (req, res) => {
  Customer.removeAll((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while removing all customers."
      });
    else res.send({ message: `All Customers were deleted successfully!` });
  });
};

//envoi d'un e-mail 
// exports.sendEmail = (req, res) => {
//   console.log(process.env.SENDGRID_API_KEY)
//   if (!req.body.email || !req.body.content ) {

//     res.status(400).send({
//       message: "Content can not be empty!"
//     });
//   }
//   else
//   {
//     console.log('rrrr')
//   const sgMail = require('@sendgrid/mail')
//   sgMail.setApiKey(process.env.SENDGRID_API_KEY) ; 
  
//   const msg = {
//     to: req.body.email, 
//     from: {email : process.env.EMAIL, name : process.env.NAME }, 
//     subject: 'activation ',
//     html: req.body.content,
//   }
  

  
//   sgMail
//     .send(msg)
//     .then(() => {
//       console.log('Email sent')
//       res.send({message: 'Email sent'})
//     })
//     .catch((error) => {
//       console.error(error)
//       res.send({message: error})
//     })}


// };


const axios = require('axios');

exports.sendEmail = async (req, res) => {
  try {
    const url = 'https://api.sendinblue.com/v3/smtp/email';
    const apiKey = '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';

    if ((!req.body.email || !req.body.content)) {
      return res.status(400).send({
        message: 'Content can not be empty!',
      });
    }

    const headers = {
      'Content-Type': 'application/json',
      'api-key': apiKey,
    };

    const emailData = {
      to: [{ email: req.body.email }],
      sender: {
        email: process.env.EMAIL,
        name: process.env.NAME,
      },
      subject: 'activation',
      htmlContent: req.body.content,
    };

    const response = await axios.post(url, emailData, { headers });
    console.log('Sendinblue API called successfully. Returned data: ', response.data);
    res.send({ message: 'Email sent' });
  } catch (error) {
    console.error('Error while calling Sendinblue API: ', error);
    res.status(500).send({ message: error.message || 'Internal Server Error' });
  }
};









 // Find a single Customer with  customerCleActivation
 exports.findByCles = (req, res) => {
     Customer.findByCle(req.params.customerCleActivation, (err, data) => {
        if (err) {
          if (err.kind === "not_found") {
            res.status(404).send({
              message: `Not found Customer with CleActivation ${req.params.customerCleActivation}.`
            });
          } else {
            res.status(500).send({
              
              message: "Error retrieving Customer with CleActivation " + req.params.customerCleActivation

            });
          }
        } else res.send(data);
      }); 
   
  }; 
  


// Update a Customer by the customercle in the request
exports.updateByCles = (req, res) => {
  
  // Validate Request
 if (!req.body) {


    res.status(400).send({
      message: "Content can not be empty!"
    }); 
  } 


  Customer.updateByCle(req.params.customerCleActivation,
    new Customer(req.body),
    (err, data) =>
     {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found Customer with cle  ${req.params.customerCleActivation}.`
          });
        } else {
          res.status(500).send({
            message: "Error updating Customer with cle " + req.params.customerCleActivation
          });
        }
      } else res.send(data); 
    } 
  );
};  

// connexion : 

exports.findByAuth = (req, res) => {

  Customer.findAuth(req.body.email, req.body.mot_de_passe, (err, data) => {
    if (err) {
      console.log(err);
      if (err.kind === "not_found") {
        res.send({
          message: `Not found Customer with user name and password ${req.body.email} ${req.body.mot_de_passe}.`
        });
      } else {
        res.send({
          message: "Error retrieving Customer with user name" + req.params.customerNu + " and password " + req.params.customerMdp
        });
      }
    } else {
      res.send(data);
    }
  });
  

};

 // Update a Customer by the CommandeId 
 exports.updateByIdUser = (req, res) => {

 if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    }); 
  } 
  Customer.updateCustId(req.params.customerId,
    new Customer(req.body),
    (err, data) =>
     {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found customer with Id  ${req.params.customerId}.`
          });
        } else {
          res.status(500).send({
            message: "Error updating customer with Id " + req.params.customerId
          });
        }
      } else res.send(data); 
    } 
  );
}; 
  

// Retrieve all Transporteur from the database.
exports.findAllTr = (req, res) => {
  Customer.getAllTr((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving customer."
      });
    else res.send(data);
  });
 
};


// Retrieve all Expediteur from the database.
exports.findAllExp= (req, res) => {
  Customer.getAllExp((err, data) => {
    
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving customer."
      });
    else {
     // var bufferBase64 = new Buffer( data[0].image_cin, 'binary' ).toString('base64');
      //let v=base64_encode(data[0].image_cin) ;
    //  data[0].image_cin=bufferBase64;
      res.send(data);}
  });
 
};




// Update a Customer by the customerId in the request
exports.upCustomerTr= (req, res) => {
  
  // Validate Request
 if (!req.body) {


    res.status(400).send({
      message: "Content can not be empty!"
    }); 
  } 
 // else //res.send('tout va bien')


  Customer.upCustTr(req.params.customerId,
    new Customer(req.body),
    (err, data) =>
     {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found Customer with id  ${req.params.customerId}.`
          });
        } else {
          res.status(500).send({
            message: "Error updating Customer with cle " + req.params.customerId
          });
        }
      } else res.send(data); 
    } 
  );
}; 




exports.lastConnexion = (req, res) => {
  if (!req.params.idUser) {
    return res.status(400).send({
      message: "L'ID utilisateur est requis!"
    });
  }

  // Appel à la fonction Customer.lastConnexion avec l'ID utilisateur
  Customer.lastConnexion(req.params.idUser, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).send({
          message: `Utilisateur non trouvé avec l'ID ${req.params.idUser}.`
        });
      } else {
        return res.status(500).send({
          message: `Erreur lors de la mise à jour de l'utilisateur avec l'ID ${req.params.idUser}.`
        });
      }
    } 

    // Envoyer les données mises à jour (l'heure de connexion)
    res.send(data);
  });
};




exports.getAllEntrprise = (req, res) => {
  Customer.getAllEntrprise((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving entreprise."
      });
    else res.send(data);
  });

};
exports.setClient = (req, res) => {
  // Validate Request
 if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    });
  }

 
  Customer.setClient(
    req.body ,
    (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found Customer `
          });
        } else {
          res.status(500).send({
            message: "Error setting client"
          });
        }
      } else res.send(data);
    }
  );
};
exports.removeClient = (req, res) => {
  // Validate Request
 if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    });
  }

 
  Customer.removeClient(
    req.body ,
    (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found Customer `
          });
        } else {
          res.status(500).send({
            message: "Error setting client"
          });
        }
      } else res.send(data);
    }
  );
};



exports.disabledClient = (req, res) => {
  // Validate Request
  const customerId = req.params.customerId;
  if (!customerId) {
    res.status(400).send({
      message: "Customer ID cannot be empty!"
    });
    return;
  }

  Customer.disabledClient(
    { idClient: customerId },
    (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found Customer`
          });
        } else {
          res.status(500).send({
            message: "Error setting client"
          });
        }
      } else res.send(data);
    }
  );
};



exports.getAllClients = (req, res) => {
  Customer.getAllClients((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving clients."
      });
    else res.send(data);
  });

};
exports.getClientByExp = (req, res) => {
  // Validate Request
 if (!req.body) {


    res.status(400).send({
      message: "Content can not be empty!"
    }); 
  } 
 // else //res.send('tout va bien')

 
  Customer.getClientByExp(req.params.id,
    
    (err, data) =>
     {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found Customer with id  ${req.params.id}.`
          });
        } else {
          res.status(500).send({
            message: "Error updating Customer with id " + req.params.id
          });
        }
      } else res.send(data); 
    } 
  );
}; 

exports.findOneByEmail = (req, res) => {
  const email = req.params.email;

  Customer.findByEmail(email, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(204).send({
          message: `Utilisateur non trouvé avec l'e-mail ${email}.`
        });
      } else {
        res.status(500).send({
          message: "Erreur lors de la recherche de l'utilisateur avec l'e-mail " + email
        });
      }
    } else res.send(data);
  });
};


exports.findAllClient= (req, res) => {
  Customer.findAllClient((err, data) => {
    
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving customer."
      });
    else {
     // var bufferBase64 = new Buffer( data[0].image_cin, 'binary' ).toString('base64');
      //let v=base64_encode(data[0].image_cin) ;
    //  data[0].image_cin=bufferBase64;
      res.send(data);}
  });
 
};


exports.findAllChef= (req, res) => {
  Customer.findAllChef((err, data) => {
    
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving customer."
      });
    else {
     // var bufferBase64 = new Buffer( data[0].image_cin, 'binary' ).toString('base64');
      //let v=base64_encode(data[0].image_cin) ;
    //  data[0].image_cin=bufferBase64;
//console.log(da)
      res.send(data);}
  });
 
};


exports.createChefUser = (req, res) => {
  const id_chef = req.body.id_chef;
  const id_users = req.body.id_users;

  Customer.createChefUser(id_chef, id_users, (err, result) => {
    if (err) {
      return res.status(500).send({
        message: err.message || "Some error occurred while creating chef_user entries."
      });
    }
    return res.send({ message: result });
  });
};


exports.findUsersByChef = (req, res) => {
  const chefId = req.params.chefId; // Utilisez req.params.chefId pour récupérer l'ID du chef de département

  Customer.findUsersByChef(chefId, (err, data) => { // Utilisez chefId pour passer l'ID du chef de département à la fonction
    if (err) {
      if (err.kind === "not_found") {
        res.status(204).send({
          message: `Utilisateur non trouvé avec l'ID du chef ${chefId}.`
        });
      } else {
        res.status(500).send({
          message: "Erreur lors de la recherche de l'utilisateur avec l'ID du chef " + chefId
        });
      }
    } else res.send(data);
  });
};


exports.updateUserById = (req, res) => {

 if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    }); 
  } 

  Customer.updateUserById(req.params.id,
    new Customer(req.body),
    (err, data) =>
     {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found customer with Id  ${req.params.id}.`
          });
        } else {
          res.status(500).send({
            message: "Error updating customer with Id " + req.params.id
          });
        }
      } else res.send(data); 
    } 
  );
}; 


// Met à jour la couleur du client
exports.updateClientColor = (req, res) => {
  if (!req.body || !req.body.color) {
    return res.status(400).send({
      message: "La couleur ne peut pas être vide !"
    });
  }

  Customer.updateClientColor(
    req.params.customerId,
    req.body.color,
    (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          return res.status(404).send({
            message: `Client avec l'ID ${req.params.customerId} introuvable.`
          });
        } else {
          return res.status(500).send({
            message: "Erreur lors de la mise à jour de la couleur du client avec l'ID " + req.params.customerId
          });
        }
      }

      res.send(data);
    }
  );
};
