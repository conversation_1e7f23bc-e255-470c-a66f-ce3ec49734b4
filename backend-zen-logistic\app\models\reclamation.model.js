const sql = require("./db.js");

// constructor

const Reclamation = function(reclamation) {
 // this.id=customer.id ;
  this.id_commande  = reclamation.id_commande ;
  this.id_conducteur  = reclamation.id_conducteur ;
  this.id_transporteur  = reclamation.id_transporteur ;
  this.id_expediteur  = reclamation.id_expediteur ;
  this.type = reclamation.type; 
  this.image = reclamation.image;
   

} ;
Reclamation.create = (newReclamation, result) => {
    sql.query("INSERT INTO reclamation SET ?", newReclamation, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      //console.log("created reclamation: ", { id_reclamation: res.insertId, ...newReclamation });
      result(null, { id_reclamation: res.insertId, ...newReclamation });
    });
  };
  
  Reclamation.findByTransporteur = (Transporteur, result) => {
    sql.query(`SELECT R.id_reclamation,CM.code as commande ,CONCAT( U.prenom,' ', U.nom ) as transporteur,CONCAT( UE.prenom,' ', UE.nom ) as expediteur ,CONCAT( C.prenom,' ', C.nom ) as conducteur ,R.type,R.image FROM reclamation R LEFT join utilisateurs U on R.id_transporteur=U.id LEFT join utilisateurs UE on R.id_expediteur=UE.id LEFT join conducteur C on R.id_conducteur=C.id LEFT join commande CM on R.id_commande=CM.id where R.id_transporteur = ${Transporteur} `, (err, res) => {
      //console.log(`SELECT * FROM reclamation WHERE id_transporteur = ${Transporteur}`)
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      if (res.length) {
        //console.log("found Region: ", res);
        result(null, res);
        return;
      }









  
      // not found Region with the ville_id
      result({ kind: "not_found" }, null);
    });
  }; 
  
  Reclamation.findByExpediteur = (expediteur, result) => {
    sql.query(`SELECT R.id_reclamation,CM.code as commande ,CONCAT( U.prenom,' ', U.nom ) as transporteur,CONCAT( UE.prenom,' ', UE.nom ) as expediteur ,CONCAT( C.prenom,' ', C.nom ) as conducteur ,R.type,R.image FROM reclamation R LEFT join utilisateurs U on R.id_transporteur=U.id LEFT join utilisateurs UE on R.id_expediteur=UE.id LEFT join conducteur C on R.id_conducteur=C.id LEFT join commande CM on R.id_commande=CM.id where R.id_expediteur = ${expediteur} `, (err, res) => {
      //console.log(`SELECT R.id_reclamation,CM.code as commande ,CONCAT( U.prenom,' ', U.nom ) as transporteur,CONCAT( UE.prenom,' ', UE.nom ) as expediteur ,CONCAT( C.prenom,' ', C.nom ) as conducteur ,R.type,R.image FROM reclamation R LEFT join utilisateurs U on R.id_transporteur=U.id LEFT join utilisateurs UE on R.id_expediteur=UE.id LEFT join conducteur C on R.id_conducteur=C.id LEFT join commande CM on R.id_commande=CM.id where R.id_expediteur = ${expediteur} `)
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      if (res.length) {
        //console.log("found Region: ", res);
        result(null, res);
        return;
      }


      result({ kind: "not_found" }, null);
    });
  }; 
  
  Reclamation.findAll = (result) => {
    sql.query(`SELECT R.id_reclamation,CM.code as commande ,CONCAT( U.prenom,' ', U.nom ) as transporteur,CONCAT( UE.prenom,' ', UE.nom ) as expediteur ,CONCAT( C.prenom,' ', C.nom ) as conducteur ,R.type,R.image FROM reclamation R LEFT join utilisateurs U on R.id_transporteur=U.id LEFT join utilisateurs UE on R.id_expediteur=UE.id LEFT join conducteur C on R.id_conducteur=C.id LEFT join commande CM on R.id_commande=CM.id  `, (err, res) => {
    
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      if (res.length) {
      
        result(null, res);
        return;
      }









  
      // not found Region with the ville_id
      result({ kind: "not_found" }, null);
    });
  }; 
  module.exports = Reclamation; 