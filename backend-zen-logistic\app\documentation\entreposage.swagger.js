/**
 * @swagger
 * components:
 *   schemas:
 *     Entreposage:
 *       type: object
 *       required:
 *         - id_ste
 *         - nom_societe
 *         - nom_depot
 *         - date
 *         - qte
 *       properties:
 *         id:
 *           type: integer
 *           description: ID de l'enregistrement
 *           example: 1
 *         id_ste:
 *           type: integer
 *           description: ID de la société
 *           example: 101
 *         nom_societe:
 *           type: string
 *           description: Nom de la société
 *           example: "Société ABC"
 *         nom_depot:
 *           type: string
 *           description: Nom du dépôt
 *           example: "Dépôt Central"
 *         date:
 *           type: string
 *           format: date
 *           description: Date de l'entreposage
 *           example: "2024-05-26"
 *         qte:
 *           type: number
 *           description: Quantité entreposée
 *           example: 150.5
 *         id_facture:
 *           type: integer
 *           nullable: true
 *           description: ID de la facture liée (peut être null)
 *           example: null
 */

/**
 * @swagger
 * tags:
 *   name: Entreposage
 *   description: API de gestion des entreposages
 */

/**
 * @swagger
 * /api/entreposage:
 *   get:
 *     summary: Récupérer tous les enregistrements d'entreposage
 *     tags: [Entreposage]
 *     responses:
 *       200:
 *         description: Liste des entreposages
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Entreposage'
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/entreposage:
 *   post:
 *     summary: Créer un nouvel enregistrement d'entreposage
 *     tags: [Entreposage]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Entreposage'
 *     responses:
 *       201:
 *         description: Enregistrement créé avec succès
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Entreposage'
 *       400:
 *         description: Requête invalide
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/entreposage/{id}:
 *   get:
 *     summary: Récupérer un entreposage par ID
 *     tags: [Entreposage]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: ID de l'enregistrement
 *     responses:
 *       200:
 *         description: Enregistrement trouvé
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Entreposage'
 *       404:
 *         description: Enregistrement non trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/entreposage/by-client-date:
 *   get:
 *     summary: Récupérer les entreposages par client et période
 *     tags: [Entreposage]
 *     parameters:
 *       - in: query
 *         name: id_ste
 *         schema:
 *           type: integer
 *         required: true
 *         description: ID de la société
 *       - in: query
 *         name: datedebut
 *         schema:
 *           type: string
 *           format: date
 *         required: true
 *         description: Date de début (format AAAA-MM-JJ)
 *       - in: query
 *         name: datefin
 *         schema:
 *           type: string
 *           format: date
 *         required: true
 *         description: Date de fin (format AAAA-MM-JJ)
 *     responses:
 *       200:
 *         description: Liste filtrée des entreposages
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Entreposage'
 *       400:
 *         description: Requête invalide
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/entreposage/by-facture/{id_facture}:
 *   get:
 *     summary: Récupérer les entreposages liés à une facture
 *     tags: [Entreposage]
 *     parameters:
 *       - in: path
 *         name: id_facture
 *         schema:
 *           type: integer
 *         required: true
 *         description: ID de la facture
 *     responses:
 *       200:
 *         description: Liste des entreposages liés à la facture
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Entreposage'
 *       404:
 *         description: Aucune donnée trouvée
 *       500:
 *         description: Erreur serveur
 */
