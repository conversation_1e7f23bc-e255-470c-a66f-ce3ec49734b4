const TypeCommande = require("../models/typeCmd.model.js");

exports.create = (req, res) => {
  //console.log('typeCmd');
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty"
    });
    return;
  }

  //console.log("fffffffffffff",req.body)
  const typeCmd = new TypeCommande({
    nom_type: req.body.nom_type,
  });

  TypeCommande.create(typeCmd, (err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Some error occurred while creating the typeCmdId."
      });
    } else {
      res.send(data);
    }
  });
};

exports.findAll = (req, res) => {
  TypeCommande.findAll((err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Some error occurred while retrieving typeCmdId."
      });
    } else {
      res.send(data);
    }
  });
};

exports.deletetypeCmd = (req, res) => {
  const typeCmdId = req.params.id; // Make sure this line is correct and provides a value

  TypeCommande.delete(typeCmdId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Marchandise with id ${typeCmdId} not found.`
        });
      } else {
        res.status(500).send({
          message: err.message || `Some error occurred while deleting the marchandise with id ${marchandiseId}.`
        });
      }
    } else {
      res.send({
        message: 'Marchandise deleted successfully.'
      });
    }
  });
};
