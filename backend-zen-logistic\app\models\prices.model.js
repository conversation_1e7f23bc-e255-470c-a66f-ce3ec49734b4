const sql = require("./db.js");


const Prices = function(prices) {
    this.id = prices.id;
    this.distance = prices.distance;
    this.prix_court_distance = prices.prix_court_distance;
    this.prix_long_distance = prices.prix_long_distance;
    this.type = prices.type;
  };

Prices.findAllPrices = result => {
    sql.query("SELECT * FROM prices", (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }
  
      //console.log("ville: ", res);
      result(null, res);
    });
  }


  Prices.updatePrice = (id, distance,prix_court,prix_long,montant,pourcentage,  result) => {
    sql.query("UPDATE prices SET distance = ?,prix_court_distance =?,prix_long_distance =?,montant=?,pourcentage=? WHERE id = ?", [distance,prix_court,prix_long,montant,pourcentage, id], (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      if (res.affectedRows === 0) {
        // Aucun point de chargement trouvé avec l'id spécifié
        result({ kind: "not_found" }, null);
        return;
      }
  
      //console.log("updated updatePrice: ");
      result(null,);
    });
  }



  module.exports = Prices ; 