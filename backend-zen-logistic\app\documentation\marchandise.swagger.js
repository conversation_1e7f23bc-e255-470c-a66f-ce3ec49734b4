/**
 * @swagger
 * tags:
 *   name: Marchandise
 *   description: Opérations liées à la gestion des marchandises
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Marchandise:
 *       type: object
 *       required:
 *         - nom_marchandise
 *       properties:
 *         id:
 *           type: integer
 *           description: Identifiant unique de la marchandise
 *         nom_marchandise:
 *           type: string
 *           description: Nom de la marchandise

 */

/**
 * @swagger
 * /api/marchandise:
 *   post:
 *     tags: [Marchandise]
 *     summary: Créer une nouvelle marchandise
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Marchandise'
 *     responses:
 *       201:
 *         description: Marchandise créée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Marchandise'
 *       400:
 *         description: Contenu vide ou données invalides
 *       500:
 *         description: Erreur interne du serveur

 *   get:
 *     tags: [Marchandise]
 *     summary: Récupérer toutes les marchandises
 *     responses:
 *       200:
 *         description: Liste des marchandises récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Marchandise'
 *       500:
 *         description: Erreur interne du serveur

 * /api/marchandise/{id}:
 *   delete:
 *     tags: [Marchandise]
 *     summary: Supprimer une marchandise par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID de la marchandise à supprimer
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Marchandise supprimée avec succès
 *       404:
 *         description: Marchandise non trouvée
 *       500:
 *         description: Erreur interne du serveur
 */
