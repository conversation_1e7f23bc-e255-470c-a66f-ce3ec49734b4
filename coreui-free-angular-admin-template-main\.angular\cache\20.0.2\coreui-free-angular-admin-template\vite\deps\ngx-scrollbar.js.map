{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/coercion.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/directionality-CChdj3az.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/bidi.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/scrolling-BkvA05C8.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/platform.mjs", "../../../../../../node_modules/ngx-scrollbar/fesm2022/ngx-scrollbar-smooth-scroll.mjs", "../../../../../../node_modules/ngx-scrollbar/fesm2022/ngx-scrollbar.mjs"], "sourcesContent": ["export { _ as _isNumberValue, a as coerceElement, c as coerceNumberProperty } from './element-x4z00URv.mjs';\nexport { c as coerceArray } from './array-I1yfCXUO.mjs';\nexport { c as coerceCssPixelValue } from './css-pixel-value-C_HEqLhI.mjs';\nimport '@angular/core';\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n    return value != null && `${value}` !== 'false';\n}\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\nfunction coerceStringArray(value, separator = /\\s+/) {\n    const result = [];\n    if (value != null) {\n        const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n        for (const sourceValue of sourceValues) {\n            const trimmedString = `${sourceValue}`.trim();\n            if (trimmedString) {\n                result.push(trimmedString);\n            }\n        }\n    }\n    return result;\n}\n\nexport { coerceBooleanProperty, coerceStringArray };\n\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, DOCUMENT, signal, EventEmitter, Injectable } from '@angular/core';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nconst DIR_DOCUMENT = new InjectionToken('cdk-dir-doc', {\n  providedIn: 'root',\n  factory: DIR_DOCUMENT_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction DIR_DOCUMENT_FACTORY() {\n  return inject(DOCUMENT);\n}\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\nfunction _resolveDirectionality(rawValue) {\n  const value = rawValue?.toLowerCase() || '';\n  if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n    return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n  }\n  return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\nclass Directionality {\n  /** The current 'ltr' or 'rtl' value. */\n  get value() {\n    return this.valueSignal();\n  }\n  /**\n   * The current 'ltr' or 'rtl' value.\n   */\n  valueSignal = signal('ltr');\n  /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n  change = new EventEmitter();\n  constructor() {\n    const _document = inject(DIR_DOCUMENT, {\n      optional: true\n    });\n    if (_document) {\n      const bodyDir = _document.body ? _document.body.dir : null;\n      const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n      this.valueSignal.set(_resolveDirectionality(bodyDir || htmlDir || 'ltr'));\n    }\n  }\n  ngOnDestroy() {\n    this.change.complete();\n  }\n  static ɵfac = function Directionality_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Directionality)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Directionality,\n    factory: Directionality.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Directionality, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { Directionality as D, _resolveDirectionality as _, DIR_DOCUMENT as a };\n", "import { _ as _resolveDirectionality, D as Directionality } from './directionality-CChdj3az.mjs';\nexport { a as DIR_DOCUMENT } from './directionality-CChdj3az.mjs';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, signal, Directive, Output, Input, NgModule } from '@angular/core';\n\n/**\n * Directive to listen for changes of direction of part of the DOM.\n *\n * Provides itself as Directionality such that descendant directives only need to ever inject\n * Directionality to get the closest direction.\n */\nclass Dir {\n  /** Whether the `value` has been set to its initial value. */\n  _isInitialized = false;\n  /** Direction as passed in by the consumer. */\n  _rawDir;\n  /** Event emitted when the direction changes. */\n  change = new EventEmitter();\n  /** @docs-private */\n  get dir() {\n    return this.valueSignal();\n  }\n  set dir(value) {\n    const previousValue = this.valueSignal();\n    // Note: `_resolveDirectionality` resolves the language based on the browser's language,\n    // whereas the browser does it based on the content of the element. Since doing so based\n    // on the content can be expensive, for now we're doing the simpler matching.\n    this.valueSignal.set(_resolveDirectionality(value));\n    this._rawDir = value;\n    if (previousValue !== this.valueSignal() && this._isInitialized) {\n      this.change.emit(this.valueSignal());\n    }\n  }\n  /** Current layout direction of the element. */\n  get value() {\n    return this.dir;\n  }\n  valueSignal = signal('ltr');\n  /** Initialize once default value has been set. */\n  ngAfterContentInit() {\n    this._isInitialized = true;\n  }\n  ngOnDestroy() {\n    this.change.complete();\n  }\n  static ɵfac = function Dir_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Dir)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: Dir,\n    selectors: [[\"\", \"dir\", \"\"]],\n    hostVars: 1,\n    hostBindings: function Dir_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"dir\", ctx._rawDir);\n      }\n    },\n    inputs: {\n      dir: \"dir\"\n    },\n    outputs: {\n      change: \"dirChange\"\n    },\n    exportAs: [\"dir\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: Directionality,\n      useExisting: Dir\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dir, [{\n    type: Directive,\n    args: [{\n      selector: '[dir]',\n      providers: [{\n        provide: Directionality,\n        useExisting: Dir\n      }],\n      host: {\n        '[attr.dir]': '_rawDir'\n      },\n      exportAs: 'dir'\n    }]\n  }], null, {\n    change: [{\n      type: Output,\n      args: ['dirChange']\n    }],\n    dir: [{\n      type: Input\n    }]\n  });\n})();\nclass BidiModule {\n  static ɵfac = function BidiModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BidiModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BidiModule,\n    imports: [Dir],\n    exports: [Dir]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BidiModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Dir],\n      exports: [Dir]\n    }]\n  }], null, null);\n})();\nexport { BidiModule, Dir, Directionality };\n", "/** The possible ways the browser may handle the horizontal scroll axis in RTL languages. */\nvar RtlScrollAxisType;\n(function (RtlScrollAxisType) {\n    /**\n     * scrollLeft is 0 when scrolled all the way left and (scrollWidth - clientWidth) when scrolled\n     * all the way right.\n     */\n    RtlScrollAxisType[RtlScrollAxisType[\"NORMAL\"] = 0] = \"NORMAL\";\n    /**\n     * scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n     * all the way right.\n     */\n    RtlScrollAxisType[RtlScrollAxisType[\"NEGATED\"] = 1] = \"NEGATED\";\n    /**\n     * scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and 0 when scrolled\n     * all the way right.\n     */\n    RtlScrollAxisType[RtlScrollAxisType[\"INVERTED\"] = 2] = \"INVERTED\";\n})(RtlScrollAxisType || (RtlScrollAxisType = {}));\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\nlet rtlScrollAxisType;\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\nlet scrollBehaviorSupported;\n/** Check whether the browser supports scroll behaviors. */\nfunction supportsScrollBehavior() {\n    if (scrollBehaviorSupported == null) {\n        // If we're not in the browser, it can't be supported. Also check for `Element`, because\n        // some projects stub out the global `document` during SSR which can throw us off.\n        if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n            scrollBehaviorSupported = false;\n            return scrollBehaviorSupported;\n        }\n        // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n        if ('scrollBehavior' in document.documentElement.style) {\n            scrollBehaviorSupported = true;\n        }\n        else {\n            // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n            // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n            const scrollToFunction = Element.prototype.scrollTo;\n            if (scrollToFunction) {\n                // We can detect if the function has been polyfilled by calling `toString` on it. Native\n                // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n                // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n                // polyfilled functions as supporting scroll behavior.\n                scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n            }\n            else {\n                scrollBehaviorSupported = false;\n            }\n        }\n    }\n    return scrollBehaviorSupported;\n}\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\nfunction getRtlScrollAxisType() {\n    // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n    if (typeof document !== 'object' || !document) {\n        return RtlScrollAxisType.NORMAL;\n    }\n    if (rtlScrollAxisType == null) {\n        // Create a 1px wide scrolling container and a 2px wide content element.\n        const scrollContainer = document.createElement('div');\n        const containerStyle = scrollContainer.style;\n        scrollContainer.dir = 'rtl';\n        containerStyle.width = '1px';\n        containerStyle.overflow = 'auto';\n        containerStyle.visibility = 'hidden';\n        containerStyle.pointerEvents = 'none';\n        containerStyle.position = 'absolute';\n        const content = document.createElement('div');\n        const contentStyle = content.style;\n        contentStyle.width = '2px';\n        contentStyle.height = '1px';\n        scrollContainer.appendChild(content);\n        document.body.appendChild(scrollContainer);\n        rtlScrollAxisType = RtlScrollAxisType.NORMAL;\n        // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n        // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n        // dealing with one of the other two types of browsers.\n        if (scrollContainer.scrollLeft === 0) {\n            // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n            // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n            // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n            // return 0 when we read it again.\n            scrollContainer.scrollLeft = 1;\n            rtlScrollAxisType =\n                scrollContainer.scrollLeft === 0 ? RtlScrollAxisType.NEGATED : RtlScrollAxisType.INVERTED;\n        }\n        scrollContainer.remove();\n    }\n    return rtlScrollAxisType;\n}\n\nexport { RtlScrollAxisType as R, getRtlScrollAxisType as g, supportsScrollBehavior as s };\n\n", "export { P as Platform } from './platform-DNDzkVcI.mjs';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nexport { n as normalizePassiveListenerOptions, s as supportsPassiveEventListeners } from './passive-listeners-esHZRgIN.mjs';\nexport { R as RtlScrollAxisType, g as getRtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nexport { _ as _getEventTarget, c as _getFocusedElementPierceShadowDom, a as _getShadowRoot, b as _supportsShadowDom } from './shadow-dom-B0oHn41l.mjs';\nexport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nimport '@angular/common';\nclass PlatformModule {\n  static ɵfac = function PlatformModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PlatformModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PlatformModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n// `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n// first changing it to something else:\n// The specified value \"\" does not conform to the required format.\n// The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n'color', 'button', 'checkbox', 'date', 'datetime-local', 'email', 'file', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'text', 'time', 'url', 'week'];\n/** @returns The input types supported by this browser. */\nfunction getSupportedInputTypes() {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  }\n  // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n    featureTestInput.setAttribute('type', value);\n    return featureTestInput.type === value;\n  }));\n  return supportedInputTypes;\n}\nexport { PlatformModule, getSupportedInputTypes };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Optional, Directive } from '@angular/core';\nimport { isPlatformBrowser, DOCUMENT } from '@angular/common';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { getRtlScrollAxisType } from '@angular/cdk/platform';\nimport { Subject, merge, fromEvent, take, Observable, animationFrameScheduler, of, expand, takeWhile, takeUntil, finalize } from 'rxjs';\n\n/**\r\n * https://github.com/gre/bezier-easing\r\n * BezierEasing - use bezier curve for transition easing function\r\n * by <PERSON><PERSON><PERSON><PERSON> 2014 - 2015 – MIT License\r\n */\n// These values are established by empiricism with tests (tradeoff: performance VS precision)\nconst NEWTON_ITERATIONS = 4;\nconst NEWTON_MIN_SLOPE = 0.001;\nconst SUBDIVISION_PRECISION = 0.0000001;\nconst SUBDIVISION_MAX_ITERATIONS = 10;\nconst kSplineTableSize = 11;\nconst kSampleStepSize = 1.0 / (kSplineTableSize - 1.0);\nconst float32ArraySupported = typeof Float32Array === 'function';\nfunction A(aA1, aA2) {\n  return 1.0 - 3.0 * aA2 + 3.0 * aA1;\n}\nfunction B(aA1, aA2) {\n  return 3.0 * aA2 - 6.0 * aA1;\n}\nfunction C(aA1) {\n  return 3.0 * aA1;\n}\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nfunction calcBezier(aT, aA1, aA2) {\n  return ((A(aA1, aA2) * aT + B(aA1, aA2)) * aT + C(aA1)) * aT;\n}\n// Returns dx/dt given t, x1, and x2, or dy/dt given t, y1, and y2.\nfunction getSlope(aT, aA1, aA2) {\n  return 3.0 * A(aA1, aA2) * aT * aT + 2.0 * B(aA1, aA2) * aT + C(aA1);\n}\nfunction binarySubdivide(aX, aA, aB, mX1, mX2) {\n  let currentX,\n    currentT,\n    i = 0;\n  do {\n    currentT = aA + (aB - aA) / 2.0;\n    currentX = calcBezier(currentT, mX1, mX2) - aX;\n    if (currentX > 0.0) {\n      aB = currentT;\n    } else {\n      aA = currentT;\n    }\n  } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i < SUBDIVISION_MAX_ITERATIONS);\n  return currentT;\n}\nfunction newtonRaphsonIterate(aX, aGuessT, mX1, mX2) {\n  for (let i = 0; i < NEWTON_ITERATIONS; ++i) {\n    let currentSlope = getSlope(aGuessT, mX1, mX2);\n    if (currentSlope === 0.0) {\n      return aGuessT;\n    }\n    let currentX = calcBezier(aGuessT, mX1, mX2) - aX;\n    aGuessT -= currentX / currentSlope;\n  }\n  return aGuessT;\n}\nfunction LinearEasing(x) {\n  return x;\n}\nfunction bezier(mX1, mY1, mX2, mY2) {\n  if (!(0 <= mX1 && mX1 <= 1 && 0 <= mX2 && mX2 <= 1)) {\n    throw new Error('bezier x values must be in [0, 1] range');\n  }\n  if (mX1 === mY1 && mX2 === mY2) {\n    return LinearEasing;\n  }\n  // Precompute samples table\n  let sampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);\n  for (let i = 0; i < kSplineTableSize; ++i) {\n    sampleValues[i] = calcBezier(i * kSampleStepSize, mX1, mX2);\n  }\n  function getTForX(aX) {\n    let intervalStart = 0.0;\n    let currentSample = 1;\n    let lastSample = kSplineTableSize - 1;\n    for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {\n      intervalStart += kSampleStepSize;\n    }\n    --currentSample;\n    // Interpolate to provide an initial guess for t\n    let dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);\n    let guessForT = intervalStart + dist * kSampleStepSize;\n    let initialSlope = getSlope(guessForT, mX1, mX2);\n    if (initialSlope >= NEWTON_MIN_SLOPE) {\n      return newtonRaphsonIterate(aX, guessForT, mX1, mX2);\n    } else if (initialSlope === 0.0) {\n      return guessForT;\n    } else {\n      return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);\n    }\n  }\n  return function BezierEasing(x) {\n    // Because JavaScript number are imprecise, we should guarantee the extremes are right.\n    if (x === 0) {\n      return 0;\n    }\n    if (x === 1) {\n      return 1;\n    }\n    return calcBezier(getTForX(x), mY1, mY2);\n  };\n}\n;\nconst SMOOTH_SCROLL_OPTIONS = new InjectionToken('SMOOTH_SCROLL_OPTIONS');\n\n// @dynamic\nclass SmoothScrollManager {\n  get _w() {\n    return this._document.defaultView;\n  }\n  /**\r\n   * Timing method\r\n   */\n  get _now() {\n    return this._w.performance && this._w.performance.now ? this._w.performance.now.bind(this._w.performance) : Date.now;\n  }\n  constructor(_document, _platform, customDefaultOptions) {\n    this._document = _document;\n    this._platform = _platform;\n    // Keeps track of the ongoing SmoothScroll functions so they can be handled in case of duplication.\n    // Each scrolled element gets a destroyer stream which gets deleted immediately after it completes.\n    // Purpose: If user called a scroll function again on the same element before the scrolls completes,\n    // it cancels the ongoing scroll and starts a new one\n    this._onGoingScrolls = new Map();\n    this._defaultOptions = {\n      duration: 468,\n      easing: {\n        x1: 0.42,\n        y1: 0,\n        x2: 0.58,\n        y2: 1\n      },\n      ...customDefaultOptions\n    };\n  }\n  /**\r\n   * changes scroll position inside an element\r\n   */\n  _scrollElement(el, x, y) {\n    el.scrollLeft = x;\n    el.scrollTop = y;\n  }\n  /**\r\n   * Handles a given parameter of type HTMLElement, ElementRef or selector\r\n   */\n  _getElement(el, parent) {\n    if (typeof el === 'string') {\n      return (parent || this._document).querySelector(el);\n    }\n    return coerceElement(el);\n  }\n  /**\r\n   * Initializes a destroyer stream, re-initializes it if the element is already being scrolled\r\n   */\n  _initSmoothScroll(el) {\n    if (this._onGoingScrolls.has(el)) {\n      this._onGoingScrolls.get(el).next();\n    }\n    return this._onGoingScrolls.set(el, new Subject()).get(el);\n  }\n  /**\r\n   * Checks if smooth scroll has reached, cleans up the smooth scroll stream and resolves its promise\r\n   */\n  _isFinished(context, destroyed, resolve) {\n    if (context.currentX !== context.x || context.currentY !== context.y) {\n      return true;\n    }\n    destroyed.next();\n    resolve();\n    return false;\n  }\n  /**\r\n   * Terminates an ongoing smooth scroll\r\n   */\n  _interrupted(el, destroyed) {\n    return merge(fromEvent(el, 'wheel', {\n      passive: true,\n      capture: true\n    }), fromEvent(el, 'touchmove', {\n      passive: true,\n      capture: true\n    }), destroyed).pipe(take(1));\n  }\n  /**\r\n   * Deletes the destroyer function, runs if the smooth scroll has finished or interrupted\r\n   */\n  _destroy(el, destroyed) {\n    destroyed.complete();\n    this._onGoingScrolls.delete(el);\n  }\n  /**\r\n   * A function called recursively that, given a context, steps through scrolling\r\n   */\n  _step(context) {\n    return new Observable(subscriber => {\n      let elapsed = (this._now() - context.startTime) / context.duration;\n      // avoid elapsed times higher than one\n      elapsed = elapsed > 1 ? 1 : elapsed;\n      // apply easing to elapsed time\n      const value = context.easing(elapsed);\n      context.currentX = context.startX + (context.x - context.startX) * value;\n      context.currentY = context.startY + (context.y - context.startY) * value;\n      this._scrollElement(context.scrollable, context.currentX, context.currentY);\n      // Proceed to the step\n      animationFrameScheduler.schedule(() => subscriber.next(context));\n    });\n  }\n  _applyScrollToOptions(el, options) {\n    if (!options.duration) {\n      this._scrollElement(el, options.left, options.top);\n      return Promise.resolve();\n    }\n    // Initialize a destroyer stream, reinitialize it if the element is already being scrolled\n    const destroyed = this._initSmoothScroll(el);\n    const context = {\n      scrollable: el,\n      startTime: this._now(),\n      startX: el.scrollLeft,\n      startY: el.scrollTop,\n      x: options.left == null ? el.scrollLeft : ~~options.left,\n      y: options.top == null ? el.scrollTop : ~~options.top,\n      duration: options.duration,\n      easing: bezier(options.easing.x1, options.easing.y1, options.easing.x2, options.easing.y2)\n    };\n    return new Promise(resolve => {\n      // Scroll each step recursively\n      of(null).pipe(expand(() => this._step(context).pipe(takeWhile(currContext => this._isFinished(currContext, destroyed, resolve)))), takeUntil(this._interrupted(el, destroyed)), finalize(() => this._destroy(el, destroyed))).subscribe();\n    });\n  }\n  /**\r\n   * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\r\n   * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\r\n   * left and right always refer to the left and right side of the scrolling container irrespective\r\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\r\n   * in an RTL context.\r\n   * @param scrollable element\r\n   * @param customOptions specified the offsets to scroll to.\r\n   */\n  scrollTo(scrollable, customOptions) {\n    if (isPlatformBrowser(this._platform)) {\n      const el = this._getElement(scrollable);\n      const isRtl = getComputedStyle(el).direction === 'rtl';\n      const rtlScrollAxisType = getRtlScrollAxisType();\n      const options = {\n        ...this._defaultOptions,\n        ...customOptions,\n        ...{\n          // Rewrite start & end offsets as right or left offsets.\n          left: customOptions.left == null ? isRtl ? customOptions.end : customOptions.start : customOptions.left,\n          right: customOptions.right == null ? isRtl ? customOptions.start : customOptions.end : customOptions.right\n        }\n      };\n      // Rewrite the bottom offset as a top offset.\n      if (options.bottom != null) {\n        options.top = el.scrollHeight - el.clientHeight - options.bottom;\n      }\n      // Rewrite the right offset as a left offset.\n      if (isRtl && rtlScrollAxisType !== 0 /* RtlScrollAxisType.NORMAL */) {\n        if (options.left != null) {\n          options.right = el.scrollWidth - el.clientWidth - options.left;\n        }\n        if (rtlScrollAxisType === 2 /* RtlScrollAxisType.INVERTED */) {\n          options.left = options.right;\n        } else if (rtlScrollAxisType === 1 /* RtlScrollAxisType.NEGATED */) {\n          options.left = options.right ? -options.right : options.right;\n        }\n      } else {\n        if (options.right != null) {\n          options.left = el.scrollWidth - el.clientWidth - options.right;\n        }\n      }\n      return this._applyScrollToOptions(el, options);\n    }\n    return Promise.resolve();\n  }\n  /**\r\n   * Scroll to element by reference or selector\r\n   */\n  scrollToElement(scrollable, target, customOptions = {}) {\n    const scrollableEl = this._getElement(scrollable);\n    const targetEl = this._getElement(target, scrollableEl);\n    const options = {\n      ...customOptions,\n      ...{\n        left: targetEl.offsetLeft + (customOptions.left || 0),\n        top: targetEl.offsetTop + (customOptions.top || 0)\n      }\n    };\n    return targetEl ? this.scrollTo(scrollableEl, options) : Promise.resolve();\n  }\n  static {\n    this.ɵfac = function SmoothScrollManager_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SmoothScrollManager)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(SMOOTH_SCROLL_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SmoothScrollManager,\n      factory: SmoothScrollManager.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SmoothScrollManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [SMOOTH_SCROLL_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\nclass SmoothScroll {\n  constructor(element, smoothScroll) {\n    this.element = element;\n    this.smoothScroll = smoothScroll;\n  }\n  scrollTo(options) {\n    return this.smoothScroll.scrollTo(this.element, options);\n  }\n  scrollToElement(target, options) {\n    return this.smoothScroll.scrollToElement(this.element, target, options);\n  }\n  static {\n    this.ɵfac = function SmoothScroll_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SmoothScroll)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(SmoothScrollManager));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SmoothScroll,\n      selectors: [[\"\", \"smoothScroll\", \"\"], [\"\", \"smooth-scroll\", \"\"]],\n      exportAs: [\"smoothScroll\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SmoothScroll, [{\n    type: Directive,\n    args: [{\n      selector: '[smoothScroll], [smooth-scroll]',\n      exportAs: 'smoothScroll',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: SmoothScrollManager\n    }];\n  }, null);\n})();\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { SMOOTH_SCROLL_OPTIONS, SmoothScroll, SmoothScrollManager };\n", "import * as i0 from '@angular/core';\nimport { Directive, Input, Inject, Output, Component, ChangeDetectionStrategy, ViewChild, InjectionToken, Injectable, Optional, RendererStyleFlags2, EventEmitter, ElementRef, ContentChild, NgModule } from '@angular/core';\nimport { DOCUMENT, NgIf } from '@angular/common';\nimport { coerceNumberProperty, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { tap, Observable, fromEvent, map, merge, takeUntil, switchMap, EMPTY, of, Subject, distinctUntilChanged, animationFrameScheduler, mergeMap, BehaviorSubject, debounceTime, Subscription, pairwise, filter, auditTime } from 'rxjs';\nimport * as i3 from '@angular/cdk/bidi';\nimport * as i2 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType } from '@angular/cdk/platform';\nimport * as i2$1 from 'ngx-scrollbar/smooth-scroll';\nconst _c0 = [\"scrollbarY\"];\nconst _c1 = [\"scrollbarX\"];\nconst _c2 = [\"*\"];\nfunction NgScrollbar_ng_container_5_scrollbar_x_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"scrollbar-x\", null, 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"scrollable\", ctx_r0.state.isHorizontallyScrollable)(\"fit\", ctx_r0.state.verticalUsed);\n  }\n}\nfunction NgScrollbar_ng_container_5_scrollbar_y_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"scrollbar-y\", null, 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"scrollable\", ctx_r0.state.isVerticallyScrollable)(\"fit\", ctx_r0.state.horizontalUsed);\n  }\n}\nfunction NgScrollbar_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NgScrollbar_ng_container_5_scrollbar_x_1_Template, 2, 2, \"scrollbar-x\", 5)(2, NgScrollbar_ng_container_5_scrollbar_y_2_Template, 2, 2, \"scrollbar-y\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.state.horizontalUsed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.state.verticalUsed);\n  }\n}\nclass NgAttr {\n  constructor(el) {\n    this.el = el;\n  }\n  set ngAttr(attrs) {\n    for (const [key, value] of Object.entries(attrs)) {\n      this.el.nativeElement.setAttribute(key, value);\n    }\n  }\n  static {\n    this.ɵfac = function NgAttr_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgAttr)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgAttr,\n      selectors: [[\"\", \"ngAttr\", \"\"]],\n      inputs: {\n        ngAttr: \"ngAttr\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgAttr, [{\n    type: Directive,\n    args: [{\n      selector: '[ngAttr]',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    ngAttr: [{\n      type: Input\n    }]\n  });\n})();\nfunction preventSelection(doc) {\n  return tap(() => {\n    doc.onselectstart = () => false;\n  });\n}\nfunction enableSelection(doc) {\n  return tap(() => {\n    doc.onselectstart = null;\n  });\n}\nfunction stopPropagation() {\n  return tap(e => e.stopPropagation());\n}\n/**\r\n * Check if pointer is within scrollbar bounds\r\n */\nfunction isWithinBounds(e, rect) {\n  return e.clientX >= rect.left && e.clientX <= rect.left + rect.width && e.clientY >= rect.top && e.clientY <= rect.top + rect.height;\n}\nclass ScrollViewport {\n  // Get viewport size, clientHeight or clientWidth\n  get clientHeight() {\n    return this.nativeElement.clientHeight;\n  }\n  get clientWidth() {\n    return this.nativeElement.clientWidth;\n  }\n  get scrollHeight() {\n    return this.nativeElement.scrollHeight;\n  }\n  get scrollWidth() {\n    return this.nativeElement.scrollWidth;\n  }\n  // Get viewport scroll offset, scrollTop or scrollLeft\n  get scrollTop() {\n    return this.nativeElement.scrollTop;\n  }\n  get scrollLeft() {\n    return this.nativeElement.scrollLeft;\n  }\n  // Get the available scrollable size\n  get scrollMaxX() {\n    return this.scrollWidth - this.clientWidth;\n  }\n  get scrollMaxY() {\n    return this.scrollHeight - this.clientHeight;\n  }\n  get contentHeight() {\n    return this.contentWrapperElement?.clientHeight || 0;\n  }\n  get contentWidth() {\n    return this.contentWrapperElement?.clientWidth || 0;\n  }\n  constructor(viewPort) {\n    this.viewPort = viewPort;\n    this.nativeElement = viewPort.nativeElement;\n  }\n  /**\r\n   * Activate viewport pointer events such as 'hovered' and 'clicked' events\r\n   */\n  activatePointerEvents(propagate, destroyed) {\n    this.hovered = new Observable(subscriber => {\n      // Stream that emits when pointer is moved over the viewport (used to set the hovered state)\n      const mouseMoveStream = fromEvent(this.nativeElement, 'mousemove', {\n        passive: true\n      });\n      const mouseMove = propagate ? mouseMoveStream : mouseMoveStream.pipe(stopPropagation());\n      // Stream that emits when pointer leaves the viewport (used to remove the hovered state)\n      const mouseLeave = fromEvent(this.nativeElement, 'mouseleave', {\n        passive: true\n      }).pipe(map(() => false));\n      merge(mouseMove, mouseLeave).pipe(tap(e => subscriber.next(e)), takeUntil(destroyed)).subscribe();\n    });\n    this.clicked = new Observable(subscriber => {\n      const mouseDown = fromEvent(this.nativeElement, 'mousedown', {\n        passive: true\n      }).pipe(tap(e => subscriber.next(e)));\n      const mouseUp = fromEvent(this.nativeElement, 'mouseup', {\n        passive: true\n      }).pipe(tap(() => subscriber.next(false)));\n      mouseDown.pipe(switchMap(() => mouseUp), takeUntil(destroyed)).subscribe();\n    });\n  }\n  /**\r\n   * Set this directive as a non-functional wrapper, called when a custom viewport is used\r\n   */\n  setAsWrapper() {\n    // In this case the default viewport and the default content wrapper will act as a mask\n    this.nativeElement.className = 'ng-native-scrollbar-hider ng-scroll-layer';\n    if (this.nativeElement.firstElementChild) {\n      this.nativeElement.firstElementChild.className = 'ng-scroll-layer';\n    }\n  }\n  /**\r\n   * Set this directive as  the viewport, called when no custom viewport is used\r\n   */\n  setAsViewport(customClassName) {\n    this.nativeElement.className += ` ng-native-scrollbar-hider ng-scroll-viewport ${customClassName}`;\n    // Check if the custom viewport has only one child and set it as the content wrapper\n    if (this.nativeElement.firstElementChild) {\n      this.contentWrapperElement = this.nativeElement.firstElementChild;\n      this.contentWrapperElement.classList.add('ng-scroll-content');\n    }\n  }\n  /**\r\n   * Scroll viewport vertically\r\n   */\n  scrollYTo(value) {\n    this.nativeElement.scrollTop = value;\n  }\n  /**\r\n   * Scroll viewport horizontally\r\n   */\n  scrollXTo(value) {\n    this.nativeElement.scrollLeft = value;\n  }\n  static {\n    this.ɵfac = function ScrollViewport_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollViewport)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ScrollViewport,\n      selectors: [[\"\", \"scrollViewport\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollViewport, [{\n    type: Directive,\n    args: [{\n      selector: '[scrollViewport]',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\nclass NgScrollbarBase {\n  static {\n    this.ɵfac = function NgScrollbarBase_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgScrollbarBase)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NgScrollbarBase,\n      standalone: false\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgScrollbarBase, [{\n    type: Directive\n  }], null, null);\n})();\n\n// @dynamic\nclass TrackAdapter {\n  // Stream that emits when the track element is clicked\n  get clicked() {\n    const mouseDown = fromEvent(this.trackElement, 'mousedown', {\n      passive: true\n    }).pipe(stopPropagation(), preventSelection(this.document));\n    const mouseup = fromEvent(this.document, 'mouseup', {\n      passive: true\n    }).pipe(stopPropagation(), enableSelection(this.document), switchMap(() => EMPTY));\n    return merge(mouseDown, mouseup);\n  }\n  // Get track client rect\n  get clientRect() {\n    return this.trackElement.getBoundingClientRect();\n  }\n  constructor(cmp, trackElement, document) {\n    this.cmp = cmp;\n    this.trackElement = trackElement;\n    this.document = document;\n  }\n  /**\r\n   * Stream that emits when scrollbar track is clicked\r\n   */\n  onTrackClicked(e, thumbSize, scrollSize) {\n    return of(e).pipe(map(e => e[this.pageProperty]),\n    // Calculate scrollTo position\n    map(pageOffset => {\n      const clickOffset = pageOffset - this.offset;\n      const offset = clickOffset - thumbSize / 2;\n      const ratio = offset / this.size;\n      return ratio * scrollSize;\n    }),\n    // Smooth scroll to position\n    tap(value => {\n      this.cmp.scrollTo({\n        ...this.mapToScrollToOption(value),\n        duration: coerceNumberProperty(this.cmp.trackClickScrollDuration)\n      });\n    }));\n  }\n  static {\n    this.ɵfac = function TrackAdapter_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TrackAdapter)(i0.ɵɵdirectiveInject(NgScrollbarBase), i0.ɵɵdirectiveInject(HTMLElement), i0.ɵɵdirectiveInject(Document));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TrackAdapter,\n      standalone: false\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TrackAdapter, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: NgScrollbarBase\n    }, {\n      type: HTMLElement\n    }, {\n      type: Document\n    }];\n  }, null);\n})();\nclass TrackXDirective extends TrackAdapter {\n  get pageProperty() {\n    return 'pageX';\n  }\n  get offset() {\n    return this.clientRect.left;\n  }\n  get size() {\n    return this.trackElement.clientWidth;\n  }\n  constructor(cmp, trackElement, document) {\n    super(cmp, trackElement.nativeElement, document);\n    this.cmp = cmp;\n    this.document = document;\n  }\n  mapToScrollToOption(value) {\n    return {\n      left: value\n    };\n  }\n  static {\n    this.ɵfac = function TrackXDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TrackXDirective)(i0.ɵɵdirectiveInject(NgScrollbarBase), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TrackXDirective,\n      selectors: [[\"\", \"scrollbarTrackX\", \"\"]],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TrackXDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[scrollbarTrackX]',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: NgScrollbarBase\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\nclass TrackYDirective extends TrackAdapter {\n  get pageProperty() {\n    return 'pageY';\n  }\n  get offset() {\n    return this.clientRect.top;\n  }\n  get size() {\n    return this.trackElement.clientHeight;\n  }\n  constructor(cmp, trackElement, document) {\n    super(cmp, trackElement.nativeElement, document);\n    this.cmp = cmp;\n    this.document = document;\n  }\n  mapToScrollToOption(value) {\n    return {\n      top: value\n    };\n  }\n  static {\n    this.ɵfac = function TrackYDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TrackYDirective)(i0.ɵɵdirectiveInject(NgScrollbarBase), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TrackYDirective,\n      selectors: [[\"\", \"scrollbarTrackY\", \"\"]],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TrackYDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[scrollbarTrackY]',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: NgScrollbarBase\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n// @dynamic\nclass ThumbAdapter {\n  get trackMax() {\n    return this.track.size - this.size;\n  }\n  // Get thumb client rect\n  get clientRect() {\n    return this.thumbElement.getBoundingClientRect();\n  }\n  // Stream that emits when scrollbar thumb is clicked\n  get clicked() {\n    return fromEvent(this.thumbElement, 'mousedown', {\n      passive: true\n    }).pipe(stopPropagation());\n  }\n  constructor(cmp, track, thumbElement, document) {\n    this.cmp = cmp;\n    this.track = track;\n    this.thumbElement = thumbElement;\n    this.document = document;\n    // Stream that emits dragging state\n    this._dragging = new Subject();\n    this.dragging = this._dragging.pipe(distinctUntilChanged());\n  }\n  // Calculate and update thumb position and size\n  update() {\n    const size = calculateThumbSize(this.track.size, this.viewportScrollSize, this.cmp.minThumbSize);\n    const position = calculateThumbPosition(this.viewportScrollOffset, this.viewportScrollMax, this.trackMax);\n    animationFrameScheduler.schedule(() => this.updateStyles(this.handleDirection(position, this.trackMax), size));\n  }\n  /**\r\n   * Stream that emits the 'scrollTo' position when a scrollbar thumb element is dragged\r\n   * This function is called by thumb drag event using viewport or scrollbar pointer events\r\n   */\n  dragged(event) {\n    let trackMaxStart;\n    let scrollMaxStart;\n    const dragStart = of(event).pipe(preventSelection(this.document), tap(() => {\n      // Capture scrollMax and trackMax once\n      trackMaxStart = this.trackMax;\n      scrollMaxStart = this.viewportScrollMax;\n      this.setDragging(true);\n    }));\n    const dragging = fromEvent(this.document, 'mousemove', {\n      capture: true,\n      passive: true\n    }).pipe(stopPropagation());\n    const dragEnd = fromEvent(this.document, 'mouseup', {\n      capture: true\n    }).pipe(stopPropagation(), enableSelection(this.document), tap(() => this.setDragging(false)));\n    return dragStart.pipe(map(e => e[this.pageProperty]), map(pageOffset => pageOffset - this.dragStartOffset), mergeMap(mouseDownOffset => dragging.pipe(map(e => e[this.clientProperty]),\n    // Calculate how far the pointer is from the top/left of the scrollbar (minus the dragOffset).\n    map(mouseOffset => mouseOffset - this.track.offset), map(offset => scrollMaxStart * (offset - mouseDownOffset) / trackMaxStart), map(position => this.handleDrag(position, scrollMaxStart)), tap(position => this.scrollTo(position)), takeUntil(dragEnd))));\n  }\n  static {\n    this.ɵfac = function ThumbAdapter_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ThumbAdapter)(i0.ɵɵdirectiveInject(NgScrollbarBase), i0.ɵɵdirectiveInject(TrackAdapter), i0.ɵɵdirectiveInject(HTMLElement), i0.ɵɵdirectiveInject(Document));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ThumbAdapter,\n      outputs: {\n        dragging: \"dragging\"\n      },\n      standalone: false\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ThumbAdapter, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: NgScrollbarBase\n    }, {\n      type: TrackAdapter\n    }, {\n      type: HTMLElement\n    }, {\n      type: Document\n    }];\n  }, {\n    dragging: [{\n      type: Output\n    }]\n  });\n})();\n/**\r\n * Calculate scrollbar thumb size\r\n */\nfunction calculateThumbSize(trackSize, contentSize, minThumbSize) {\n  const scrollbarRatio = trackSize / contentSize;\n  const thumbSize = scrollbarRatio * trackSize;\n  return Math.max(~~thumbSize, minThumbSize);\n}\n/**\r\n * Calculate scrollbar thumb position\r\n */\nfunction calculateThumbPosition(scrollPosition, scrollMax, trackMax) {\n  return scrollPosition * trackMax / scrollMax;\n}\nclass ThumbXDirective extends ThumbAdapter {\n  get clientProperty() {\n    return 'clientX';\n  }\n  get pageProperty() {\n    return 'pageX';\n  }\n  get viewportScrollSize() {\n    return this.cmp.viewport.scrollWidth;\n  }\n  get viewportScrollOffset() {\n    return this.cmp.viewport.scrollLeft;\n  }\n  get viewportScrollMax() {\n    return this.cmp.viewport.scrollMaxX;\n  }\n  get dragStartOffset() {\n    return this.clientRect.left + this.document.defaultView.pageXOffset || 0;\n  }\n  get size() {\n    return this.thumbElement.clientWidth;\n  }\n  constructor(cmp, track, element, document, dir) {\n    super(cmp, track, element.nativeElement, document);\n    this.cmp = cmp;\n    this.track = track;\n    this.element = element;\n    this.document = document;\n    this.dir = dir;\n  }\n  updateStyles(position, size) {\n    this.thumbElement.style.width = `${size}px`;\n    this.thumbElement.style.transform = `translate3d(${position}px, 0, 0)`;\n  }\n  handleDrag(position, scrollMax) {\n    if (this.dir.value === 'rtl') {\n      if (this.cmp.manager.rtlScrollAxisType === 1 /* RtlScrollAxisType.NEGATED */) {\n        return position - scrollMax;\n      }\n      if (this.cmp.manager.rtlScrollAxisType === 2 /* RtlScrollAxisType.INVERTED */) {\n        return scrollMax - position;\n      }\n      // Keeping this as a memo\n      // if (this.rtlScrollAxisType === RtlScrollAxisType.NORMAL) {\n      //   return position;\n      // }\n    }\n    return position;\n  }\n  handleDirection(position, trackMax) {\n    if (this.dir.value === 'rtl') {\n      if (this.cmp.manager.rtlScrollAxisType === 2 /* RtlScrollAxisType.INVERTED */) {\n        return -position;\n      }\n      if (this.cmp.manager.rtlScrollAxisType === 0 /* RtlScrollAxisType.NORMAL */) {\n        return position - trackMax;\n      }\n      // Keeping this as a memo\n      // if (this.rtlScrollAxisType === RtlScrollAxisType.NEGATED) {\n      //   return position;\n      // }\n    }\n    return position;\n  }\n  setDragging(value) {\n    this.cmp.setDragging({\n      horizontalDragging: value\n    });\n  }\n  scrollTo(position) {\n    this.cmp.viewport.scrollXTo(position);\n  }\n  static {\n    this.ɵfac = function ThumbXDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ThumbXDirective)(i0.ɵɵdirectiveInject(NgScrollbarBase), i0.ɵɵdirectiveInject(TrackXDirective), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i3.Directionality));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ThumbXDirective,\n      selectors: [[\"\", \"scrollbarThumbX\", \"\"]],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ThumbXDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[scrollbarThumbX]',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: NgScrollbarBase\n    }, {\n      type: TrackXDirective\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i3.Directionality\n    }];\n  }, null);\n})();\nclass ThumbYDirective extends ThumbAdapter {\n  get pageProperty() {\n    return 'pageY';\n  }\n  get viewportScrollSize() {\n    return this.cmp.viewport.scrollHeight;\n  }\n  get viewportScrollOffset() {\n    return this.cmp.viewport.scrollTop;\n  }\n  get viewportScrollMax() {\n    return this.cmp.viewport.scrollMaxY;\n  }\n  get clientProperty() {\n    return 'clientY';\n  }\n  get dragStartOffset() {\n    return this.clientRect.top + this.document.defaultView.pageYOffset || 0;\n  }\n  get size() {\n    return this.thumbElement.clientHeight;\n  }\n  constructor(cmp, track, element, document) {\n    super(cmp, track, element.nativeElement, document);\n    this.cmp = cmp;\n    this.track = track;\n    this.element = element;\n    this.document = document;\n  }\n  updateStyles(position, size) {\n    this.thumbElement.style.height = `${size}px`;\n    this.thumbElement.style.transform = `translate3d(0px, ${position}px, 0)`;\n  }\n  handleDrag(position) {\n    return position;\n  }\n  handleDirection(position) {\n    return position;\n  }\n  setDragging(value) {\n    this.cmp.setDragging({\n      verticalDragging: value\n    });\n  }\n  scrollTo(position) {\n    this.cmp.viewport.scrollYTo(position);\n  }\n  static {\n    this.ɵfac = function ThumbYDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ThumbYDirective)(i0.ɵɵdirectiveInject(NgScrollbarBase), i0.ɵɵdirectiveInject(TrackYDirective), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ThumbYDirective,\n      selectors: [[\"\", \"scrollbarThumbY\", \"\"]],\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ThumbYDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[scrollbarThumbY]',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: NgScrollbarBase\n    }, {\n      type: TrackYDirective\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\n// @dynamic\nclass Scrollbar {\n  constructor(el, cmp, platform, document, zone) {\n    this.el = el;\n    this.cmp = cmp;\n    this.platform = platform;\n    this.document = document;\n    this.zone = zone;\n    // Stream that emits to unsubscribe from all streams\n    this.destroyed = new Subject();\n  }\n  /**\r\n   * Activate scrollbar pointer events\r\n   */\n  activatePointerEvents() {\n    // Stream that emits when scrollbar thumb is dragged\n    let thumbDragEvent;\n    // Stream that emits when scrollbar track is clicked\n    let trackClickEvent;\n    // Stream that emits when scrollbar track is hovered\n    let trackHoveredEvent;\n    // Set the method used for the pointer events option\n    if (this.cmp.pointerEventsMethod === 'viewport') {\n      // Pointer events using the viewport\n      this.viewportTrackClicked = new Subject();\n      this.viewportThumbClicked = new Subject();\n      // Activate the pointer events of the viewport directive\n      this.cmp.viewport.activatePointerEvents(this.cmp.viewportPropagateMouseMove, this.destroyed);\n      // Set streams\n      thumbDragEvent = this.viewportThumbClicked;\n      trackClickEvent = this.viewportTrackClicked;\n      trackHoveredEvent = this.cmp.viewport.hovered.pipe(\n      // Check if track is hovered\n      map(e => e ? isWithinBounds(e, this.el.getBoundingClientRect()) : false), distinctUntilChanged(),\n      // Enable / disable text selection\n      tap(hovered => this.document.onselectstart = hovered ? () => false : null));\n      this.cmp.viewport.clicked.pipe(tap(e => {\n        if (e) {\n          if (isWithinBounds(e, this.thumb.clientRect)) {\n            this.viewportThumbClicked.next(e);\n          } else if (isWithinBounds(e, this.track.clientRect)) {\n            this.cmp.setClicked(true);\n            this.viewportTrackClicked.next(e);\n          }\n        } else {\n          this.cmp.setClicked(false);\n        }\n      }), takeUntil(this.destroyed)).subscribe();\n    } else {\n      // Pointer events method is using 'scrollbar'\n      thumbDragEvent = this.thumb.clicked;\n      trackClickEvent = this.track.clicked;\n      trackHoveredEvent = this.hovered;\n    }\n    return merge(\n    // Activate scrollbar hovered event\n    trackHoveredEvent.pipe(tap(e => this.setHovered(e))),\n    // Activate scrollbar thumb drag event\n    thumbDragEvent.pipe(switchMap(e => this.thumb.dragged(e))),\n    // Activate scrollbar track click event\n    trackClickEvent.pipe(switchMap(e => this.track.onTrackClicked(e, this.thumb.size, this.viewportScrollSize))));\n  }\n  // Stream that emits when the track element is hovered\n  get hovered() {\n    const mouseEnter = fromEvent(this.el, 'mouseenter', {\n      passive: true\n    }).pipe(stopPropagation(), map(() => true));\n    const mouseLeave = fromEvent(this.el, 'mouseleave', {\n      passive: true\n    }).pipe(stopPropagation(), map(() => false));\n    return merge(mouseEnter, mouseLeave);\n  }\n  ngOnInit() {\n    this.zone.runOutsideAngular(() => {\n      // Activate pointer events on Desktop only\n      if (!(this.platform.IOS || this.platform.ANDROID) && !this.cmp.pointerEventsDisabled) {\n        this.activatePointerEvents().pipe(takeUntil(this.destroyed)).subscribe();\n      }\n      // Update scrollbar thumb when viewport is scrolled and when scrollbar component is updated\n      merge(this.cmp.scrolled, this.cmp.updated).pipe(tap(() => this.thumb?.update()), takeUntil(this.destroyed)).subscribe();\n    });\n  }\n  ngOnDestroy() {\n    this.destroyed.next();\n    this.destroyed.complete();\n    // Clean up viewport streams if used\n    if (this.viewportThumbClicked && this.viewportTrackClicked) {\n      this.viewportTrackClicked.complete();\n      this.viewportThumbClicked.complete();\n    }\n  }\n  static {\n    this.ɵfac = function Scrollbar_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Scrollbar)(i0.ɵɵdirectiveInject(HTMLElement), i0.ɵɵdirectiveInject(NgScrollbarBase), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(Document), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: Scrollbar,\n      standalone: false\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Scrollbar, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: HTMLElement\n    }, {\n      type: NgScrollbarBase\n    }, {\n      type: i2.Platform\n    }, {\n      type: Document\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nclass ScrollbarY extends Scrollbar {\n  get viewportScrollSize() {\n    return this.cmp.viewport.scrollHeight;\n  }\n  constructor(el, cmp, platform, document, zone) {\n    super(el.nativeElement, cmp, platform, document, zone);\n    this.cmp = cmp;\n    this.platform = platform;\n    this.document = document;\n    this.zone = zone;\n  }\n  setHovered(value) {\n    this.cmp.setHovered({\n      verticalHovered: value\n    });\n  }\n  static {\n    this.ɵfac = function ScrollbarY_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollbarY)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NgScrollbarBase), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ScrollbarY,\n      selectors: [[\"scrollbar-y\"]],\n      viewQuery: function ScrollbarY_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TrackYDirective, 7);\n          i0.ɵɵviewQuery(ThumbYDirective, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.track = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.thumb = _t.first);\n        }\n      },\n      hostVars: 2,\n      hostBindings: function ScrollbarY_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"scrollbar-control\", true);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 2,\n      vars: 6,\n      consts: [[\"scrollbarTrackY\", \"\"], [\"scrollbarThumbY\", \"\"]],\n      template: function ScrollbarY_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(i0.ɵɵinterpolate1(\"ng-scrollbar-track \", ctx.cmp.trackClass));\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(i0.ɵɵinterpolate1(\"ng-scrollbar-thumb \", ctx.cmp.thumbClass));\n        }\n      },\n      dependencies: [TrackYDirective, ThumbYDirective],\n      styles: [\".ng-scrollbar-wrapper>scrollbar-y.scrollbar-control{width:var(--vertical-scrollbar-total-size)}  .ng-scrollbar-wrapper>scrollbar-y.scrollbar-control>.ng-scrollbar-track{width:var(--vertical-scrollbar-size);height:calc(100% - var(--scrollbar-padding) * 2)}  .ng-scrollbar-wrapper>scrollbar-y.scrollbar-control>.ng-scrollbar-track>.ng-scrollbar-thumb{height:0;width:100%}  .ng-scrollbar-wrapper[verticalHovered=true]>scrollbar-y.scrollbar-control .ng-scrollbar-thumb,   .ng-scrollbar-wrapper[verticalDragging=true]>scrollbar-y.scrollbar-control .ng-scrollbar-thumb{background-color:var(--scrollbar-thumb-hover-color)}  .ng-scrollbar-wrapper[deactivated=false]>scrollbar-y.scrollbar-control{top:0;bottom:0}  .ng-scrollbar-wrapper[deactivated=false][dir=ltr]>scrollbar-y.scrollbar-control{right:0;left:unset}  .ng-scrollbar-wrapper[deactivated=false][dir=ltr][position=invertY]>scrollbar-y.scrollbar-control,   .ng-scrollbar-wrapper[deactivated=false][dir=ltr][position=invertAll]>scrollbar-y.scrollbar-control{left:0;right:unset}  .ng-scrollbar-wrapper[deactivated=false][dir=rtl]>scrollbar-y.scrollbar-control{left:0;right:unset}  .ng-scrollbar-wrapper[deactivated=false][dir=rtl][position=invertY]>scrollbar-y.scrollbar-control,   .ng-scrollbar-wrapper[deactivated=false][dir=rtl][position=invertAll]>scrollbar-y.scrollbar-control{left:unset;right:0}  .ng-scrollbar-wrapper[deactivated=false][track=all]>scrollbar-y.scrollbar-control[fit=true]{bottom:var(--scrollbar-total-size);top:0}  .ng-scrollbar-wrapper[deactivated=false][track=all][position=invertX]>scrollbar-y.scrollbar-control[fit=true],   .ng-scrollbar-wrapper[deactivated=false][track=all][position=invertAll]>scrollbar-y.scrollbar-control[fit=true]{top:var(--scrollbar-total-size);bottom:0}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollbarY, [{\n    type: Component,\n    args: [{\n      selector: 'scrollbar-y',\n      host: {\n        '[class.scrollbar-control]': 'true'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <div scrollbarTrackY class=\"ng-scrollbar-track {{cmp.trackClass}}\">\n      <div scrollbarThumbY class=\"ng-scrollbar-thumb {{cmp.thumbClass}}\"></div>\n    </div>\n  `,\n      standalone: true,\n      imports: [TrackYDirective, ThumbYDirective],\n      styles: [\"::ng-deep .ng-scrollbar-wrapper>scrollbar-y.scrollbar-control{width:var(--vertical-scrollbar-total-size)}::ng-deep .ng-scrollbar-wrapper>scrollbar-y.scrollbar-control>.ng-scrollbar-track{width:var(--vertical-scrollbar-size);height:calc(100% - var(--scrollbar-padding) * 2)}::ng-deep .ng-scrollbar-wrapper>scrollbar-y.scrollbar-control>.ng-scrollbar-track>.ng-scrollbar-thumb{height:0;width:100%}::ng-deep .ng-scrollbar-wrapper[verticalHovered=true]>scrollbar-y.scrollbar-control .ng-scrollbar-thumb,::ng-deep .ng-scrollbar-wrapper[verticalDragging=true]>scrollbar-y.scrollbar-control .ng-scrollbar-thumb{background-color:var(--scrollbar-thumb-hover-color)}::ng-deep .ng-scrollbar-wrapper[deactivated=false]>scrollbar-y.scrollbar-control{top:0;bottom:0}::ng-deep .ng-scrollbar-wrapper[deactivated=false][dir=ltr]>scrollbar-y.scrollbar-control{right:0;left:unset}::ng-deep .ng-scrollbar-wrapper[deactivated=false][dir=ltr][position=invertY]>scrollbar-y.scrollbar-control,::ng-deep .ng-scrollbar-wrapper[deactivated=false][dir=ltr][position=invertAll]>scrollbar-y.scrollbar-control{left:0;right:unset}::ng-deep .ng-scrollbar-wrapper[deactivated=false][dir=rtl]>scrollbar-y.scrollbar-control{left:0;right:unset}::ng-deep .ng-scrollbar-wrapper[deactivated=false][dir=rtl][position=invertY]>scrollbar-y.scrollbar-control,::ng-deep .ng-scrollbar-wrapper[deactivated=false][dir=rtl][position=invertAll]>scrollbar-y.scrollbar-control{left:unset;right:0}::ng-deep .ng-scrollbar-wrapper[deactivated=false][track=all]>scrollbar-y.scrollbar-control[fit=true]{bottom:var(--scrollbar-total-size);top:0}::ng-deep .ng-scrollbar-wrapper[deactivated=false][track=all][position=invertX]>scrollbar-y.scrollbar-control[fit=true],::ng-deep .ng-scrollbar-wrapper[deactivated=false][track=all][position=invertAll]>scrollbar-y.scrollbar-control[fit=true]{top:var(--scrollbar-total-size);bottom:0}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: NgScrollbarBase\n    }, {\n      type: i2.Platform\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    track: [{\n      type: ViewChild,\n      args: [TrackYDirective, {\n        static: true\n      }]\n    }],\n    thumb: [{\n      type: ViewChild,\n      args: [ThumbYDirective, {\n        static: true\n      }]\n    }]\n  });\n})();\nclass ScrollbarX extends Scrollbar {\n  get viewportScrollSize() {\n    return this.cmp.viewport.scrollWidth;\n  }\n  constructor(el, cmp, platform, document, zone) {\n    super(el.nativeElement, cmp, platform, document, zone);\n    this.cmp = cmp;\n    this.platform = platform;\n    this.document = document;\n    this.zone = zone;\n  }\n  setHovered(value) {\n    this.cmp.setHovered({\n      horizontalHovered: value\n    });\n  }\n  static {\n    this.ɵfac = function ScrollbarX_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollbarX)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NgScrollbarBase), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ScrollbarX,\n      selectors: [[\"scrollbar-x\"]],\n      viewQuery: function ScrollbarX_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TrackXDirective, 7);\n          i0.ɵɵviewQuery(ThumbXDirective, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.track = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.thumb = _t.first);\n        }\n      },\n      hostVars: 2,\n      hostBindings: function ScrollbarX_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"scrollbar-control\", true);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 2,\n      vars: 6,\n      consts: [[\"scrollbarTrackX\", \"\"], [\"scrollbarThumbX\", \"\"]],\n      template: function ScrollbarX_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(i0.ɵɵinterpolate1(\"ng-scrollbar-track \", ctx.cmp.trackClass));\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(i0.ɵɵinterpolate1(\"ng-scrollbar-thumb \", ctx.cmp.thumbClass));\n        }\n      },\n      dependencies: [TrackXDirective, ThumbXDirective],\n      styles: [\".ng-scrollbar-wrapper>scrollbar-x.scrollbar-control{height:var(--horizontal-scrollbar-total-size)}  .ng-scrollbar-wrapper>scrollbar-x.scrollbar-control>.ng-scrollbar-track{height:var(--horizontal-scrollbar-size);width:calc(100% - var(--scrollbar-padding) * 2)}  .ng-scrollbar-wrapper>scrollbar-x.scrollbar-control>.ng-scrollbar-track>.ng-scrollbar-thumb{width:0;height:100%}  .ng-scrollbar-wrapper[horizontalHovered=true]>scrollbar-x.scrollbar-control .ng-scrollbar-thumb,   .ng-scrollbar-wrapper[horizontalDragging=true]>scrollbar-x.scrollbar-control .ng-scrollbar-thumb{background-color:var(--scrollbar-thumb-hover-color)}  .ng-scrollbar-wrapper[position=invertX]>scrollbar-x.scrollbar-control,   .ng-scrollbar-wrapper[position=invertAll]>scrollbar-x.scrollbar-control{top:0;bottom:unset}  .ng-scrollbar-wrapper[deactivated=false]>scrollbar-x.scrollbar-control{left:0;right:0;bottom:0;top:unset}  .ng-scrollbar-wrapper[deactivated=false][position=invertX]>scrollbar-x.scrollbar-control,   .ng-scrollbar-wrapper[deactivated=false][position=invertAll]>scrollbar-x.scrollbar-control{top:0;bottom:unset}  .ng-scrollbar-wrapper[deactivated=false][track=all][dir=ltr]>scrollbar-x.scrollbar-control[fit=true]{right:var(--scrollbar-total-size);left:0}  .ng-scrollbar-wrapper[deactivated=false][track=all][dir=ltr][position=invertY]>scrollbar-x.scrollbar-control[fit=true],   .ng-scrollbar-wrapper[deactivated=false][track=all][dir=ltr][position=invertAll]>scrollbar-x.scrollbar-control[fit=true]{left:var(--scrollbar-total-size);right:0}  .ng-scrollbar-wrapper[deactivated=false][track=all][dir=rtl]>scrollbar-x.scrollbar-control[fit=true]{left:var(--scrollbar-total-size);right:0}  .ng-scrollbar-wrapper[deactivated=false][track=all][dir=rtl][position=invertY]>scrollbar-x.scrollbar-control[fit=true],   .ng-scrollbar-wrapper[deactivated=false][track=all][dir=rtl][position=invertAll]>scrollbar-x.scrollbar-control[fit=true]{right:var(--scrollbar-total-size);left:0}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollbarX, [{\n    type: Component,\n    args: [{\n      selector: 'scrollbar-x',\n      host: {\n        '[class.scrollbar-control]': 'true'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <div scrollbarTrackX class=\"ng-scrollbar-track {{cmp.trackClass}}\">\n      <div scrollbarThumbX class=\"ng-scrollbar-thumb {{cmp.thumbClass}}\"></div>\n    </div>\n  `,\n      standalone: true,\n      imports: [TrackXDirective, ThumbXDirective],\n      styles: [\"::ng-deep .ng-scrollbar-wrapper>scrollbar-x.scrollbar-control{height:var(--horizontal-scrollbar-total-size)}::ng-deep .ng-scrollbar-wrapper>scrollbar-x.scrollbar-control>.ng-scrollbar-track{height:var(--horizontal-scrollbar-size);width:calc(100% - var(--scrollbar-padding) * 2)}::ng-deep .ng-scrollbar-wrapper>scrollbar-x.scrollbar-control>.ng-scrollbar-track>.ng-scrollbar-thumb{width:0;height:100%}::ng-deep .ng-scrollbar-wrapper[horizontalHovered=true]>scrollbar-x.scrollbar-control .ng-scrollbar-thumb,::ng-deep .ng-scrollbar-wrapper[horizontalDragging=true]>scrollbar-x.scrollbar-control .ng-scrollbar-thumb{background-color:var(--scrollbar-thumb-hover-color)}::ng-deep .ng-scrollbar-wrapper[position=invertX]>scrollbar-x.scrollbar-control,::ng-deep .ng-scrollbar-wrapper[position=invertAll]>scrollbar-x.scrollbar-control{top:0;bottom:unset}::ng-deep .ng-scrollbar-wrapper[deactivated=false]>scrollbar-x.scrollbar-control{left:0;right:0;bottom:0;top:unset}::ng-deep .ng-scrollbar-wrapper[deactivated=false][position=invertX]>scrollbar-x.scrollbar-control,::ng-deep .ng-scrollbar-wrapper[deactivated=false][position=invertAll]>scrollbar-x.scrollbar-control{top:0;bottom:unset}::ng-deep .ng-scrollbar-wrapper[deactivated=false][track=all][dir=ltr]>scrollbar-x.scrollbar-control[fit=true]{right:var(--scrollbar-total-size);left:0}::ng-deep .ng-scrollbar-wrapper[deactivated=false][track=all][dir=ltr][position=invertY]>scrollbar-x.scrollbar-control[fit=true],::ng-deep .ng-scrollbar-wrapper[deactivated=false][track=all][dir=ltr][position=invertAll]>scrollbar-x.scrollbar-control[fit=true]{left:var(--scrollbar-total-size);right:0}::ng-deep .ng-scrollbar-wrapper[deactivated=false][track=all][dir=rtl]>scrollbar-x.scrollbar-control[fit=true]{left:var(--scrollbar-total-size);right:0}::ng-deep .ng-scrollbar-wrapper[deactivated=false][track=all][dir=rtl][position=invertY]>scrollbar-x.scrollbar-control[fit=true],::ng-deep .ng-scrollbar-wrapper[deactivated=false][track=all][dir=rtl][position=invertAll]>scrollbar-x.scrollbar-control[fit=true]{right:var(--scrollbar-total-size);left:0}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: NgScrollbarBase\n    }, {\n      type: i2.Platform\n    }, {\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    track: [{\n      type: ViewChild,\n      args: [TrackXDirective, {\n        static: true\n      }]\n    }],\n    thumb: [{\n      type: ViewChild,\n      args: [ThumbXDirective, {\n        static: true\n      }]\n    }]\n  });\n})();\nconst NG_SCROLLBAR_OPTIONS = new InjectionToken('NG_SCROLLBAR_OPTIONS');\nconst defaultOptions = {\n  viewClass: '',\n  trackClass: '',\n  thumbClass: '',\n  track: 'vertical',\n  appearance: 'compact',\n  visibility: 'native',\n  position: 'native',\n  pointerEventsMethod: 'viewport',\n  trackClickScrollDuration: 300,\n  minThumbSize: 20,\n  windowResizeDebounce: 0,\n  sensorDebounce: 0,\n  scrollAuditTime: 0,\n  viewportPropagateMouseMove: true,\n  autoHeightDisabled: true,\n  autoWidthDisabled: true,\n  sensorDisabled: false,\n  pointerEventsDisabled: false\n};\nclass ScrollbarManager {\n  constructor(options) {\n    this.globalOptions = options ? {\n      ...defaultOptions,\n      ...options\n    } : defaultOptions;\n    this.rtlScrollAxisType = getRtlScrollAxisType();\n  }\n  static {\n    this.ɵfac = function ScrollbarManager_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ScrollbarManager)(i0.ɵɵinject(NG_SCROLLBAR_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ScrollbarManager,\n      factory: ScrollbarManager.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollbarManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [NG_SCROLLBAR_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\nclass NativeScrollbarSizeFactory {\n  constructor(document, manager, platform) {\n    this.document = document;\n    this.manager = manager;\n    this.platform = platform;\n    this._scrollbarSize = new BehaviorSubject(this.getNativeScrollbarSize());\n    this.scrollbarSize = this._scrollbarSize.asObservable();\n    // Calculate native scrollbar size on window resize event, because the size changes if use zoomed in/out\n    if (platform.isBrowser) {\n      fromEvent(this.document.defaultView, 'resize', {\n        passive: true\n      }).pipe(debounceTime(this.manager.globalOptions.windowResizeDebounce), map(() => this.getNativeScrollbarSize()), distinctUntilChanged(), tap(size => this._scrollbarSize.next(size))).subscribe();\n    }\n  }\n  /**\r\n   * Get native scrollbar size\r\n   */\n  getNativeScrollbarSize() {\n    // Avoid executing browser code in server side rendering\n    if (!this.platform.isBrowser) {\n      return 0;\n    }\n    // Hide iOS browsers native scrollbar\n    if (this.platform.IOS) {\n      return 6;\n    }\n    const box = this.document.createElement('div');\n    box.className = 'ng-scrollbar-measure';\n    box.style.left = '0px';\n    box.style.overflow = 'scroll';\n    box.style.position = 'fixed';\n    box.style.top = '-9999px';\n    this.document.body.appendChild(box);\n    const size = box.getBoundingClientRect().right;\n    this.document.body.removeChild(box);\n    return size;\n  }\n  static {\n    this.ɵfac = function NativeScrollbarSizeFactory_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NativeScrollbarSizeFactory)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(ScrollbarManager), i0.ɵɵinject(i2.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NativeScrollbarSizeFactory,\n      factory: NativeScrollbarSizeFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NativeScrollbarSizeFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: ScrollbarManager\n    }, {\n      type: i2.Platform\n    }];\n  }, null);\n})();\nclass HideNativeScrollbar {\n  constructor(el, renderer, hideNativeScrollbar) {\n    this.renderer = renderer;\n    this.hideNativeScrollbar = hideNativeScrollbar;\n    this._subscriber = Subscription.EMPTY;\n    this._subscriber = hideNativeScrollbar.scrollbarSize.subscribe(size => {\n      this.renderer.setStyle(el.nativeElement, '--native-scrollbar-size', `-${size}px`, RendererStyleFlags2.DashCase);\n    });\n  }\n  ngOnDestroy() {\n    this._subscriber.unsubscribe();\n  }\n  static {\n    this.ɵfac = function HideNativeScrollbar_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HideNativeScrollbar)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(NativeScrollbarSizeFactory));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: HideNativeScrollbar,\n      selectors: [[\"\", \"hideNativeScrollbar\", \"\"]]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HideNativeScrollbar, [{\n    type: Directive,\n    args: [{\n      selector: '[hideNativeScrollbar]',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: NativeScrollbarSizeFactory\n    }];\n  }, null);\n})();\nclass ResizeSensor {\n  /** Debounce interval for emitting the changes. */\n  get debounce() {\n    return this._debounce;\n  }\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n    this._subscribe();\n  }\n  /** Whether ResizeObserver is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  constructor(zone, platform, scrollbar) {\n    this.zone = zone;\n    this.platform = platform;\n    this.scrollbar = scrollbar;\n    this._disabled = false;\n    this._currentSubscription = null;\n    this.event = new EventEmitter();\n    if (!scrollbar) {\n      throw new Error('[NgScrollbar Resize Sensor Directive]: Host element must be an NgScrollbar component.');\n    }\n  }\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this._disabled) {\n      this._subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n  _subscribe() {\n    this._unsubscribe();\n    if (this.platform.isBrowser) {\n      const stream = new Observable(observer => {\n        this._resizeObserver = new ResizeObserver(e => observer.next(e));\n        this._resizeObserver.observe(this.scrollbar.viewport.nativeElement);\n        if (this.scrollbar.viewport.contentWrapperElement) {\n          this._resizeObserver.observe(this.scrollbar.viewport.contentWrapperElement);\n        }\n      });\n      this.zone.runOutsideAngular(() => {\n        this._currentSubscription = (this._debounce ? stream.pipe(debounceTime(this._debounce)) : stream).subscribe(this.event);\n      });\n    }\n  }\n  _unsubscribe() {\n    this._resizeObserver?.disconnect();\n    this._currentSubscription?.unsubscribe();\n  }\n  static {\n    this.ɵfac = function ResizeSensor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ResizeSensor)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Platform), i0.ɵɵdirectiveInject(NgScrollbarBase));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: ResizeSensor,\n      selectors: [[\"\", \"resizeSensor\", \"\"]],\n      inputs: {\n        debounce: [0, \"sensorDebounce\", \"debounce\"],\n        disabled: [0, \"sensorDisabled\", \"disabled\"]\n      },\n      outputs: {\n        event: \"resizeSensor\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResizeSensor, [{\n    type: Directive,\n    args: [{\n      selector: '[resizeSensor]',\n      standalone: true\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: i2.Platform\n    }, {\n      type: NgScrollbarBase\n    }];\n  }, {\n    debounce: [{\n      type: Input,\n      args: ['sensorDebounce']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['sensorDisabled']\n    }],\n    event: [{\n      type: Output,\n      args: ['resizeSensor']\n    }]\n  });\n})();\nclass NgScrollbar {\n  /** Disable custom scrollbar and switch back to native scrollbar */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(disabled) {\n    this._disabled = coerceBooleanProperty(disabled);\n  }\n  /** Whether ResizeObserver is disabled */\n  get sensorDisabled() {\n    return this._sensorDisabled;\n  }\n  set sensorDisabled(disabled) {\n    this._sensorDisabled = coerceBooleanProperty(disabled);\n  }\n  /** A flag used to enable/disable the scrollbar thumb dragged event */\n  get pointerEventsDisabled() {\n    return this._pointerEventsDisabled;\n  }\n  set pointerEventsDisabled(disabled) {\n    this._pointerEventsDisabled = coerceBooleanProperty(disabled);\n  }\n  /** Enable viewport mousemove event propagation (only when pointerEventsMethod=\"viewport\") */\n  get viewportPropagateMouseMove() {\n    return this._viewportPropagateMouseMove;\n  }\n  set viewportPropagateMouseMove(disabled) {\n    this._viewportPropagateMouseMove = coerceBooleanProperty(disabled);\n  }\n  /** Disable auto-height */\n  get autoHeightDisabled() {\n    return this._autoHeightDisabled;\n  }\n  set autoHeightDisabled(disabled) {\n    this._autoHeightDisabled = coerceBooleanProperty(disabled);\n  }\n  /** Disable auto-width */\n  get autoWidthDisabled() {\n    return this._autoWidthDisabled;\n  }\n  set autoWidthDisabled(disabled) {\n    this._autoWidthDisabled = coerceBooleanProperty(disabled);\n  }\n  get nativeElement() {\n    return this.el.nativeElement;\n  }\n  constructor(el, zone, changeDetectorRef, dir, smoothScroll, manager) {\n    this.el = el;\n    this.zone = zone;\n    this.changeDetectorRef = changeDetectorRef;\n    this.dir = dir;\n    this.smoothScroll = smoothScroll;\n    this.manager = manager;\n    this._disabled = false;\n    this._sensorDisabled = this.manager.globalOptions.sensorDisabled;\n    this._pointerEventsDisabled = this.manager.globalOptions.pointerEventsDisabled;\n    this._autoHeightDisabled = this.manager.globalOptions.autoHeightDisabled;\n    this._autoWidthDisabled = this.manager.globalOptions.autoWidthDisabled;\n    this._viewportPropagateMouseMove = this.manager.globalOptions.viewportPropagateMouseMove;\n    /** A class forwarded to scrollable viewport element */\n    this.viewClass = this.manager.globalOptions.viewClass;\n    /** A class forwarded to the scrollbar track element */\n    this.trackClass = this.manager.globalOptions.trackClass;\n    /** A class forwarded to the scrollbar thumb element */\n    this.thumbClass = this.manager.globalOptions.thumbClass;\n    /** Minimum scrollbar thumb size */\n    this.minThumbSize = this.manager.globalOptions.minThumbSize;\n    /** The duration which the scrolling takes to reach its target when scrollbar rail is clicked */\n    this.trackClickScrollDuration = this.manager.globalOptions.trackClickScrollDuration;\n    /**\r\n     * Sets the pointer events method\r\n     * Use viewport pointer events  to handle dragging and track click (This makes scrolling work when mouse is over the scrollbar)\r\n     * Use scrollbar pointer events to handle dragging and track click\r\n     */\n    this.pointerEventsMethod = this.manager.globalOptions.pointerEventsMethod;\n    /**\r\n     * Sets the supported scroll track of the viewport, there are 3 options:\r\n     *\r\n     * - `vertical` Use both vertical and horizontal scrollbar\r\n     * - `horizontal` Use both vertical and horizontal scrollbar\r\n     * - `all` Use both vertical and horizontal scrollbar\r\n     */\n    this.track = this.manager.globalOptions.track;\n    /**\r\n     * When to show the scrollbar, and there are 3 options:\r\n     *\r\n     * - `native` (default) Scrollbar will be visible when viewport is scrollable like with native scrollbar\r\n     * - `hover` Scrollbars are hidden by default, only visible on scrolling or hovering\r\n     * - `always` Scrollbars are always shown even if the viewport is not scrollable\r\n     */\n    this.visibility = this.manager.globalOptions.visibility;\n    /**\r\n     *  Sets the appearance of the scrollbar, there are 2 options:\r\n     *\r\n     * - `standard` (default) scrollbar space will be reserved just like with native scrollbar.\r\n     * - `compact` scrollbar doesn't reserve any space, they are placed over the viewport.\r\n     */\n    this.appearance = this.manager.globalOptions.appearance;\n    /**\r\n     * Sets the position of each scrollbar, there are 4 options:\r\n     *\r\n     * - `native` (Default) Use the default position like in native scrollbar.\r\n     * - `invertY` Inverts vertical scrollbar position\r\n     * - `invertX` Inverts Horizontal scrollbar position\r\n     * - `invertAll` Inverts both scrollbar positions\r\n     */\n    this.position = this.manager.globalOptions.position;\n    /** Debounce interval for detecting changes via ResizeObserver */\n    this.sensorDebounce = this.manager.globalOptions.sensorDebounce;\n    /** Scroll Audit Time */\n    this.scrollAuditTime = this.manager.globalOptions.scrollAuditTime;\n    /** Steam that emits when scrollbar is updated */\n    this.updated = new EventEmitter();\n    /** Set of attributes added on the scrollbar wrapper */\n    this.state = {};\n    /** Stream that destroys components' observables */\n    this.destroyed = new Subject();\n  }\n  /**\r\n   * Update local state with each change detection\r\n   */\n  updateState() {\n    let verticalUsed = false;\n    let horizontalUsed = false;\n    let isVerticallyScrollable = false;\n    let isHorizontallyScrollable = false;\n    // Check if vertical scrollbar should be displayed\n    if (this.track === 'all' || this.track === 'vertical') {\n      isVerticallyScrollable = this.viewport.scrollHeight > this.viewport.clientHeight;\n      verticalUsed = this.visibility === 'always' || isVerticallyScrollable;\n    }\n    // Check if horizontal scrollbar should be displayed\n    if (this.track === 'all' || this.track === 'horizontal') {\n      isHorizontallyScrollable = this.viewport.scrollWidth > this.viewport.clientWidth;\n      horizontalUsed = this.visibility === 'always' || isHorizontallyScrollable;\n    }\n    // Update inner wrapper attributes\n    this.setState({\n      position: this.position,\n      track: this.track,\n      appearance: this.appearance,\n      visibility: this.visibility,\n      deactivated: this.disabled,\n      dir: this.dir.value,\n      pointerEventsMethod: this.pointerEventsMethod,\n      verticalUsed,\n      horizontalUsed,\n      isVerticallyScrollable,\n      isHorizontallyScrollable\n    });\n  }\n  setState(state) {\n    this.state = {\n      ...this.state,\n      ...state\n    };\n    this.changeDetectorRef.detectChanges();\n  }\n  getScrolledByDirection(property) {\n    let event;\n    return this.scrolled.pipe(tap(e => event = e), map(e => e.target[property]), pairwise(), filter(([prev, curr]) => prev !== curr), map(() => event));\n  }\n  /**\r\n   * Set hovered state if a scrollbar is being hovered\r\n   */\n  setHovered(hovered) {\n    this.zone.run(() => this.setState({\n      ...hovered\n    }));\n  }\n  /**\r\n   * Set dragging state if a scrollbar is being dragged\r\n   */\n  setDragging(dragging) {\n    this.zone.run(() => this.setState({\n      ...dragging\n    }));\n  }\n  /**\r\n   * Set clicked state if a scrollbar track is being clicked\r\n   */\n  setClicked(scrollbarClicked) {\n    this.zone.run(() => this.setState({\n      scrollbarClicked\n    }));\n  }\n  ngOnInit() {\n    // Set the viewport based on user choice\n    this.zone.runOutsideAngular(() => {\n      if (this.customViewPort) {\n        this.viewport = this.customViewPort;\n        this.defaultViewPort.setAsWrapper();\n      } else {\n        this.viewport = this.defaultViewPort;\n      }\n      // Activate the selected viewport\n      this.viewport.setAsViewport(this.viewClass);\n      let scrollStream = fromEvent(this.viewport.nativeElement, 'scroll', {\n        passive: true\n      });\n      // Throttle scroll event if 'scrollAuditTime' is set\n      scrollStream = this.scrollAuditTime ? scrollStream.pipe(auditTime(this.scrollAuditTime)) : scrollStream;\n      // Initialize scroll streams\n      this.scrolled = scrollStream.pipe(takeUntil(this.destroyed));\n      this.verticalScrolled = this.getScrolledByDirection('scrollTop');\n      this.horizontalScrolled = this.getScrolledByDirection('scrollLeft');\n    });\n  }\n  ngOnChanges(changes) {\n    // Update only when the viewport is initialized\n    if (this.viewport) {\n      this.update();\n    }\n  }\n  ngAfterViewInit() {\n    // Initial update\n    this.update();\n    // Update on dir changes\n    this.dir.change.pipe(tap(() => this.update()), takeUntil(this.destroyed)).subscribe();\n  }\n  ngOnDestroy() {\n    this.destroyed.next();\n    this.destroyed.complete();\n  }\n  /**\r\n   * Update local state and the internal scrollbar controls\r\n   */\n  update() {\n    if (!this.autoHeightDisabled) {\n      this.updateHeight();\n    }\n    if (!this.autoWidthDisabled) {\n      this.updateWidth();\n    }\n    // Re-evaluate the state after setting height or width\n    this.updateState();\n    this.updated.next();\n  }\n  /**\r\n   * Smooth scroll functions\r\n   */\n  scrollTo(options) {\n    return this.smoothScroll.scrollTo(this.viewport.nativeElement, options);\n  }\n  /**\r\n   * Scroll to element by reference or selector\r\n   */\n  scrollToElement(target, options) {\n    return this.smoothScroll.scrollToElement(this.viewport.nativeElement, target, options);\n  }\n  updateHeight() {\n    // Auto-height: Set component height to content height\n    if (this.appearance === 'standard' && this.scrollbarX) {\n      // if scrollbar-x is displayed in standard mode\n      this.nativeElement.style.height = `${this.viewport.contentHeight + this.scrollbarX.nativeElement.clientHeight}px`;\n    } else {\n      this.nativeElement.style.height = `${this.viewport.contentHeight}px`;\n    }\n  }\n  updateWidth() {\n    // Auto-width: Set component minWidth to content width\n    if (this.appearance === 'standard' && this.scrollbarY) {\n      // if scrollbar-y is displayed in standard mode\n      this.nativeElement.style.width = `${this.viewport.contentWidth + this.scrollbarY.nativeElement.clientWidth}px`;\n    } else {\n      this.nativeElement.style.width = `${this.viewport.contentWidth}px`;\n    }\n  }\n  static {\n    this.ɵfac = function NgScrollbar_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgScrollbar)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.Directionality), i0.ɵɵdirectiveInject(i2$1.SmoothScrollManager), i0.ɵɵdirectiveInject(ScrollbarManager));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NgScrollbar,\n      selectors: [[\"ng-scrollbar\"]],\n      contentQueries: function NgScrollbar_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, ScrollViewport, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.customViewPort = _t.first);\n        }\n      },\n      viewQuery: function NgScrollbar_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5, ElementRef);\n          i0.ɵɵviewQuery(_c1, 5, ElementRef);\n          i0.ɵɵviewQuery(ScrollViewport, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollbarY = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollbarX = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.defaultViewPort = _t.first);\n        }\n      },\n      hostVars: 2,\n      hostBindings: function NgScrollbar_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ng-scrollbar\", true);\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        sensorDisabled: \"sensorDisabled\",\n        pointerEventsDisabled: \"pointerEventsDisabled\",\n        viewportPropagateMouseMove: \"viewportPropagateMouseMove\",\n        autoHeightDisabled: \"autoHeightDisabled\",\n        autoWidthDisabled: \"autoWidthDisabled\",\n        viewClass: \"viewClass\",\n        trackClass: \"trackClass\",\n        thumbClass: \"thumbClass\",\n        minThumbSize: \"minThumbSize\",\n        trackClickScrollDuration: \"trackClickScrollDuration\",\n        pointerEventsMethod: \"pointerEventsMethod\",\n        track: \"track\",\n        visibility: \"visibility\",\n        appearance: \"appearance\",\n        position: \"position\",\n        sensorDebounce: \"sensorDebounce\",\n        scrollAuditTime: \"scrollAuditTime\"\n      },\n      outputs: {\n        updated: \"updated\"\n      },\n      exportAs: [\"ngScrollbar\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NgScrollbarBase,\n        useExisting: NgScrollbar\n      }]), i0.ɵɵNgOnChangesFeature],\n      ngContentSelectors: _c2,\n      decls: 6,\n      vars: 4,\n      consts: [[\"scrollbarX\", \"\"], [\"scrollbarY\", \"\"], [1, \"ng-scrollbar-wrapper\", 3, \"ngAttr\"], [1, \"ng-scroll-viewport-wrapper\", 3, \"resizeSensor\", \"sensorDebounce\", \"sensorDisabled\"], [\"scrollViewport\", \"\", \"hideNativeScrollbar\", \"\"], [4, \"ngIf\"]],\n      template: function NgScrollbar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n          i0.ɵɵlistener(\"resizeSensor\", function NgScrollbar_Template_div_resizeSensor_1_listener() {\n            return ctx.update();\n          });\n          i0.ɵɵelementStart(2, \"div\", 4)(3, \"div\");\n          i0.ɵɵprojection(4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(5, NgScrollbar_ng_container_5_Template, 3, 2, \"ng-container\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngAttr\", ctx.state);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"sensorDebounce\", ctx.sensorDebounce)(\"sensorDisabled\", ctx.sensorDisabled);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.disabled);\n        }\n      },\n      dependencies: [NgIf, NgAttr, ResizeSensor, ScrollViewport, HideNativeScrollbar, ScrollbarX, ScrollbarY],\n      styles: [\".ng-scrollbar-measure{scrollbar-width:none;-ms-overflow-style:none}  .ng-scrollbar-measure::-webkit-scrollbar{display:none}[_nghost-%COMP%]{--scrollbar-border-radius: 7px;--scrollbar-padding: 4px;--scrollbar-track-color: transparent;--scrollbar-thumb-color: rgba(0, 0, 0, .2);--scrollbar-thumb-hover-color: var(--scrollbar-thumb-color);--scrollbar-size: 5px;--scrollbar-hover-size: var(--scrollbar-size);--scrollbar-overscroll-behavior: initial;--scrollbar-transition-duration: .4s;--scrollbar-transition-delay: .8s;--scrollbar-thumb-transition: height ease-out .15s, width ease-out .15s;--scrollbar-track-transition: height ease-out .15s, width ease-out .15s;display:block;position:relative;height:100%;max-height:100%;max-width:100%;box-sizing:content-box!important}[_nghost-%COMP%] > .ng-scrollbar-wrapper[_ngcontent-%COMP%]{--scrollbar-total-size: calc(var(--scrollbar-size) + var(--scrollbar-padding) * 2);--vertical-scrollbar-size: var(--scrollbar-size);--horizontal-scrollbar-size: var(--scrollbar-size);--vertical-scrollbar-total-size: calc(var(--vertical-scrollbar-size) + var(--scrollbar-padding) * 2);--horizontal-scrollbar-total-size: calc(var(--horizontal-scrollbar-size) + var(--scrollbar-padding) * 2)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[verticalHovered=true][_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[verticalDragging=true][_ngcontent-%COMP%]{--vertical-scrollbar-size: var(--scrollbar-hover-size);--vertical-scrollbar-total-size: calc(var(--vertical-scrollbar-size) + var(--scrollbar-padding) * 2);cursor:default}[_nghost-%COMP%] > .ng-scrollbar-wrapper[horizontalHovered=true][_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[horizontalDragging=true][_ngcontent-%COMP%]{--horizontal-scrollbar-size: var(--scrollbar-hover-size);--horizontal-scrollbar-total-size: calc(var(--horizontal-scrollbar-size) + var(--scrollbar-padding) * 2);cursor:default}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{left:0;right:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{padding-right:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content{padding-right:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{left:var(--scrollbar-total-size);right:0}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{padding-left:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content{padding-left:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{left:var(--scrollbar-total-size);right:0}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{padding-left:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content{padding-left:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{left:0;right:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{padding-right:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content{padding-right:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{top:0;bottom:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{padding-bottom:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content{padding-bottom:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{top:var(--scrollbar-total-size);bottom:0}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{padding-top:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content{padding-top:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{scrollbar-width:none;-ms-overflow-style:none}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%]::-webkit-scrollbar, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport::-webkit-scrollbar{display:none}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][horizontalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-native-scrollbar-hider[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][horizontalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-native-scrollbar-hider{bottom:var(--native-scrollbar-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][verticalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-native-scrollbar-hider[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][verticalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-native-scrollbar-hider{left:0;right:var(--native-scrollbar-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][verticalUsed=true][dir=rtl][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-native-scrollbar-hider[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][verticalUsed=true][dir=rtl][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-native-scrollbar-hider{right:0;left:var(--native-scrollbar-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][visibility=hover][_ngcontent-%COMP%] > .scrollbar-control[_ngcontent-%COMP%]{opacity:0;transition-property:opacity;transition-duration:var(--scrollbar-transition-duration);transition-delay:var(--scrollbar-transition-delay)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][visibility=hover][_ngcontent-%COMP%]:hover > .scrollbar-control[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][visibility=hover][_ngcontent-%COMP%]:active > .scrollbar-control[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][visibility=hover][_ngcontent-%COMP%]:focus > .scrollbar-control[_ngcontent-%COMP%]{opacity:1;transition-duration:var(--scrollbar-transition-duration);transition-delay:0ms}[_nghost-%COMP%] > .ng-scrollbar-wrapper[horizontalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[horizontalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{overflow-x:auto;overflow-y:hidden}[_nghost-%COMP%] > .ng-scrollbar-wrapper[verticalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[verticalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{overflow-y:auto;overflow-x:hidden}[_nghost-%COMP%] > .ng-scrollbar-wrapper[verticalUsed=true][horizontalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[verticalUsed=true][horizontalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{overflow:auto}.ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{overflow:hidden}.ng-scroll-viewport[_ngcontent-%COMP%]{-webkit-overflow-scrolling:touch;contain:strict;will-change:scroll-position;overscroll-behavior:var(--scrollbar-overscroll-behavior)}  .ng-scroll-content{display:inline-block;min-width:100%}.ng-scrollbar-wrapper[_ngcontent-%COMP%], .ng-scroll-viewport-wrapper[_ngcontent-%COMP%], .ng-scroll-layer[_ngcontent-%COMP%],   .ng-scroll-viewport{position:absolute;inset:0}\", \".ng-scrollbar-wrapper[pointerEventsMethod=viewport]>.scrollbar-control{pointer-events:none}  .ng-scrollbar-wrapper[horizontalDragging=true]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,   .ng-scrollbar-wrapper[horizontalDragging=true]>.ng-scroll-viewport-wrapper>*>*>  .ng-scroll-viewport,   .ng-scrollbar-wrapper[verticalDragging=true]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,   .ng-scrollbar-wrapper[verticalDragging=true]>.ng-scroll-viewport-wrapper>*>*>  .ng-scroll-viewport,   .ng-scrollbar-wrapper[scrollbarClicked=true]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,   .ng-scrollbar-wrapper[scrollbarClicked=true]>.ng-scroll-viewport-wrapper>*>*>  .ng-scroll-viewport{-webkit-user-select:none;-moz-user-select:none;user-select:none}  .ng-scrollbar-wrapper>.scrollbar-control{position:absolute;display:flex;justify-content:center;align-items:center;transition:var(--scrollbar-track-transition)}  .ng-scrollbar-wrapper>.scrollbar-control[scrollable=false] .ng-scrollbar-thumb{display:none}  .ng-scrollbar-track{height:100%;width:100%;z-index:1;border-radius:var(--scrollbar-border-radius);background-color:var(--scrollbar-track-color);overflow:hidden;transition:var(--scrollbar-track-transition);cursor:default}  .ng-scrollbar-thumb{box-sizing:border-box;position:relative;border-radius:inherit;background-color:var(--scrollbar-thumb-color);transform:translateZ(0);transition:var(--scrollbar-thumb-transition)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgScrollbar, [{\n    type: Component,\n    args: [{\n      selector: 'ng-scrollbar',\n      exportAs: 'ngScrollbar',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class.ng-scrollbar]': 'true'\n      },\n      providers: [{\n        provide: NgScrollbarBase,\n        useExisting: NgScrollbar\n      }],\n      standalone: true,\n      imports: [NgIf, NgAttr, ResizeSensor, ScrollViewport, HideNativeScrollbar, ScrollbarX, ScrollbarY],\n      template: \"<div class=\\\"ng-scrollbar-wrapper\\\" [ngAttr]=\\\"state\\\">\\r\\n  <div class=\\\"ng-scroll-viewport-wrapper\\\"\\r\\n       (resizeSensor)=\\\"update()\\\"\\r\\n       [sensorDebounce]=\\\"sensorDebounce\\\"\\r\\n       [sensorDisabled]=\\\"sensorDisabled\\\">\\r\\n    <div scrollViewport\\r\\n         hideNativeScrollbar>\\r\\n      <div>\\r\\n        <ng-content></ng-content>\\r\\n      </div>\\r\\n    </div>\\r\\n  </div>\\r\\n  <ng-container *ngIf=\\\"!disabled\\\">\\r\\n    <scrollbar-x #scrollbarX\\r\\n                 *ngIf=\\\"state.horizontalUsed\\\"\\r\\n                 [attr.scrollable]=\\\"state.isHorizontallyScrollable\\\"\\r\\n                 [attr.fit]=\\\"state.verticalUsed\\\">\\r\\n    </scrollbar-x>\\r\\n    <scrollbar-y #scrollbarY\\r\\n                 *ngIf=\\\"state.verticalUsed\\\"\\r\\n                 [attr.scrollable]=\\\"state.isVerticallyScrollable\\\"\\r\\n                 [attr.fit]=\\\"state.horizontalUsed\\\">\\r\\n    </scrollbar-y>\\r\\n  </ng-container>\\r\\n</div>\\r\\n\\r\\n\",\n      styles: [\"::ng-deep .ng-scrollbar-measure{scrollbar-width:none;-ms-overflow-style:none}::ng-deep .ng-scrollbar-measure::-webkit-scrollbar{display:none}:host{--scrollbar-border-radius: 7px;--scrollbar-padding: 4px;--scrollbar-track-color: transparent;--scrollbar-thumb-color: rgba(0, 0, 0, .2);--scrollbar-thumb-hover-color: var(--scrollbar-thumb-color);--scrollbar-size: 5px;--scrollbar-hover-size: var(--scrollbar-size);--scrollbar-overscroll-behavior: initial;--scrollbar-transition-duration: .4s;--scrollbar-transition-delay: .8s;--scrollbar-thumb-transition: height ease-out .15s, width ease-out .15s;--scrollbar-track-transition: height ease-out .15s, width ease-out .15s;display:block;position:relative;height:100%;max-height:100%;max-width:100%;box-sizing:content-box!important}:host>.ng-scrollbar-wrapper{--scrollbar-total-size: calc(var(--scrollbar-size) + var(--scrollbar-padding) * 2);--vertical-scrollbar-size: var(--scrollbar-size);--horizontal-scrollbar-size: var(--scrollbar-size);--vertical-scrollbar-total-size: calc(var(--vertical-scrollbar-size) + var(--scrollbar-padding) * 2);--horizontal-scrollbar-total-size: calc(var(--horizontal-scrollbar-size) + var(--scrollbar-padding) * 2)}:host>.ng-scrollbar-wrapper[verticalHovered=true],:host>.ng-scrollbar-wrapper[verticalDragging=true]{--vertical-scrollbar-size: var(--scrollbar-hover-size);--vertical-scrollbar-total-size: calc(var(--vertical-scrollbar-size) + var(--scrollbar-padding) * 2);cursor:default}:host>.ng-scrollbar-wrapper[horizontalHovered=true],:host>.ng-scrollbar-wrapper[horizontalDragging=true]{--horizontal-scrollbar-size: var(--scrollbar-hover-size);--horizontal-scrollbar-total-size: calc(var(--horizontal-scrollbar-size) + var(--scrollbar-padding) * 2);cursor:default}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=scrollbar]>.ng-scroll-viewport-wrapper{left:0;right:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport{padding-right:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport>.ng-scroll-content,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport>.ng-scroll-content{padding-right:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=scrollbar]>.ng-scroll-viewport-wrapper{left:var(--scrollbar-total-size);right:0}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport{padding-left:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport>.ng-scroll-content,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport>.ng-scroll-content{padding-left:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=scrollbar]>.ng-scroll-viewport-wrapper,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=scrollbar]>.ng-scroll-viewport-wrapper{left:var(--scrollbar-total-size);right:0}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport{padding-left:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport>.ng-scroll-content,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport>.ng-scroll-content,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport>.ng-scroll-content,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport>.ng-scroll-content{padding-left:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=scrollbar]>.ng-scroll-viewport-wrapper,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=scrollbar]>.ng-scroll-viewport-wrapper{left:0;right:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport{padding-right:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport>.ng-scroll-content,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport>.ng-scroll-content,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport>.ng-scroll-content,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport>.ng-scroll-content{padding-right:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=scrollbar]>.ng-scroll-viewport-wrapper{top:0;bottom:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport{padding-bottom:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport>.ng-scroll-content,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport>.ng-scroll-content{padding-bottom:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=scrollbar]>.ng-scroll-viewport-wrapper,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=scrollbar]>.ng-scroll-viewport-wrapper{top:var(--scrollbar-total-size);bottom:0}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport{padding-top:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport>.ng-scroll-content,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport>.ng-scroll-content,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport>.ng-scroll-content,:host>.ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=viewport]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport>.ng-scroll-content{padding-top:var(--scrollbar-total-size)}:host>.ng-scrollbar-wrapper[deactivated=false]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[deactivated=false]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport{scrollbar-width:none;-ms-overflow-style:none}:host>.ng-scrollbar-wrapper[deactivated=false]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport::-webkit-scrollbar,:host>.ng-scrollbar-wrapper[deactivated=false]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport::-webkit-scrollbar{display:none}:host>.ng-scrollbar-wrapper[deactivated=false][horizontalUsed=true]>.ng-scroll-viewport-wrapper>.ng-native-scrollbar-hider,:host>.ng-scrollbar-wrapper[deactivated=false][horizontalUsed=true]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-native-scrollbar-hider{bottom:var(--native-scrollbar-size)}:host>.ng-scrollbar-wrapper[deactivated=false][verticalUsed=true]>.ng-scroll-viewport-wrapper>.ng-native-scrollbar-hider,:host>.ng-scrollbar-wrapper[deactivated=false][verticalUsed=true]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-native-scrollbar-hider{left:0;right:var(--native-scrollbar-size)}:host>.ng-scrollbar-wrapper[deactivated=false][verticalUsed=true][dir=rtl]>.ng-scroll-viewport-wrapper>.ng-native-scrollbar-hider,:host>.ng-scrollbar-wrapper[deactivated=false][verticalUsed=true][dir=rtl]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-native-scrollbar-hider{right:0;left:var(--native-scrollbar-size)}:host>.ng-scrollbar-wrapper[deactivated=false][visibility=hover]>.scrollbar-control{opacity:0;transition-property:opacity;transition-duration:var(--scrollbar-transition-duration);transition-delay:var(--scrollbar-transition-delay)}:host>.ng-scrollbar-wrapper[deactivated=false][visibility=hover]:hover>.scrollbar-control,:host>.ng-scrollbar-wrapper[deactivated=false][visibility=hover]:active>.scrollbar-control,:host>.ng-scrollbar-wrapper[deactivated=false][visibility=hover]:focus>.scrollbar-control{opacity:1;transition-duration:var(--scrollbar-transition-duration);transition-delay:0ms}:host>.ng-scrollbar-wrapper[horizontalUsed=true]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[horizontalUsed=true]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport{overflow-x:auto;overflow-y:hidden}:host>.ng-scrollbar-wrapper[verticalUsed=true]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[verticalUsed=true]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport{overflow-y:auto;overflow-x:hidden}:host>.ng-scrollbar-wrapper[verticalUsed=true][horizontalUsed=true]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,:host>.ng-scrollbar-wrapper[verticalUsed=true][horizontalUsed=true]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport{overflow:auto}.ng-scroll-viewport-wrapper{overflow:hidden}.ng-scroll-viewport{-webkit-overflow-scrolling:touch;contain:strict;will-change:scroll-position;overscroll-behavior:var(--scrollbar-overscroll-behavior)}::ng-deep .ng-scroll-content{display:inline-block;min-width:100%}.ng-scrollbar-wrapper,.ng-scroll-viewport-wrapper,.ng-scroll-layer,::ng-deep .ng-scroll-viewport{position:absolute;inset:0}\\n\", \"::ng-deep .ng-scrollbar-wrapper[pointerEventsMethod=viewport]>.scrollbar-control{pointer-events:none}::ng-deep .ng-scrollbar-wrapper[horizontalDragging=true]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,::ng-deep .ng-scrollbar-wrapper[horizontalDragging=true]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport,::ng-deep .ng-scrollbar-wrapper[verticalDragging=true]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,::ng-deep .ng-scrollbar-wrapper[verticalDragging=true]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport,::ng-deep .ng-scrollbar-wrapper[scrollbarClicked=true]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,::ng-deep .ng-scrollbar-wrapper[scrollbarClicked=true]>.ng-scroll-viewport-wrapper>*>*>::ng-deep .ng-scroll-viewport{-webkit-user-select:none;-moz-user-select:none;user-select:none}::ng-deep .ng-scrollbar-wrapper>.scrollbar-control{position:absolute;display:flex;justify-content:center;align-items:center;transition:var(--scrollbar-track-transition)}::ng-deep .ng-scrollbar-wrapper>.scrollbar-control[scrollable=false] .ng-scrollbar-thumb{display:none}::ng-deep .ng-scrollbar-track{height:100%;width:100%;z-index:1;border-radius:var(--scrollbar-border-radius);background-color:var(--scrollbar-track-color);overflow:hidden;transition:var(--scrollbar-track-transition);cursor:default}::ng-deep .ng-scrollbar-thumb{box-sizing:border-box;position:relative;border-radius:inherit;background-color:var(--scrollbar-thumb-color);transform:translateZ(0);transition:var(--scrollbar-thumb-transition)}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i3.Directionality\n    }, {\n      type: i2$1.SmoothScrollManager\n    }, {\n      type: ScrollbarManager\n    }];\n  }, {\n    disabled: [{\n      type: Input\n    }],\n    sensorDisabled: [{\n      type: Input\n    }],\n    pointerEventsDisabled: [{\n      type: Input\n    }],\n    viewportPropagateMouseMove: [{\n      type: Input\n    }],\n    autoHeightDisabled: [{\n      type: Input\n    }],\n    autoWidthDisabled: [{\n      type: Input\n    }],\n    viewClass: [{\n      type: Input\n    }],\n    trackClass: [{\n      type: Input\n    }],\n    thumbClass: [{\n      type: Input\n    }],\n    minThumbSize: [{\n      type: Input\n    }],\n    trackClickScrollDuration: [{\n      type: Input\n    }],\n    pointerEventsMethod: [{\n      type: Input\n    }],\n    track: [{\n      type: Input\n    }],\n    visibility: [{\n      type: Input\n    }],\n    appearance: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    sensorDebounce: [{\n      type: Input\n    }],\n    scrollAuditTime: [{\n      type: Input\n    }],\n    updated: [{\n      type: Output\n    }],\n    scrollbarY: [{\n      type: ViewChild,\n      args: ['scrollbarY', {\n        read: ElementRef\n      }]\n    }],\n    scrollbarX: [{\n      type: ViewChild,\n      args: ['scrollbarX', {\n        read: ElementRef\n      }]\n    }],\n    defaultViewPort: [{\n      type: ViewChild,\n      args: [ScrollViewport, {\n        static: true\n      }]\n    }],\n    customViewPort: [{\n      type: ContentChild,\n      args: [ScrollViewport, {\n        static: true\n      }]\n    }]\n  });\n})();\nclass NgScrollbarModule {\n  static {\n    this.ɵfac = function NgScrollbarModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || NgScrollbarModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NgScrollbarModule,\n      imports: [NgScrollbar, ScrollViewport],\n      exports: [NgScrollbar, ScrollViewport]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgScrollbarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NgScrollbar, ScrollViewport],\n      exports: [NgScrollbar, ScrollViewport]\n    }]\n  }], null, null);\n})();\n\n/*\r\n * Public API Surface of ngx-scrollbar\r\n */\n\n/**\r\n * Generated bundle index. Do not edit.\r\n */\n\nexport { NG_SCROLLBAR_OPTIONS, NgScrollbar, NgScrollbarModule, ScrollViewport, ScrollbarManager };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,SAAS,sBAAsB,OAAO;AAClC,SAAO,SAAS,QAAQ,GAAG,KAAK,OAAO;AAC3C;;;ACUA,IAAM,eAAe,IAAI,eAAe,eAAe;AAAA,EACrD,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,uBAAuB;AAC9B,SAAO,OAAO,QAAQ;AACxB;AAGA,IAAM,qBAAqB;AAE3B,SAAS,uBAAuB,UAAU;AACxC,QAAM,QAAQ,UAAU,YAAY,KAAK;AACzC,MAAI,UAAU,UAAU,OAAO,cAAc,eAAe,WAAW,UAAU;AAC/E,WAAO,mBAAmB,KAAK,UAAU,QAAQ,IAAI,QAAQ;AAAA,EAC/D;AACA,SAAO,UAAU,QAAQ,QAAQ;AACnC;AAKA,IAAM,iBAAN,MAAM,gBAAe;AAAA;AAAA,EAEnB,IAAI,QAAQ;AACV,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,OAAO,KAAK;AAAA;AAAA,EAE1B,SAAS,IAAI,aAAa;AAAA,EAC1B,cAAc;AACZ,UAAM,YAAY,OAAO,cAAc;AAAA,MACrC,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,WAAW;AACb,YAAM,UAAU,UAAU,OAAO,UAAU,KAAK,MAAM;AACtD,YAAM,UAAU,UAAU,kBAAkB,UAAU,gBAAgB,MAAM;AAC5E,WAAK,YAAY,IAAI,uBAAuB,WAAW,WAAW,KAAK,CAAC;AAAA,IAC1E;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,gBAAe;AAAA,IACxB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;AC1EH,IAAM,MAAN,MAAM,KAAI;AAAA;AAAA,EAER,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA,EAEA,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,IAAI,MAAM;AACR,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA,EACA,IAAI,IAAI,OAAO;AACb,UAAM,gBAAgB,KAAK,YAAY;AAIvC,SAAK,YAAY,IAAI,uBAAuB,KAAK,CAAC;AAClD,SAAK,UAAU;AACf,QAAI,kBAAkB,KAAK,YAAY,KAAK,KAAK,gBAAgB;AAC/D,WAAK,OAAO,KAAK,KAAK,YAAY,CAAC;AAAA,IACrC;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc,OAAO,KAAK;AAAA;AAAA,EAE1B,qBAAqB;AACnB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA,EACA,OAAO,OAAO,SAAS,YAAY,mBAAmB;AACpD,WAAO,KAAK,qBAAqB,MAAK;AAAA,EACxC;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,OAAO,EAAE,CAAC;AAAA,IAC3B,UAAU;AAAA,IACV,cAAc,SAAS,iBAAiB,IAAI,KAAK;AAC/C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,OAAO,IAAI,OAAO;AAAA,MACnC;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,IACP;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,KAAK;AAAA,IAChB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,CAAC;AAAA,EACL,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,KAAK,CAAC;AAAA,IAC5E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,GAAG;AAAA,IACb,SAAS,CAAC,GAAG;AAAA,EACf,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,GAAG;AAAA,MACb,SAAS,CAAC,GAAG;AAAA,IACf,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AChHH,IAAI;AAAA,CACH,SAAUA,oBAAmB;AAK1B,EAAAA,mBAAkBA,mBAAkB,QAAQ,IAAI,CAAC,IAAI;AAKrD,EAAAA,mBAAkBA,mBAAkB,SAAS,IAAI,CAAC,IAAI;AAKtD,EAAAA,mBAAkBA,mBAAkB,UAAU,IAAI,CAAC,IAAI;AAC3D,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,IAAI;AAsCJ,SAAS,uBAAuB;AAE5B,MAAI,OAAO,aAAa,YAAY,CAAC,UAAU;AAC3C,WAAO,kBAAkB;AAAA,EAC7B;AACA,MAAI,qBAAqB,MAAM;AAE3B,UAAM,kBAAkB,SAAS,cAAc,KAAK;AACpD,UAAM,iBAAiB,gBAAgB;AACvC,oBAAgB,MAAM;AACtB,mBAAe,QAAQ;AACvB,mBAAe,WAAW;AAC1B,mBAAe,aAAa;AAC5B,mBAAe,gBAAgB;AAC/B,mBAAe,WAAW;AAC1B,UAAM,UAAU,SAAS,cAAc,KAAK;AAC5C,UAAM,eAAe,QAAQ;AAC7B,iBAAa,QAAQ;AACrB,iBAAa,SAAS;AACtB,oBAAgB,YAAY,OAAO;AACnC,aAAS,KAAK,YAAY,eAAe;AACzC,wBAAoB,kBAAkB;AAItC,QAAI,gBAAgB,eAAe,GAAG;AAKlC,sBAAgB,aAAa;AAC7B,0BACI,gBAAgB,eAAe,IAAI,kBAAkB,UAAU,kBAAkB;AAAA,IACzF;AACA,oBAAgB,OAAO;AAAA,EAC3B;AACA,SAAO;AACX;;;ACvFA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACTH,IAAM,oBAAoB;AAC1B,IAAM,mBAAmB;AACzB,IAAM,wBAAwB;AAC9B,IAAM,6BAA6B;AACnC,IAAM,mBAAmB;AACzB,IAAM,kBAAkB,KAAO,mBAAmB;AAClD,IAAM,wBAAwB,OAAO,iBAAiB;AACtD,SAAS,EAAE,KAAK,KAAK;AACnB,SAAO,IAAM,IAAM,MAAM,IAAM;AACjC;AACA,SAAS,EAAE,KAAK,KAAK;AACnB,SAAO,IAAM,MAAM,IAAM;AAC3B;AACA,SAAS,EAAE,KAAK;AACd,SAAO,IAAM;AACf;AAEA,SAAS,WAAW,IAAI,KAAK,KAAK;AAChC,WAAS,EAAE,KAAK,GAAG,IAAI,KAAK,EAAE,KAAK,GAAG,KAAK,KAAK,EAAE,GAAG,KAAK;AAC5D;AAEA,SAAS,SAAS,IAAI,KAAK,KAAK;AAC9B,SAAO,IAAM,EAAE,KAAK,GAAG,IAAI,KAAK,KAAK,IAAM,EAAE,KAAK,GAAG,IAAI,KAAK,EAAE,GAAG;AACrE;AACA,SAAS,gBAAgB,IAAI,IAAI,IAAI,KAAK,KAAK;AAC7C,MAAI,UACF,UACA,IAAI;AACN,KAAG;AACD,eAAW,MAAM,KAAK,MAAM;AAC5B,eAAW,WAAW,UAAU,KAAK,GAAG,IAAI;AAC5C,QAAI,WAAW,GAAK;AAClB,WAAK;AAAA,IACP,OAAO;AACL,WAAK;AAAA,IACP;AAAA,EACF,SAAS,KAAK,IAAI,QAAQ,IAAI,yBAAyB,EAAE,IAAI;AAC7D,SAAO;AACT;AACA,SAAS,qBAAqB,IAAI,SAAS,KAAK,KAAK;AACnD,WAAS,IAAI,GAAG,IAAI,mBAAmB,EAAE,GAAG;AAC1C,QAAI,eAAe,SAAS,SAAS,KAAK,GAAG;AAC7C,QAAI,iBAAiB,GAAK;AACxB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,WAAW,SAAS,KAAK,GAAG,IAAI;AAC/C,eAAW,WAAW;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,aAAa,GAAG;AACvB,SAAO;AACT;AACA,SAAS,OAAO,KAAK,KAAK,KAAK,KAAK;AAClC,MAAI,EAAE,KAAK,OAAO,OAAO,KAAK,KAAK,OAAO,OAAO,IAAI;AACnD,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC3D;AACA,MAAI,QAAQ,OAAO,QAAQ,KAAK;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,eAAe,wBAAwB,IAAI,aAAa,gBAAgB,IAAI,IAAI,MAAM,gBAAgB;AAC1G,WAAS,IAAI,GAAG,IAAI,kBAAkB,EAAE,GAAG;AACzC,iBAAa,CAAC,IAAI,WAAW,IAAI,iBAAiB,KAAK,GAAG;AAAA,EAC5D;AACA,WAAS,SAAS,IAAI;AACpB,QAAI,gBAAgB;AACpB,QAAI,gBAAgB;AACpB,QAAI,aAAa,mBAAmB;AACpC,WAAO,kBAAkB,cAAc,aAAa,aAAa,KAAK,IAAI,EAAE,eAAe;AACzF,uBAAiB;AAAA,IACnB;AACA,MAAE;AAEF,QAAI,QAAQ,KAAK,aAAa,aAAa,MAAM,aAAa,gBAAgB,CAAC,IAAI,aAAa,aAAa;AAC7G,QAAI,YAAY,gBAAgB,OAAO;AACvC,QAAI,eAAe,SAAS,WAAW,KAAK,GAAG;AAC/C,QAAI,gBAAgB,kBAAkB;AACpC,aAAO,qBAAqB,IAAI,WAAW,KAAK,GAAG;AAAA,IACrD,WAAW,iBAAiB,GAAK;AAC/B,aAAO;AAAA,IACT,OAAO;AACL,aAAO,gBAAgB,IAAI,eAAe,gBAAgB,iBAAiB,KAAK,GAAG;AAAA,IACrF;AAAA,EACF;AACA,SAAO,SAAS,aAAa,GAAG;AAE9B,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,QAAI,MAAM,GAAG;AACX,aAAO;AAAA,IACT;AACA,WAAO,WAAW,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,EACzC;AACF;AAEA,IAAM,wBAAwB,IAAI,eAAe,uBAAuB;AAGxE,IAAM,uBAAN,MAAM,qBAAoB;AAAA,EACxB,IAAI,KAAK;AACP,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACT,WAAO,KAAK,GAAG,eAAe,KAAK,GAAG,YAAY,MAAM,KAAK,GAAG,YAAY,IAAI,KAAK,KAAK,GAAG,WAAW,IAAI,KAAK;AAAA,EACnH;AAAA,EACA,YAAY,WAAW,WAAW,sBAAsB;AACtD,SAAK,YAAY;AACjB,SAAK,YAAY;AAKjB,SAAK,kBAAkB,oBAAI,IAAI;AAC/B,SAAK,kBAAkB;AAAA,MACrB,UAAU;AAAA,MACV,QAAQ;AAAA,QACN,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,MACN;AAAA,OACG;AAAA,EAEP;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,IAAI,GAAG,GAAG;AACvB,OAAG,aAAa;AAChB,OAAG,YAAY;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,IAAI,QAAQ;AACtB,QAAI,OAAO,OAAO,UAAU;AAC1B,cAAQ,UAAU,KAAK,WAAW,cAAc,EAAE;AAAA,IACpD;AACA,WAAO,cAAc,EAAE;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,IAAI;AACpB,QAAI,KAAK,gBAAgB,IAAI,EAAE,GAAG;AAChC,WAAK,gBAAgB,IAAI,EAAE,EAAE,KAAK;AAAA,IACpC;AACA,WAAO,KAAK,gBAAgB,IAAI,IAAI,IAAI,QAAQ,CAAC,EAAE,IAAI,EAAE;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,SAAS,WAAW,SAAS;AACvC,QAAI,QAAQ,aAAa,QAAQ,KAAK,QAAQ,aAAa,QAAQ,GAAG;AACpE,aAAO;AAAA,IACT;AACA,cAAU,KAAK;AACf,YAAQ;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,IAAI,WAAW;AAC1B,WAAO,MAAM,UAAU,IAAI,SAAS;AAAA,MAClC,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC,GAAG,UAAU,IAAI,aAAa;AAAA,MAC7B,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC,GAAG,SAAS,EAAE,KAAK,KAAK,CAAC,CAAC;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,IAAI,WAAW;AACtB,cAAU,SAAS;AACnB,SAAK,gBAAgB,OAAO,EAAE;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,SAAS;AACb,WAAO,IAAI,WAAW,gBAAc;AAClC,UAAI,WAAW,KAAK,KAAK,IAAI,QAAQ,aAAa,QAAQ;AAE1D,gBAAU,UAAU,IAAI,IAAI;AAE5B,YAAM,QAAQ,QAAQ,OAAO,OAAO;AACpC,cAAQ,WAAW,QAAQ,UAAU,QAAQ,IAAI,QAAQ,UAAU;AACnE,cAAQ,WAAW,QAAQ,UAAU,QAAQ,IAAI,QAAQ,UAAU;AACnE,WAAK,eAAe,QAAQ,YAAY,QAAQ,UAAU,QAAQ,QAAQ;AAE1E,8BAAwB,SAAS,MAAM,WAAW,KAAK,OAAO,CAAC;AAAA,IACjE,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB,IAAI,SAAS;AACjC,QAAI,CAAC,QAAQ,UAAU;AACrB,WAAK,eAAe,IAAI,QAAQ,MAAM,QAAQ,GAAG;AACjD,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAEA,UAAM,YAAY,KAAK,kBAAkB,EAAE;AAC3C,UAAM,UAAU;AAAA,MACd,YAAY;AAAA,MACZ,WAAW,KAAK,KAAK;AAAA,MACrB,QAAQ,GAAG;AAAA,MACX,QAAQ,GAAG;AAAA,MACX,GAAG,QAAQ,QAAQ,OAAO,GAAG,aAAa,CAAC,CAAC,QAAQ;AAAA,MACpD,GAAG,QAAQ,OAAO,OAAO,GAAG,YAAY,CAAC,CAAC,QAAQ;AAAA,MAClD,UAAU,QAAQ;AAAA,MAClB,QAAQ,OAAO,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,EAAE;AAAA,IAC3F;AACA,WAAO,IAAI,QAAQ,aAAW;AAE5B,SAAG,IAAI,EAAE,KAAK,OAAO,MAAM,KAAK,MAAM,OAAO,EAAE,KAAK,UAAU,iBAAe,KAAK,YAAY,aAAa,WAAW,OAAO,CAAC,CAAC,CAAC,GAAG,UAAU,KAAK,aAAa,IAAI,SAAS,CAAC,GAAG,SAAS,MAAM,KAAK,SAAS,IAAI,SAAS,CAAC,CAAC,EAAE,UAAU;AAAA,IAC1O,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,SAAS,YAAY,eAAe;AAClC,QAAI,kBAAkB,KAAK,SAAS,GAAG;AACrC,YAAM,KAAK,KAAK,YAAY,UAAU;AACtC,YAAM,QAAQ,iBAAiB,EAAE,EAAE,cAAc;AACjD,YAAMC,qBAAoB,qBAAqB;AAC/C,YAAM,UAAU,iDACX,KAAK,kBACL,gBACA;AAAA;AAAA,QAED,MAAM,cAAc,QAAQ,OAAO,QAAQ,cAAc,MAAM,cAAc,QAAQ,cAAc;AAAA,QACnG,OAAO,cAAc,SAAS,OAAO,QAAQ,cAAc,QAAQ,cAAc,MAAM,cAAc;AAAA,MACvG;AAGF,UAAI,QAAQ,UAAU,MAAM;AAC1B,gBAAQ,MAAM,GAAG,eAAe,GAAG,eAAe,QAAQ;AAAA,MAC5D;AAEA,UAAI,SAASA,uBAAsB,GAAkC;AACnE,YAAI,QAAQ,QAAQ,MAAM;AACxB,kBAAQ,QAAQ,GAAG,cAAc,GAAG,cAAc,QAAQ;AAAA,QAC5D;AACA,YAAIA,uBAAsB,GAAoC;AAC5D,kBAAQ,OAAO,QAAQ;AAAA,QACzB,WAAWA,uBAAsB,GAAmC;AAClE,kBAAQ,OAAO,QAAQ,QAAQ,CAAC,QAAQ,QAAQ,QAAQ;AAAA,QAC1D;AAAA,MACF,OAAO;AACL,YAAI,QAAQ,SAAS,MAAM;AACzB,kBAAQ,OAAO,GAAG,cAAc,GAAG,cAAc,QAAQ;AAAA,QAC3D;AAAA,MACF;AACA,aAAO,KAAK,sBAAsB,IAAI,OAAO;AAAA,IAC/C;AACA,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,YAAY,QAAQ,gBAAgB,CAAC,GAAG;AACtD,UAAM,eAAe,KAAK,YAAY,UAAU;AAChD,UAAM,WAAW,KAAK,YAAY,QAAQ,YAAY;AACtD,UAAM,UAAU,kCACX,gBACA;AAAA,MACD,MAAM,SAAS,cAAc,cAAc,QAAQ;AAAA,MACnD,KAAK,SAAS,aAAa,cAAc,OAAO;AAAA,IAClD;AAEF,WAAO,WAAW,KAAK,SAAS,cAAc,OAAO,IAAI,QAAQ,QAAQ;AAAA,EAC3E;AAaF;AAXI,qBAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,SAAO,KAAK,qBAAqB,sBAAwB,SAAS,QAAQ,GAAM,SAAS,WAAW,GAAM,SAAS,uBAAuB,CAAC,CAAC;AAC9I;AAGA,qBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,qBAAoB;AAAA,EAC7B,YAAY;AACd,CAAC;AAlML,IAAM,sBAAN;AAAA,CAqMC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,WAAW;AAAA,MACpB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,GAAG;AAAA,QACD,MAAM;AAAA,QACN,MAAM,CAAC,qBAAqB;AAAA,MAC9B,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,gBAAN,MAAM,cAAa;AAAA,EACjB,YAAY,SAAS,cAAc;AACjC,SAAK,UAAU;AACf,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,SAAS,SAAS;AAChB,WAAO,KAAK,aAAa,SAAS,KAAK,SAAS,OAAO;AAAA,EACzD;AAAA,EACA,gBAAgB,QAAQ,SAAS;AAC/B,WAAO,KAAK,aAAa,gBAAgB,KAAK,SAAS,QAAQ,OAAO;AAAA,EACxE;AAaF;AAXI,cAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,SAAO,KAAK,qBAAqB,eAAiB,kBAAqB,UAAU,GAAM,kBAAkB,mBAAmB,CAAC;AAC/H;AAGA,cAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,EAC/D,UAAU,CAAC,cAAc;AAC3B,CAAC;AArBL,IAAM,eAAN;AAAA,CAwBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;;;AClXH,IAAM,MAAM,CAAC,YAAY;AACzB,IAAM,MAAM,CAAC,YAAY;AACzB,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,eAAe,MAAM,CAAC;AAAA,EACxC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,cAAc,OAAO,MAAM,wBAAwB,EAAE,OAAO,OAAO,MAAM,YAAY;AAAA,EACtG;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,eAAe,MAAM,CAAC;AAAA,EACxC;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,cAAc,OAAO,MAAM,sBAAsB,EAAE,OAAO,OAAO,MAAM,cAAc;AAAA,EACtG;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,mDAAmD,GAAG,GAAG,eAAe,CAAC;AACxK,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,MAAM,cAAc;AACjD,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,MAAM,YAAY;AAAA,EACjD;AACF;AACA,IAAM,UAAN,MAAM,QAAO;AAAA,EACX,YAAY,IAAI;AACd,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,IAAI,OAAO,OAAO;AAChB,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,GAAG;AAChD,WAAK,GAAG,cAAc,aAAa,KAAK,KAAK;AAAA,IAC/C;AAAA,EACF;AAeF;AAbI,QAAK,OAAO,SAAS,eAAe,mBAAmB;AACrD,SAAO,KAAK,qBAAqB,SAAW,kBAAqB,UAAU,CAAC;AAC9E;AAGA,QAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,UAAU,EAAE,CAAC;AAAA,EAC9B,QAAQ;AAAA,IACN,QAAQ;AAAA,EACV;AACF,CAAC;AArBL,IAAM,SAAN;AAAA,CAwBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,iBAAiB,KAAK;AAC7B,SAAO,IAAI,MAAM;AACf,QAAI,gBAAgB,MAAM;AAAA,EAC5B,CAAC;AACH;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,MAAM;AACf,QAAI,gBAAgB;AAAA,EACtB,CAAC;AACH;AACA,SAAS,kBAAkB;AACzB,SAAO,IAAI,OAAK,EAAE,gBAAgB,CAAC;AACrC;AAIA,SAAS,eAAe,GAAG,MAAM;AAC/B,SAAO,EAAE,WAAW,KAAK,QAAQ,EAAE,WAAW,KAAK,OAAO,KAAK,SAAS,EAAE,WAAW,KAAK,OAAO,EAAE,WAAW,KAAK,MAAM,KAAK;AAChI;AACA,IAAM,kBAAN,MAAM,gBAAe;AAAA;AAAA,EAEnB,IAAI,eAAe;AACjB,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,cAAc,KAAK;AAAA,EACjC;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,eAAe,KAAK;AAAA,EAClC;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,uBAAuB,gBAAgB;AAAA,EACrD;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,uBAAuB,eAAe;AAAA,EACpD;AAAA,EACA,YAAY,UAAU;AACpB,SAAK,WAAW;AAChB,SAAK,gBAAgB,SAAS;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB,WAAW,WAAW;AAC1C,SAAK,UAAU,IAAI,WAAW,gBAAc;AAE1C,YAAM,kBAAkB,UAAU,KAAK,eAAe,aAAa;AAAA,QACjE,SAAS;AAAA,MACX,CAAC;AACD,YAAM,YAAY,YAAY,kBAAkB,gBAAgB,KAAK,gBAAgB,CAAC;AAEtF,YAAM,aAAa,UAAU,KAAK,eAAe,cAAc;AAAA,QAC7D,SAAS;AAAA,MACX,CAAC,EAAE,KAAK,IAAI,MAAM,KAAK,CAAC;AACxB,YAAM,WAAW,UAAU,EAAE,KAAK,IAAI,OAAK,WAAW,KAAK,CAAC,CAAC,GAAG,UAAU,SAAS,CAAC,EAAE,UAAU;AAAA,IAClG,CAAC;AACD,SAAK,UAAU,IAAI,WAAW,gBAAc;AAC1C,YAAM,YAAY,UAAU,KAAK,eAAe,aAAa;AAAA,QAC3D,SAAS;AAAA,MACX,CAAC,EAAE,KAAK,IAAI,OAAK,WAAW,KAAK,CAAC,CAAC,CAAC;AACpC,YAAM,UAAU,UAAU,KAAK,eAAe,WAAW;AAAA,QACvD,SAAS;AAAA,MACX,CAAC,EAAE,KAAK,IAAI,MAAM,WAAW,KAAK,KAAK,CAAC,CAAC;AACzC,gBAAU,KAAK,UAAU,MAAM,OAAO,GAAG,UAAU,SAAS,CAAC,EAAE,UAAU;AAAA,IAC3E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AAEb,SAAK,cAAc,YAAY;AAC/B,QAAI,KAAK,cAAc,mBAAmB;AACxC,WAAK,cAAc,kBAAkB,YAAY;AAAA,IACnD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,iBAAiB;AAC7B,SAAK,cAAc,aAAa,iDAAiD,eAAe;AAEhG,QAAI,KAAK,cAAc,mBAAmB;AACxC,WAAK,wBAAwB,KAAK,cAAc;AAChD,WAAK,sBAAsB,UAAU,IAAI,mBAAmB;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,OAAO;AACf,SAAK,cAAc,YAAY;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,OAAO;AACf,SAAK,cAAc,aAAa;AAAA,EAClC;AAYF;AAVI,gBAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,SAAO,KAAK,qBAAqB,iBAAmB,kBAAqB,UAAU,CAAC;AACtF;AAGA,gBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;AACxC,CAAC;AA1GL,IAAM,iBAAN;AAAA,CA6GC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,mBAAN,MAAM,iBAAgB;AAYtB;AAVI,iBAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,kBAAiB;AACpD;AAGA,iBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,YAAY;AACd,CAAC;AAVL,IAAM,kBAAN;AAAA,CAaC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,gBAAN,MAAM,cAAa;AAAA;AAAA,EAEjB,IAAI,UAAU;AACZ,UAAM,YAAY,UAAU,KAAK,cAAc,aAAa;AAAA,MAC1D,SAAS;AAAA,IACX,CAAC,EAAE,KAAK,gBAAgB,GAAG,iBAAiB,KAAK,QAAQ,CAAC;AAC1D,UAAM,UAAU,UAAU,KAAK,UAAU,WAAW;AAAA,MAClD,SAAS;AAAA,IACX,CAAC,EAAE,KAAK,gBAAgB,GAAG,gBAAgB,KAAK,QAAQ,GAAG,UAAU,MAAM,KAAK,CAAC;AACjF,WAAO,MAAM,WAAW,OAAO;AAAA,EACjC;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,aAAa,sBAAsB;AAAA,EACjD;AAAA,EACA,YAAY,KAAK,cAAcC,WAAU;AACvC,SAAK,MAAM;AACX,SAAK,eAAe;AACpB,SAAK,WAAWA;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,GAAG,WAAW,YAAY;AACvC,WAAO,GAAG,CAAC,EAAE;AAAA,MAAK,IAAI,CAAAC,OAAKA,GAAE,KAAK,YAAY,CAAC;AAAA;AAAA,MAE/C,IAAI,gBAAc;AAChB,cAAM,cAAc,aAAa,KAAK;AACtC,cAAM,SAAS,cAAc,YAAY;AACzC,cAAM,QAAQ,SAAS,KAAK;AAC5B,eAAO,QAAQ;AAAA,MACjB,CAAC;AAAA;AAAA,MAED,IAAI,WAAS;AACX,aAAK,IAAI,SAAS,iCACb,KAAK,oBAAoB,KAAK,IADjB;AAAA,UAEhB,UAAU,qBAAqB,KAAK,IAAI,wBAAwB;AAAA,QAClE,EAAC;AAAA,MACH,CAAC;AAAA,IAAC;AAAA,EACJ;AAYF;AAVI,cAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,SAAO,KAAK,qBAAqB,eAAiB,kBAAkB,eAAe,GAAM,kBAAkB,WAAW,GAAM,kBAAkB,QAAQ,CAAC;AACzJ;AAGA,cAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,YAAY;AACd,CAAC;AAjDL,IAAM,eAAN;AAAA,CAoDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,mBAAN,MAAM,yBAAwB,aAAa;AAAA,EACzC,IAAI,eAAe;AACjB,WAAO;AAAA,EACT;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,YAAY,KAAK,cAAcD,WAAU;AACvC,UAAM,KAAK,aAAa,eAAeA,SAAQ;AAC/C,SAAK,MAAM;AACX,SAAK,WAAWA;AAAA,EAClB;AAAA,EACA,oBAAoB,OAAO;AACzB,WAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAaF;AAXI,iBAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,kBAAoB,kBAAkB,eAAe,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,QAAQ,CAAC;AAC9J;AAGA,iBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACvC,UAAU,CAAI,0BAA0B;AAC1C,CAAC;AA9BL,IAAM,kBAAN;AAAA,CAiCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,mBAAN,MAAM,yBAAwB,aAAa;AAAA,EACzC,IAAI,eAAe;AACjB,WAAO;AAAA,EACT;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,YAAY,KAAK,cAAcA,WAAU;AACvC,UAAM,KAAK,aAAa,eAAeA,SAAQ;AAC/C,SAAK,MAAM;AACX,SAAK,WAAWA;AAAA,EAClB;AAAA,EACA,oBAAoB,OAAO;AACzB,WAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAaF;AAXI,iBAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,kBAAoB,kBAAkB,eAAe,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,QAAQ,CAAC;AAC9J;AAGA,iBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACvC,UAAU,CAAI,0BAA0B;AAC1C,CAAC;AA9BL,IAAM,kBAAN;AAAA,CAiCC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAGH,IAAM,gBAAN,MAAM,cAAa;AAAA,EACjB,IAAI,WAAW;AACb,WAAO,KAAK,MAAM,OAAO,KAAK;AAAA,EAChC;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,aAAa,sBAAsB;AAAA,EACjD;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,UAAU,KAAK,cAAc,aAAa;AAAA,MAC/C,SAAS;AAAA,IACX,CAAC,EAAE,KAAK,gBAAgB,CAAC;AAAA,EAC3B;AAAA,EACA,YAAY,KAAK,OAAO,cAAcA,WAAU;AAC9C,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,eAAe;AACpB,SAAK,WAAWA;AAEhB,SAAK,YAAY,IAAI,QAAQ;AAC7B,SAAK,WAAW,KAAK,UAAU,KAAK,qBAAqB,CAAC;AAAA,EAC5D;AAAA;AAAA,EAEA,SAAS;AACP,UAAM,OAAO,mBAAmB,KAAK,MAAM,MAAM,KAAK,oBAAoB,KAAK,IAAI,YAAY;AAC/F,UAAM,WAAW,uBAAuB,KAAK,sBAAsB,KAAK,mBAAmB,KAAK,QAAQ;AACxG,4BAAwB,SAAS,MAAM,KAAK,aAAa,KAAK,gBAAgB,UAAU,KAAK,QAAQ,GAAG,IAAI,CAAC;AAAA,EAC/G;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,OAAO;AACb,QAAI;AACJ,QAAI;AACJ,UAAM,YAAY,GAAG,KAAK,EAAE,KAAK,iBAAiB,KAAK,QAAQ,GAAG,IAAI,MAAM;AAE1E,sBAAgB,KAAK;AACrB,uBAAiB,KAAK;AACtB,WAAK,YAAY,IAAI;AAAA,IACvB,CAAC,CAAC;AACF,UAAM,WAAW,UAAU,KAAK,UAAU,aAAa;AAAA,MACrD,SAAS;AAAA,MACT,SAAS;AAAA,IACX,CAAC,EAAE,KAAK,gBAAgB,CAAC;AACzB,UAAM,UAAU,UAAU,KAAK,UAAU,WAAW;AAAA,MAClD,SAAS;AAAA,IACX,CAAC,EAAE,KAAK,gBAAgB,GAAG,gBAAgB,KAAK,QAAQ,GAAG,IAAI,MAAM,KAAK,YAAY,KAAK,CAAC,CAAC;AAC7F,WAAO,UAAU,KAAK,IAAI,OAAK,EAAE,KAAK,YAAY,CAAC,GAAG,IAAI,gBAAc,aAAa,KAAK,eAAe,GAAG,SAAS,qBAAmB,SAAS;AAAA,MAAK,IAAI,OAAK,EAAE,KAAK,cAAc,CAAC;AAAA;AAAA,MAErL,IAAI,iBAAe,cAAc,KAAK,MAAM,MAAM;AAAA,MAAG,IAAI,YAAU,kBAAkB,SAAS,mBAAmB,aAAa;AAAA,MAAG,IAAI,cAAY,KAAK,WAAW,UAAU,cAAc,CAAC;AAAA,MAAG,IAAI,cAAY,KAAK,SAAS,QAAQ,CAAC;AAAA,MAAG,UAAU,OAAO;AAAA,IAAC,CAAC,CAAC;AAAA,EAC7P;AAeF;AAbI,cAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,SAAO,KAAK,qBAAqB,eAAiB,kBAAkB,eAAe,GAAM,kBAAkB,YAAY,GAAM,kBAAkB,WAAW,GAAM,kBAAkB,QAAQ,CAAC;AAC7L;AAGA,cAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,SAAS;AAAA,IACP,UAAU;AAAA,EACZ;AAAA,EACA,YAAY;AACd,CAAC;AAjEL,IAAM,eAAN;AAAA,CAoEC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,SAAS,mBAAmB,WAAW,aAAa,cAAc;AAChE,QAAM,iBAAiB,YAAY;AACnC,QAAM,YAAY,iBAAiB;AACnC,SAAO,KAAK,IAAI,CAAC,CAAC,WAAW,YAAY;AAC3C;AAIA,SAAS,uBAAuB,gBAAgB,WAAW,UAAU;AACnE,SAAO,iBAAiB,WAAW;AACrC;AACA,IAAM,mBAAN,MAAM,yBAAwB,aAAa;AAAA,EACzC,IAAI,iBAAiB;AACnB,WAAO;AAAA,EACT;AAAA,EACA,IAAI,eAAe;AACjB,WAAO;AAAA,EACT;AAAA,EACA,IAAI,qBAAqB;AACvB,WAAO,KAAK,IAAI,SAAS;AAAA,EAC3B;AAAA,EACA,IAAI,uBAAuB;AACzB,WAAO,KAAK,IAAI,SAAS;AAAA,EAC3B;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,IAAI,SAAS;AAAA,EAC3B;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK,WAAW,OAAO,KAAK,SAAS,YAAY,eAAe;AAAA,EACzE;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,YAAY,KAAK,OAAO,SAASA,WAAU,KAAK;AAC9C,UAAM,KAAK,OAAO,QAAQ,eAAeA,SAAQ;AACjD,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,WAAWA;AAChB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,aAAa,UAAU,MAAM;AAC3B,SAAK,aAAa,MAAM,QAAQ,GAAG,IAAI;AACvC,SAAK,aAAa,MAAM,YAAY,eAAe,QAAQ;AAAA,EAC7D;AAAA,EACA,WAAW,UAAU,WAAW;AAC9B,QAAI,KAAK,IAAI,UAAU,OAAO;AAC5B,UAAI,KAAK,IAAI,QAAQ,sBAAsB,GAAmC;AAC5E,eAAO,WAAW;AAAA,MACpB;AACA,UAAI,KAAK,IAAI,QAAQ,sBAAsB,GAAoC;AAC7E,eAAO,YAAY;AAAA,MACrB;AAAA,IAKF;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,UAAU,UAAU;AAClC,QAAI,KAAK,IAAI,UAAU,OAAO;AAC5B,UAAI,KAAK,IAAI,QAAQ,sBAAsB,GAAoC;AAC7E,eAAO,CAAC;AAAA,MACV;AACA,UAAI,KAAK,IAAI,QAAQ,sBAAsB,GAAkC;AAC3E,eAAO,WAAW;AAAA,MACpB;AAAA,IAKF;AACA,WAAO;AAAA,EACT;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,IAAI,YAAY;AAAA,MACnB,oBAAoB;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EACA,SAAS,UAAU;AACjB,SAAK,IAAI,SAAS,UAAU,QAAQ;AAAA,EACtC;AAaF;AAXI,iBAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,kBAAoB,kBAAkB,eAAe,GAAM,kBAAkB,eAAe,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,cAAc,CAAC;AAC9O;AAGA,iBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACvC,UAAU,CAAI,0BAA0B;AAC1C,CAAC;AAlFL,IAAM,kBAAN;AAAA,CAqFC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,mBAAN,MAAM,yBAAwB,aAAa;AAAA,EACzC,IAAI,eAAe;AACjB,WAAO;AAAA,EACT;AAAA,EACA,IAAI,qBAAqB;AACvB,WAAO,KAAK,IAAI,SAAS;AAAA,EAC3B;AAAA,EACA,IAAI,uBAAuB;AACzB,WAAO,KAAK,IAAI,SAAS;AAAA,EAC3B;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,IAAI,SAAS;AAAA,EAC3B;AAAA,EACA,IAAI,iBAAiB;AACnB,WAAO;AAAA,EACT;AAAA,EACA,IAAI,kBAAkB;AACpB,WAAO,KAAK,WAAW,MAAM,KAAK,SAAS,YAAY,eAAe;AAAA,EACxE;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,YAAY,KAAK,OAAO,SAASA,WAAU;AACzC,UAAM,KAAK,OAAO,QAAQ,eAAeA,SAAQ;AACjD,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,WAAWA;AAAA,EAClB;AAAA,EACA,aAAa,UAAU,MAAM;AAC3B,SAAK,aAAa,MAAM,SAAS,GAAG,IAAI;AACxC,SAAK,aAAa,MAAM,YAAY,oBAAoB,QAAQ;AAAA,EAClE;AAAA,EACA,WAAW,UAAU;AACnB,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,UAAU;AACxB,WAAO;AAAA,EACT;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,IAAI,YAAY;AAAA,MACnB,kBAAkB;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,SAAS,UAAU;AACjB,SAAK,IAAI,SAAS,UAAU,QAAQ;AAAA,EACtC;AAaF;AAXI,iBAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,SAAO,KAAK,qBAAqB,kBAAoB,kBAAkB,eAAe,GAAM,kBAAkB,eAAe,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,QAAQ,CAAC;AACrM;AAGA,iBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,EACvC,UAAU,CAAI,0BAA0B;AAC1C,CAAC;AAzDL,IAAM,kBAAN;AAAA,CA4DC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AAGH,IAAM,aAAN,MAAM,WAAU;AAAA,EACd,YAAY,IAAI,KAAK,UAAUA,WAAU,MAAM;AAC7C,SAAK,KAAK;AACV,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,WAAWA;AAChB,SAAK,OAAO;AAEZ,SAAK,YAAY,IAAI,QAAQ;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,wBAAwB;AAEtB,QAAI;AAEJ,QAAI;AAEJ,QAAI;AAEJ,QAAI,KAAK,IAAI,wBAAwB,YAAY;AAE/C,WAAK,uBAAuB,IAAI,QAAQ;AACxC,WAAK,uBAAuB,IAAI,QAAQ;AAExC,WAAK,IAAI,SAAS,sBAAsB,KAAK,IAAI,4BAA4B,KAAK,SAAS;AAE3F,uBAAiB,KAAK;AACtB,wBAAkB,KAAK;AACvB,0BAAoB,KAAK,IAAI,SAAS,QAAQ;AAAA;AAAA,QAE9C,IAAI,OAAK,IAAI,eAAe,GAAG,KAAK,GAAG,sBAAsB,CAAC,IAAI,KAAK;AAAA,QAAG,qBAAqB;AAAA;AAAA,QAE/F,IAAI,aAAW,KAAK,SAAS,gBAAgB,UAAU,MAAM,QAAQ,IAAI;AAAA,MAAC;AAC1E,WAAK,IAAI,SAAS,QAAQ,KAAK,IAAI,OAAK;AACtC,YAAI,GAAG;AACL,cAAI,eAAe,GAAG,KAAK,MAAM,UAAU,GAAG;AAC5C,iBAAK,qBAAqB,KAAK,CAAC;AAAA,UAClC,WAAW,eAAe,GAAG,KAAK,MAAM,UAAU,GAAG;AACnD,iBAAK,IAAI,WAAW,IAAI;AACxB,iBAAK,qBAAqB,KAAK,CAAC;AAAA,UAClC;AAAA,QACF,OAAO;AACL,eAAK,IAAI,WAAW,KAAK;AAAA,QAC3B;AAAA,MACF,CAAC,GAAG,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU;AAAA,IAC3C,OAAO;AAEL,uBAAiB,KAAK,MAAM;AAC5B,wBAAkB,KAAK,MAAM;AAC7B,0BAAoB,KAAK;AAAA,IAC3B;AACA,WAAO;AAAA;AAAA,MAEP,kBAAkB,KAAK,IAAI,OAAK,KAAK,WAAW,CAAC,CAAC,CAAC;AAAA;AAAA,MAEnD,eAAe,KAAK,UAAU,OAAK,KAAK,MAAM,QAAQ,CAAC,CAAC,CAAC;AAAA;AAAA,MAEzD,gBAAgB,KAAK,UAAU,OAAK,KAAK,MAAM,eAAe,GAAG,KAAK,MAAM,MAAM,KAAK,kBAAkB,CAAC,CAAC;AAAA,IAAC;AAAA,EAC9G;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,UAAM,aAAa,UAAU,KAAK,IAAI,cAAc;AAAA,MAClD,SAAS;AAAA,IACX,CAAC,EAAE,KAAK,gBAAgB,GAAG,IAAI,MAAM,IAAI,CAAC;AAC1C,UAAM,aAAa,UAAU,KAAK,IAAI,cAAc;AAAA,MAClD,SAAS;AAAA,IACX,CAAC,EAAE,KAAK,gBAAgB,GAAG,IAAI,MAAM,KAAK,CAAC;AAC3C,WAAO,MAAM,YAAY,UAAU;AAAA,EACrC;AAAA,EACA,WAAW;AACT,SAAK,KAAK,kBAAkB,MAAM;AAEhC,UAAI,EAAE,KAAK,SAAS,OAAO,KAAK,SAAS,YAAY,CAAC,KAAK,IAAI,uBAAuB;AACpF,aAAK,sBAAsB,EAAE,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU;AAAA,MACzE;AAEA,YAAM,KAAK,IAAI,UAAU,KAAK,IAAI,OAAO,EAAE,KAAK,IAAI,MAAM,KAAK,OAAO,OAAO,CAAC,GAAG,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU;AAAA,IACxH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,SAAS;AAExB,QAAI,KAAK,wBAAwB,KAAK,sBAAsB;AAC1D,WAAK,qBAAqB,SAAS;AACnC,WAAK,qBAAqB,SAAS;AAAA,IACrC;AAAA,EACF;AAYF;AAVI,WAAK,OAAO,SAAS,kBAAkB,mBAAmB;AACxD,SAAO,KAAK,qBAAqB,YAAc,kBAAkB,WAAW,GAAM,kBAAkB,eAAe,GAAM,kBAAqB,QAAQ,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,MAAM,CAAC;AAC1N;AAGA,WAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,YAAY;AACd,CAAC;AAnGL,IAAM,YAAN;AAAA,CAsGC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,EACR,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,cAAN,MAAM,oBAAmB,UAAU;AAAA,EACjC,IAAI,qBAAqB;AACvB,WAAO,KAAK,IAAI,SAAS;AAAA,EAC3B;AAAA,EACA,YAAY,IAAI,KAAK,UAAUA,WAAU,MAAM;AAC7C,UAAM,GAAG,eAAe,KAAK,UAAUA,WAAU,IAAI;AACrD,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,WAAWA;AAChB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,IAAI,WAAW;AAAA,MAClB,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AAgDF;AA9CI,YAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,SAAO,KAAK,qBAAqB,aAAe,kBAAqB,UAAU,GAAM,kBAAkB,eAAe,GAAM,kBAAqB,QAAQ,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,MAAM,CAAC;AAC7N;AAGA,YAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,EAC3B,WAAW,SAAS,iBAAiB,IAAI,KAAK;AAC5C,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,iBAAiB,CAAC;AACjC,MAAG,YAAY,iBAAiB,CAAC;AAAA,IACnC;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAC5D,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,UAAU;AAAA,EACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,qBAAqB,IAAI;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,UAAU,CAAI,0BAA0B;AAAA,EACxC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,mBAAmB,EAAE,CAAC;AAAA,EACzD,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,UAAU,GAAG,OAAO,CAAC;AACxB,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAc,eAAe,uBAAuB,IAAI,IAAI,UAAU,CAAC;AAC1E,MAAG,UAAU;AACb,MAAG,WAAc,eAAe,uBAAuB,IAAI,IAAI,UAAU,CAAC;AAAA,IAC5E;AAAA,EACF;AAAA,EACA,cAAc,CAAC,iBAAiB,eAAe;AAAA,EAC/C,QAAQ,CAAC,0tDAA0tD;AAAA,EACnuD,iBAAiB;AACnB,CAAC;AA7DL,IAAM,aAAN;AAAA,CAgEC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,6BAA6B;AAAA,MAC/B;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,YAAY;AAAA,MACZ,SAAS,CAAC,iBAAiB,eAAe;AAAA,MAC1C,QAAQ,CAAC,k1DAAk1D;AAAA,IAC71D,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,cAAN,MAAM,oBAAmB,UAAU;AAAA,EACjC,IAAI,qBAAqB;AACvB,WAAO,KAAK,IAAI,SAAS;AAAA,EAC3B;AAAA,EACA,YAAY,IAAI,KAAK,UAAUA,WAAU,MAAM;AAC7C,UAAM,GAAG,eAAe,KAAK,UAAUA,WAAU,IAAI;AACrD,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,WAAWA;AAChB,SAAK,OAAO;AAAA,EACd;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,IAAI,WAAW;AAAA,MAClB,mBAAmB;AAAA,IACrB,CAAC;AAAA,EACH;AAgDF;AA9CI,YAAK,OAAO,SAAS,mBAAmB,mBAAmB;AACzD,SAAO,KAAK,qBAAqB,aAAe,kBAAqB,UAAU,GAAM,kBAAkB,eAAe,GAAM,kBAAqB,QAAQ,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,MAAM,CAAC;AAC7N;AAGA,YAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,aAAa,CAAC;AAAA,EAC3B,WAAW,SAAS,iBAAiB,IAAI,KAAK;AAC5C,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,iBAAiB,CAAC;AACjC,MAAG,YAAY,iBAAiB,CAAC;AAAA,IACnC;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAC5D,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,UAAU;AAAA,EACV,cAAc,SAAS,wBAAwB,IAAI,KAAK;AACtD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,qBAAqB,IAAI;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,UAAU,CAAI,0BAA0B;AAAA,EACxC,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,mBAAmB,EAAE,GAAG,CAAC,mBAAmB,EAAE,CAAC;AAAA,EACzD,UAAU,SAAS,oBAAoB,IAAI,KAAK;AAC9C,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,MAAG,UAAU,GAAG,OAAO,CAAC;AACxB,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAc,eAAe,uBAAuB,IAAI,IAAI,UAAU,CAAC;AAC1E,MAAG,UAAU;AACb,MAAG,WAAc,eAAe,uBAAuB,IAAI,IAAI,UAAU,CAAC;AAAA,IAC5E;AAAA,EACF;AAAA,EACA,cAAc,CAAC,iBAAiB,eAAe;AAAA,EAC/C,QAAQ,CAAC,26DAA26D;AAAA,EACp7D,iBAAiB;AACnB,CAAC;AA7DL,IAAM,aAAN;AAAA,CAgEC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,6BAA6B;AAAA,MAC/B;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,YAAY;AAAA,MACZ,SAAS,CAAC,iBAAiB,eAAe;AAAA,MAC1C,QAAQ,CAAC,0iEAA0iE;AAAA,IACrjE,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uBAAuB,IAAI,eAAe,sBAAsB;AACtE,IAAM,iBAAiB;AAAA,EACrB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,4BAA4B;AAAA,EAC5B,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,uBAAuB;AACzB;AACA,IAAM,oBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,SAAS;AACnB,SAAK,gBAAgB,UAAU,kCAC1B,iBACA,WACD;AACJ,SAAK,oBAAoB,qBAAqB;AAAA,EAChD;AAaF;AAXI,kBAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,SAAO,KAAK,qBAAqB,mBAAqB,SAAS,sBAAsB,CAAC,CAAC;AACzF;AAGA,kBAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,kBAAiB;AAAA,EAC1B,YAAY;AACd,CAAC;AAlBL,IAAM,mBAAN;AAAA,CAqBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,MACR,GAAG;AAAA,QACD,MAAM;AAAA,QACN,MAAM,CAAC,oBAAoB;AAAA,MAC7B,CAAC;AAAA,IACH,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,8BAAN,MAAM,4BAA2B;AAAA,EAC/B,YAAYA,WAAU,SAAS,UAAU;AACvC,SAAK,WAAWA;AAChB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,iBAAiB,IAAI,gBAAgB,KAAK,uBAAuB,CAAC;AACvE,SAAK,gBAAgB,KAAK,eAAe,aAAa;AAEtD,QAAI,SAAS,WAAW;AACtB,gBAAU,KAAK,SAAS,aAAa,UAAU;AAAA,QAC7C,SAAS;AAAA,MACX,CAAC,EAAE,KAAK,aAAa,KAAK,QAAQ,cAAc,oBAAoB,GAAG,IAAI,MAAM,KAAK,uBAAuB,CAAC,GAAG,qBAAqB,GAAG,IAAI,UAAQ,KAAK,eAAe,KAAK,IAAI,CAAC,CAAC,EAAE,UAAU;AAAA,IAClM;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,yBAAyB;AAEvB,QAAI,CAAC,KAAK,SAAS,WAAW;AAC5B,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,SAAS,KAAK;AACrB,aAAO;AAAA,IACT;AACA,UAAM,MAAM,KAAK,SAAS,cAAc,KAAK;AAC7C,QAAI,YAAY;AAChB,QAAI,MAAM,OAAO;AACjB,QAAI,MAAM,WAAW;AACrB,QAAI,MAAM,WAAW;AACrB,QAAI,MAAM,MAAM;AAChB,SAAK,SAAS,KAAK,YAAY,GAAG;AAClC,UAAM,OAAO,IAAI,sBAAsB,EAAE;AACzC,SAAK,SAAS,KAAK,YAAY,GAAG;AAClC,WAAO;AAAA,EACT;AAaF;AAXI,4BAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,SAAO,KAAK,qBAAqB,6BAA+B,SAAS,QAAQ,GAAM,SAAS,gBAAgB,GAAM,SAAY,QAAQ,CAAC;AAC7I;AAGA,4BAAK,QAA0B,mBAAmB;AAAA,EAChD,OAAO;AAAA,EACP,SAAS,4BAA2B;AAAA,EACpC,YAAY;AACd,CAAC;AA/CL,IAAM,6BAAN;AAAA,CAkDC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC;AAAA,QACX,MAAM;AAAA,QACN,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAS;AAAA,IACX,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,uBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,IAAI,UAAU,qBAAqB;AAC7C,SAAK,WAAW;AAChB,SAAK,sBAAsB;AAC3B,SAAK,cAAc,aAAa;AAChC,SAAK,cAAc,oBAAoB,cAAc,UAAU,UAAQ;AACrE,WAAK,SAAS,SAAS,GAAG,eAAe,2BAA2B,IAAI,IAAI,MAAM,oBAAoB,QAAQ;AAAA,IAChH,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,YAAY,YAAY;AAAA,EAC/B;AAYF;AAVI,qBAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,SAAO,KAAK,qBAAqB,sBAAwB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,0BAA0B,CAAC;AACjL;AAGA,qBAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,uBAAuB,EAAE,CAAC;AAC7C,CAAC;AArBL,IAAM,sBAAN;AAAA,CAwBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG,IAAI;AACT,GAAG;AACH,IAAM,gBAAN,MAAM,cAAa;AAAA;AAAA,EAEjB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,qBAAqB,KAAK;AAC3C,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAC5C,SAAK,YAAY,KAAK,aAAa,IAAI,KAAK,WAAW;AAAA,EACzD;AAAA,EACA,YAAY,MAAM,UAAU,WAAW;AACrC,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,uBAAuB;AAC5B,SAAK,QAAQ,IAAI,aAAa;AAC9B,QAAI,CAAC,WAAW;AACd,YAAM,IAAI,MAAM,uFAAuF;AAAA,IACzG;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,wBAAwB,CAAC,KAAK,WAAW;AACjD,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,aAAa;AACX,SAAK,aAAa;AAClB,QAAI,KAAK,SAAS,WAAW;AAC3B,YAAM,SAAS,IAAI,WAAW,cAAY;AACxC,aAAK,kBAAkB,IAAI,eAAe,OAAK,SAAS,KAAK,CAAC,CAAC;AAC/D,aAAK,gBAAgB,QAAQ,KAAK,UAAU,SAAS,aAAa;AAClE,YAAI,KAAK,UAAU,SAAS,uBAAuB;AACjD,eAAK,gBAAgB,QAAQ,KAAK,UAAU,SAAS,qBAAqB;AAAA,QAC5E;AAAA,MACF,CAAC;AACD,WAAK,KAAK,kBAAkB,MAAM;AAChC,aAAK,wBAAwB,KAAK,YAAY,OAAO,KAAK,aAAa,KAAK,SAAS,CAAC,IAAI,QAAQ,UAAU,KAAK,KAAK;AAAA,MACxH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK,iBAAiB,WAAW;AACjC,SAAK,sBAAsB,YAAY;AAAA,EACzC;AAmBF;AAjBI,cAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,SAAO,KAAK,qBAAqB,eAAiB,kBAAqB,MAAM,GAAM,kBAAqB,QAAQ,GAAM,kBAAkB,eAAe,CAAC;AAC1J;AAGA,cAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,EACpC,QAAQ;AAAA,IACN,UAAU,CAAC,GAAG,kBAAkB,UAAU;AAAA,IAC1C,UAAU,CAAC,GAAG,kBAAkB,UAAU;AAAA,EAC5C;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AACF,CAAC;AAvEL,IAAM,eAAN;AAAA,CA0EC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAN,MAAM,aAAY;AAAA;AAAA,EAEhB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,UAAU;AACrB,SAAK,YAAY,sBAAsB,QAAQ;AAAA,EACjD;AAAA;AAAA,EAEA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,eAAe,UAAU;AAC3B,SAAK,kBAAkB,sBAAsB,QAAQ;AAAA,EACvD;AAAA;AAAA,EAEA,IAAI,wBAAwB;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,sBAAsB,UAAU;AAClC,SAAK,yBAAyB,sBAAsB,QAAQ;AAAA,EAC9D;AAAA;AAAA,EAEA,IAAI,6BAA6B;AAC/B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,2BAA2B,UAAU;AACvC,SAAK,8BAA8B,sBAAsB,QAAQ;AAAA,EACnE;AAAA;AAAA,EAEA,IAAI,qBAAqB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,mBAAmB,UAAU;AAC/B,SAAK,sBAAsB,sBAAsB,QAAQ;AAAA,EAC3D;AAAA;AAAA,EAEA,IAAI,oBAAoB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,kBAAkB,UAAU;AAC9B,SAAK,qBAAqB,sBAAsB,QAAQ;AAAA,EAC1D;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,GAAG;AAAA,EACjB;AAAA,EACA,YAAY,IAAI,MAAM,mBAAmB,KAAK,cAAc,SAAS;AACnE,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,oBAAoB;AACzB,SAAK,MAAM;AACX,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,kBAAkB,KAAK,QAAQ,cAAc;AAClD,SAAK,yBAAyB,KAAK,QAAQ,cAAc;AACzD,SAAK,sBAAsB,KAAK,QAAQ,cAAc;AACtD,SAAK,qBAAqB,KAAK,QAAQ,cAAc;AACrD,SAAK,8BAA8B,KAAK,QAAQ,cAAc;AAE9D,SAAK,YAAY,KAAK,QAAQ,cAAc;AAE5C,SAAK,aAAa,KAAK,QAAQ,cAAc;AAE7C,SAAK,aAAa,KAAK,QAAQ,cAAc;AAE7C,SAAK,eAAe,KAAK,QAAQ,cAAc;AAE/C,SAAK,2BAA2B,KAAK,QAAQ,cAAc;AAM3D,SAAK,sBAAsB,KAAK,QAAQ,cAAc;AAQtD,SAAK,QAAQ,KAAK,QAAQ,cAAc;AAQxC,SAAK,aAAa,KAAK,QAAQ,cAAc;AAO7C,SAAK,aAAa,KAAK,QAAQ,cAAc;AAS7C,SAAK,WAAW,KAAK,QAAQ,cAAc;AAE3C,SAAK,iBAAiB,KAAK,QAAQ,cAAc;AAEjD,SAAK,kBAAkB,KAAK,QAAQ,cAAc;AAElD,SAAK,UAAU,IAAI,aAAa;AAEhC,SAAK,QAAQ,CAAC;AAEd,SAAK,YAAY,IAAI,QAAQ;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,QAAI,eAAe;AACnB,QAAI,iBAAiB;AACrB,QAAI,yBAAyB;AAC7B,QAAI,2BAA2B;AAE/B,QAAI,KAAK,UAAU,SAAS,KAAK,UAAU,YAAY;AACrD,+BAAyB,KAAK,SAAS,eAAe,KAAK,SAAS;AACpE,qBAAe,KAAK,eAAe,YAAY;AAAA,IACjD;AAEA,QAAI,KAAK,UAAU,SAAS,KAAK,UAAU,cAAc;AACvD,iCAA2B,KAAK,SAAS,cAAc,KAAK,SAAS;AACrE,uBAAiB,KAAK,eAAe,YAAY;AAAA,IACnD;AAEA,SAAK,SAAS;AAAA,MACZ,UAAU,KAAK;AAAA,MACf,OAAO,KAAK;AAAA,MACZ,YAAY,KAAK;AAAA,MACjB,YAAY,KAAK;AAAA,MACjB,aAAa,KAAK;AAAA,MAClB,KAAK,KAAK,IAAI;AAAA,MACd,qBAAqB,KAAK;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,SAAS,OAAO;AACd,SAAK,QAAQ,kCACR,KAAK,QACL;AAEL,SAAK,kBAAkB,cAAc;AAAA,EACvC;AAAA,EACA,uBAAuB,UAAU;AAC/B,QAAI;AACJ,WAAO,KAAK,SAAS,KAAK,IAAI,OAAK,QAAQ,CAAC,GAAG,IAAI,OAAK,EAAE,OAAO,QAAQ,CAAC,GAAG,SAAS,GAAG,OAAO,CAAC,CAAC,MAAM,IAAI,MAAM,SAAS,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC;AAAA,EACpJ;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,SAAS;AAClB,SAAK,KAAK,IAAI,MAAM,KAAK,SAAS,mBAC7B,QACJ,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,UAAU;AACpB,SAAK,KAAK,IAAI,MAAM,KAAK,SAAS,mBAC7B,SACJ,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,kBAAkB;AAC3B,SAAK,KAAK,IAAI,MAAM,KAAK,SAAS;AAAA,MAChC;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,WAAW;AAET,SAAK,KAAK,kBAAkB,MAAM;AAChC,UAAI,KAAK,gBAAgB;AACvB,aAAK,WAAW,KAAK;AACrB,aAAK,gBAAgB,aAAa;AAAA,MACpC,OAAO;AACL,aAAK,WAAW,KAAK;AAAA,MACvB;AAEA,WAAK,SAAS,cAAc,KAAK,SAAS;AAC1C,UAAI,eAAe,UAAU,KAAK,SAAS,eAAe,UAAU;AAAA,QAClE,SAAS;AAAA,MACX,CAAC;AAED,qBAAe,KAAK,kBAAkB,aAAa,KAAK,UAAU,KAAK,eAAe,CAAC,IAAI;AAE3F,WAAK,WAAW,aAAa,KAAK,UAAU,KAAK,SAAS,CAAC;AAC3D,WAAK,mBAAmB,KAAK,uBAAuB,WAAW;AAC/D,WAAK,qBAAqB,KAAK,uBAAuB,YAAY;AAAA,IACpE,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AAEnB,QAAI,KAAK,UAAU;AACjB,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EACA,kBAAkB;AAEhB,SAAK,OAAO;AAEZ,SAAK,IAAI,OAAO,KAAK,IAAI,MAAM,KAAK,OAAO,CAAC,GAAG,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU;AAAA,EACtF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,SAAS;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACP,QAAI,CAAC,KAAK,oBAAoB;AAC5B,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,YAAY;AAAA,IACnB;AAEA,SAAK,YAAY;AACjB,SAAK,QAAQ,KAAK;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,SAAS;AAChB,WAAO,KAAK,aAAa,SAAS,KAAK,SAAS,eAAe,OAAO;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,QAAQ,SAAS;AAC/B,WAAO,KAAK,aAAa,gBAAgB,KAAK,SAAS,eAAe,QAAQ,OAAO;AAAA,EACvF;AAAA,EACA,eAAe;AAEb,QAAI,KAAK,eAAe,cAAc,KAAK,YAAY;AAErD,WAAK,cAAc,MAAM,SAAS,GAAG,KAAK,SAAS,gBAAgB,KAAK,WAAW,cAAc,YAAY;AAAA,IAC/G,OAAO;AACL,WAAK,cAAc,MAAM,SAAS,GAAG,KAAK,SAAS,aAAa;AAAA,IAClE;AAAA,EACF;AAAA,EACA,cAAc;AAEZ,QAAI,KAAK,eAAe,cAAc,KAAK,YAAY;AAErD,WAAK,cAAc,MAAM,QAAQ,GAAG,KAAK,SAAS,eAAe,KAAK,WAAW,cAAc,WAAW;AAAA,IAC5G,OAAO;AACL,WAAK,cAAc,MAAM,QAAQ,GAAG,KAAK,SAAS,YAAY;AAAA,IAChE;AAAA,EACF;AAgGF;AA9FI,aAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,SAAO,KAAK,qBAAqB,cAAgB,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,cAAc,GAAM,kBAAuB,mBAAmB,GAAM,kBAAkB,gBAAgB,CAAC;AACjS;AAGA,aAAK,OAAyB,kBAAkB;AAAA,EAC9C,MAAM;AAAA,EACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,EAC5B,gBAAgB,SAAS,2BAA2B,IAAI,KAAK,UAAU;AACrE,QAAI,KAAK,GAAG;AACV,MAAG,eAAe,UAAU,gBAAgB,CAAC;AAAA,IAC/C;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AAAA,IACvE;AAAA,EACF;AAAA,EACA,WAAW,SAAS,kBAAkB,IAAI,KAAK;AAC7C,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,KAAK,GAAG,UAAU;AACjC,MAAG,YAAY,KAAK,GAAG,UAAU;AACjC,MAAG,YAAY,gBAAgB,CAAC;AAAA,IAClC;AACA,QAAI,KAAK,GAAG;AACV,UAAI;AACJ,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,aAAa,GAAG;AACjE,MAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,kBAAkB,GAAG;AAAA,IACxE;AAAA,EACF;AAAA,EACA,UAAU;AAAA,EACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,QAAI,KAAK,GAAG;AACV,MAAG,YAAY,gBAAgB,IAAI;AAAA,IACrC;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,uBAAuB;AAAA,IACvB,4BAA4B;AAAA,IAC5B,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,0BAA0B;AAAA,IAC1B,qBAAqB;AAAA,IACrB,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,EACnB;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AAAA,EACA,UAAU,CAAC,aAAa;AAAA,EACxB,UAAU,CAAI,mBAAmB,CAAC;AAAA,IAChC,SAAS;AAAA,IACT,aAAa;AAAA,EACf,CAAC,CAAC,GAAM,oBAAoB;AAAA,EAC5B,oBAAoB;AAAA,EACpB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ,CAAC,CAAC,cAAc,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,GAAG,wBAAwB,GAAG,QAAQ,GAAG,CAAC,GAAG,8BAA8B,GAAG,gBAAgB,kBAAkB,gBAAgB,GAAG,CAAC,kBAAkB,IAAI,uBAAuB,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC;AAAA,EACnP,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,QAAI,KAAK,GAAG;AACV,MAAG,gBAAgB;AACnB,MAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,MAAG,WAAW,gBAAgB,SAAS,mDAAmD;AACxF,eAAO,IAAI,OAAO;AAAA,MACpB,CAAC;AACD,MAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,KAAK;AACvC,MAAG,aAAa,CAAC;AACjB,MAAG,aAAa,EAAE,EAAE;AACpB,MAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,gBAAgB,CAAC;AAC7E,MAAG,aAAa;AAAA,IAClB;AACA,QAAI,KAAK,GAAG;AACV,MAAG,WAAW,UAAU,IAAI,KAAK;AACjC,MAAG,UAAU;AACb,MAAG,WAAW,kBAAkB,IAAI,cAAc,EAAE,kBAAkB,IAAI,cAAc;AACxF,MAAG,UAAU,CAAC;AACd,MAAG,WAAW,QAAQ,CAAC,IAAI,QAAQ;AAAA,IACrC;AAAA,EACF;AAAA,EACA,cAAc,CAAC,MAAM,QAAQ,cAAc,gBAAgB,qBAAqB,YAAY,UAAU;AAAA,EACtG,QAAQ,CAAC,kxmBAAkxmB,s5CAAs5C;AAAA,EACjrpB,iBAAiB;AACnB,CAAC;AAzWL,IAAM,cAAN;AAAA,CA4WC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,MAAM;AAAA,QACJ,wBAAwB;AAAA,MAC1B;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,MACZ,SAAS,CAAC,MAAM,QAAQ,cAAc,gBAAgB,qBAAqB,YAAY,UAAU;AAAA,MACjG,UAAU;AAAA,MACV,QAAQ,CAAC,sgcAAsgc,qgDAAqgD;AAAA,IACthf,CAAC;AAAA,EACH,CAAC,GAAG,WAAY;AACd,WAAO,CAAC;AAAA,MACN,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAS;AAAA,IACX,GAAG;AAAA,MACD,MAAW;AAAA,IACb,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,uBAAuB,CAAC;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,4BAA4B,CAAC;AAAA,MAC3B,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,QACnB,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,mBAAkB;AAgBxB;AAdI,mBAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,SAAO,KAAK,qBAAqB,oBAAmB;AACtD;AAGA,mBAAK,OAAyB,iBAAiB;AAAA,EAC7C,MAAM;AAAA,EACN,SAAS,CAAC,aAAa,cAAc;AAAA,EACrC,SAAS,CAAC,aAAa,cAAc;AACvC,CAAC;AAGD,mBAAK,OAAyB,iBAAiB,CAAC,CAAC;AAdrD,IAAM,oBAAN;AAAA,CAiBC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa,cAAc;AAAA,MACrC,SAAS,CAAC,aAAa,cAAc;AAAA,IACvC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["RtlScrollAxisType", "rtlScrollAxisType", "document", "e"]}