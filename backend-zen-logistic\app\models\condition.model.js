const sql = require("./db.js");

// Constructor
const ConditionModel = function(condition) {
    //console.log("rrrrrrrrr",condition)
  this.nom_condition = condition.nom_condition;
};

ConditionModel.create = (newCondition, result) => {
  sql.query("INSERT INTO `condition` SET ?", newCondition, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

   // //console.log("created condition: ", { id: res.insertId, ...newCondition });
    result(null, { id: res.insertId, ...newCondition });
  });
};

ConditionModel.findAll = (result) => {
    sql.query("SELECT * FROM `condition`", (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(err, null);
            return;
        }

      //  //console.log("conditions: ", res);
        result(null, res);
    });
};


ConditionModel.delete = (conditionId, result) => {
  sql.query("DELETE FROM `condition` WHERE id = ?", conditionId, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      // No condition found with the given id
      result({ kind: "not_found" }, null);
      return;
    }

  //  //console.log("Deleted condition with id: ", conditionId);
    result(null, res);
  });
};

module.exports = ConditionModel;
;
