const sql = require("./db.js");
const Entreposage = function(entreposage) {
    this.id= entreposage.id ;
    this.id_ste = entreposage.id_ste ;
    this.nom_societe = entreposage.nom_societe ;
    this.nom_depot = entreposage.nom_depot ;
    this.date = entreposage.date ;
    this.qte = entreposage.qte ;
    this.id_facture = entreposage.id_facture ;

    
  } ;



  Entreposage.getAll = result => {
    sql.query("SELECT * FROM entreposage", (err, res) => {
      if (err) {
        //console.log("SQL error: ", err);
        result(err, null); // Remplacer null par err en cas d'erreur
        return;
      }
  
      //console.log("entreposage: ", res);
      result(null, res);
    });
  };

  Entreposage.getByClientAndDate = (id_ste, datedebut, datefin, result) => {
    const query = `
      SELECT * FROM entreposage 
      WHERE id_ste = ? 
      AND id_facture IS NULL
      AND date BETWEEN ? AND ? AND prix_tot>0;
    `;
  
    sql.query(query, [id_ste, datedebut, datefin], (err, res) => {
      if (err) {
        //console.log("SQL error: ", err);
        result(err, null);
        return;
      }
  
      //console.log("flux: ", res);
      result(null, res);
    });
  };

  Entreposage.getEntreposageByIdfacture = (id_facture, result) => {
    const query = `
      SELECT * FROM entreposage 
      WHERE id_facture = ? 
    `;
  
    sql.query(query, [id_facture], (err, res) => {
      if (err) {
        //console.log("SQL error: ", err);
        result(err, null);
        return;
      }
  
      //console.log("flux: ", res);
      result(null, res);
    });
  };


  module.exports = Entreposage ; 
