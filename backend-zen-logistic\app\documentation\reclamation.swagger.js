/**
 * @swagger
 * tags:
 *   name: Reclamations
 *   description: Opérations liées à la gestion des réclamations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Reclamation:
 *       type: object
 *       required:
 *         - id_commande
 *         - id_conducteur
 *         - id_transporteur
 *         - id_expediteur
 *         - type
 *         - image
 *       properties:
 *         id_reclamation:
 *           type: integer
 *           description: Identifiant unique de la réclamation
 *         id_commande:
 *           type: integer
 *           description: Identifiant de la commande associée
 *         id_conducteur:
 *           type: integer
 *           description: Identifiant du conducteur lié à la réclamation
 *         id_transporteur:
 *           type: integer
 *           description: Identifiant du transporteur lié à la réclamation
 *         id_expediteur:
 *           type: integer
 *           description: Identifiant de l'expéditeur lié à la réclamation
 *         type:
 *           type: string
 *           description: Type de la réclamation
 *         image:
 *           type: string
 *           description: URL de l'image associée à la réclamation
 */

/**
 * @swagger
 * /api/reclamations:
 *   post:
 *     tags: [Reclamations]
 *     summary: Créer une nouvelle réclamation
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Reclamation'
 *     responses:
 *       201:
 *         description: Réclamation créée avec succès
 *       400:
 *         description: Contenu de la requête invalide
 *       500:
 *         description: Erreur interne du serveur
 */

/**
 * @swagger
 * /api/reclamation/transporteur/{transporteur}:
 *   get:
 *     tags: [Reclamations]
 *     summary: Récupérer les réclamations par transporteur
 *     parameters:
 *       - in: path
 *         name: transporteur
 *         required: true
 *         description: ID du transporteur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des réclamations récupérée avec succès
 *       404:
 *         description: Aucune réclamation trouvée pour ce transporteur
 *       500:
 *         description: Erreur interne du serveur
 */

/**
 * @swagger
 * /api/reclamation/expediteur/{expediteur}:
 *   get:
 *     tags: [Reclamations]
 *     summary: Récupérer les réclamations par expéditeur
 *     parameters:
 *       - in: path
 *         name: expediteur
 *         required: true
 *         description: ID de l'expéditeur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des réclamations récupérée avec succès
 *       404:
 *         description: Aucune réclamation trouvée pour cet expéditeur
 *       500:
 *         description: Erreur interne du serveur
 */

/**
 * @swagger
 * /api/reclamations:
 *   get:
 *     tags: [Reclamations]
 *     summary: Récupérer toutes les réclamations
 *     responses:
 *       200:
 *         description: Liste de toutes les réclamations récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur
 */
