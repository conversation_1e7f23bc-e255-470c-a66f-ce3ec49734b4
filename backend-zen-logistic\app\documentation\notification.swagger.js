/**
 * @swagger
 * tags:
 *   name: Notification
 *   description: Opérations liées à la gestion des notifications
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Notification:
 *       type: object
 *       required:
 *         - id_user
 *         - message
 *       properties:
 *         id:
 *           type: integer
 *           description: Identifiant unique de la notification
 *         id_user:
 *           type: integer
 *           description: Identifiant de l'utilisateur
 *         message:
 *           type: string
 *           description: Contenu du message de notification
 *         url:
 *           type: string
 *           description: URL associée à la notification
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Date et heure de création de la notification
 */

/**
 * @swagger
 * /api/notification/{userId}:
 *   get:
 *     tags: [Notification]
 *     summary: Récupérer toutes les notifications d'un utilisateur
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         description: ID de l'utilisateur pour lequel récupérer les notifications
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des notifications récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Notification'
 *       500:
 *         description: Erreur interne du serveur

 * /api/notification/read:
 *   put:
 *     tags: [Notification]
 *     summary: Mettre à jour le statut des notifications comme lues
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *     responses:
 *       200:
 *         description: Notifications mises à jour avec succès
 *       400:
 *         description: Requête invalide
 *       500:
 *         description: Erreur interne du serveur

 * /api/notifications/events:
 *   get:
 *     tags: [Notification]
 *     summary: Écouter les notifications via SSE
 *     responses:
 *       200:
 *         description: Connexion établie pour écouter les notifications
 *       500:
 *         description: Erreur interne du serveur

 * /api/countMessageNotReaded/{userId}:
 *   get:
 *     tags: [Notification]
 *     summary: Compter les messages non lus pour un utilisateur
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         description: ID de l'utilisateur pour lequel compter les messages non lus
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Nombre de messages non lus
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 unreadCount:
 *                   type: integer
 *       500:
 *         description: Erreur interne du serveur

 * /api/updateMessagesAsRead/{userId}:
 *   get:
 *     tags: [Notification]
 *     summary: Mettre à jour les messages comme lus pour un utilisateur
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         description: ID de l'utilisateur pour lequel mettre à jour les messages
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Messages mis à jour avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/sendNotificationToUserFireBase:
 *   post:
 *     tags: [Notification]
 *     summary: Envoyer une notification à un utilisateur via Firebase
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - driverId
 *               - title
 *               - message
 *             properties:
 *               driverId:
 *                 type: integer
 *               title:
 *                 type: string
 *               message:
 *                 type: string
 *     responses:
 *       200:
 *         description: Notification envoyée avec succès
 *       400:
 *         description: Requête invalide
 *       500:
 *         description: Erreur interne du serveur
 */
