const sql = require("./db.js");
const notification = require("./notification.model.js");

// Constructor
const CommentaireModel = function(commentaire) {
    this.id_ligne = commentaire.id_ligne;
    this.value = commentaire.value;
    this.nom_author = commentaire.nom_author;
    this.id_author = commentaire.id_author;
};

CommentaireModel.create = (newCommentaire, result) => {
  // Récupérer le nom d'utilisateur correspondant à l'ID de l'auteur
  sql.query("SELECT nom_utilisateur FROM utilisateurs WHERE id = ?", newCommentaire.id_author, (err, res) => {
      if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
      }
const user =res[0].nom_utilisateur
      if (res.length) {
          newCommentaire.nom_author = res[0].nom_utilisateur; // Mettre à jour le nom de l'auteur dans le commentaire avec le nom récupéré
      }

      // Insérer le commentaire dans la table 'commentaire' avec le nom de l'auteur mis à jour
      sql.query("INSERT INTO commentaire SET ?", newCommentaire, (err, res) => {
          if (err) {
              console.log("error: ", err);
              result(err, null);
              return;
          }


          result(null, { id: res.insertId, ...newCommentaire });

          // Mettre à jour le point de chargement avec le commentaire
          sql.query("UPDATE point_chargement SET commentaire = ? WHERE id = ?", [newCommentaire.value, newCommentaire.id_ligne], (err, res) => {
              if (err) {
                  console.log("error: ", err);
                  return;
              }

              notification.createNotificationForAdmins(`${user} a fait une reclamation`,"pages/reclamation", (err, notifications) => {
                if (err) {
                    console.error("Error creating notifications:", err);
                } else {
                    console.log("Notifications created for admin users:", notifications);
                }
            });
            

              //console.log("updated point_chargement with commentaire:", { id_ligne: newCommentaire.id_ligne, commentaire: newCommentaire.value });
          });
      });
  });
};

CommentaireModel.findCommentsByLigne = (id, result) => {
  sql.query(
    'SELECT * FROM commentaire WHERE id_ligne = ? ORDER BY date_creation ASC', // Remplacez 'date_creation' par le nom de la colonne appropriée
    [id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

     // //console.log("destination: ", res);
      result(null, res);
    }
  );
};


CommentaireModel.findAllComments = (page, limit, result) => {
  const offset = (page - 1) * limit;
  // Perform two queries, one for paginated comments and another for total count
  const query = `
    SELECT id, ajoutee_par, commentaire, nom_arrivee, nom_depart, type_ligne, volume, estimation 
    FROM point_chargement 
    WHERE commentaire IS NOT NULL 
    AND status NOT IN ('Facturé', 'facturé') 
    ORDER BY id DESC 
    LIMIT ? OFFSET ?;
  `;
  sql.query(query, [limit, offset], (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    // Extract comments from the result
    const comments = res;

    // Now, let's execute a separate query to get the total count
    sql.query('SELECT COUNT(*) AS totalItems FROM point_chargement WHERE commentaire IS NOT NULL AND status NOT IN ("Facturé", "facturé")', (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      // Extract total count from the result
      const totalItems = res[0].totalItems;

      result(null, { commentaire: comments, totalItems: totalItems });
    });
  });
};








module.exports = CommentaireModel;
