/**
 * @swagger
 * components:
 *   schemas:
 *     Flux:
 *       type: object
 *       required:
 *         - id
 *         - id_ste
 *         - nom_societe
 *         - nom_depot
 *         - date
 *         - qte
 *         - id_facture
 *       properties:
 *         id:
 *           type: integer
 *           description: ID unique du flux
 *         id_ste:
 *           type: integer
 *           description: ID de la société
 *         nom_societe:
 *           type: string
 *           description: Nom de la société
 *         nom_depot:
 *           type: string
 *           description: Nom du dépôt
 *         date:
 *           type: string
 *           format: date
 *           description: Date associée au flux
 *         qte:
 *           type: number
 *           description: Quantité associée au flux
 *         id_facture:
 *           type: integer
 *           description: ID de la facture associée
 *
 *     Formule:
 *       type: object
 *       properties:
 *         invoiced_for:
 *           type: string
 *         invoiced_name:
 *           type: string
 *         nature:
 *           type: string
 *         id_em_brand:
 *           type: integer
 *         name_em_brand:
 *           type: string
 *         type_em:
 *           type: string
 *         id_em_wharehouse:
 *           type: integer
 *         name_em_wharehouse:
 *           type: string
 *         id_rec_brand:
 *           type: integer
 *         name_rec_brand:
 *           type: string
 *         type_rec:
 *           type: string
 *         id_rec_wharehouse:
 *           type: integer
 *         name_rec_wharehouse:
 *           type: string
 *
 * /api/flux:
 *   get:
 *     summary: Obtenir tous les flux
 *     responses:
 *       200:
 *         description: Liste des flux
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Flux'
 *       500:
 *         description: Erreur lors de la récupération des flux
 *
 * /api/getByClientAndDate:
 *   post:
 *     summary: Obtenir des flux par client et date
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id_ste
 *               - datedebut
 *               - datefin
 *             properties:
 *               id_ste:
 *                 type: integer
 *               datedebut:
 *                 type: string
 *                 format: date
 *               datefin:
 *                 type: string
 *                 format: date
 *     responses:
 *       200:
 *         description: Liste des flux correspondant aux critères
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Flux'
 *       400:
 *         description: Paramètres manquants ou invalides
 *       500:
 *         description: Erreur lors de la récupération des flux
 *
 * /api/getFluxByidFacture/{id_facture}:
 *   get:
 *     summary: Obtenir des flux par ID de facture
 *     parameters:
 *       - in: path
 *         name: id_facture
 *         required: true
 *         description: ID de la facture
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Flux correspondant à l'ID de facture
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Flux'
 *       500:
 *         description: Erreur lors de la récupération des flux
 *
 * /api/brandFlux:
 *   get:
 *     summary: Obtenir toutes les marques
 *     responses:
 *       200:
 *         description: Liste des marques
 *       500:
 *         description: Erreur lors de la récupération des marques
 *
 * /api/addFormula:
 *   post:
 *     summary: Ajouter une formule à un flux
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               invoiced_for:
 *                 type: string
 *               invoiced_name:
 *                 type: string
 *               nature:
 *                 type: string
 *               id_em_brand:
 *                 type: integer
 *               name_em_brand:
 *                 type: string
 *               type_em:
 *                 type: string
 *               id_em_wharehouse:
 *                 type: integer
 *               name_em_wharehouse:
 *                 type: string
 *               id_rec_brand:
 *                 type: integer
 *               name_rec_brand:
 *                 type: string
 *               type_rec:
 *                 type: string
 *               id_rec_wharehouse:
 *                 type: integer
 *               name_rec_wharehouse:
 *                 type: string
 *     responses:
 *       200:
 *         description: Formule ajoutée avec succès
 *       500:
 *         description: Erreur lors de l'ajout de la formule
 *
 * /api/getAllFormula:
 *   get:
 *     summary: Obtenir toutes les formules
 *     responses:
 *       200:
 *         description: Liste des formules
 *       500:
 *         description: Erreur lors de la récupération des formules
 *
 * /api/disableFormula/{id}:
 *   put:
 *     summary: Désactiver une formule par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID de la formule à désactiver
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Formule désactivée avec succès
 *       404:
 *         description: Formule non trouvée
 *       500:
 *         description: Erreur lors de la désactivation de la formule
 */
