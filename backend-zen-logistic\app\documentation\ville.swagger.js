/**
 * @swagger
 * tags:
 *   name: Villes
 *   description: Opérations liées à la gestion des villes
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Ville:
 *       type: object
 *       required:
 *         - nom_ville
 *       properties:
 *         id:
 *           type: integer
 *           description: Identifiant unique de la ville
 *         nom_ville:
 *           type: string
 *           description: Nom de la ville
 */

/**
 * @swagger
 * /api/ville:
 *   post:
 *     tags: [Ville<PERSON>]
 *     summary: Créer une nouvelle ville
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Ville'
 *     responses:
 *       201:
 *         description: Ville créée avec succès
 *       400:
 *         description: Le contenu ne peut pas être vide
 *       500:
 *         description: Erreur interne du serveur
 */

/**
 * @swagger
 * /api/ville:
 *   get:
 *     tags: [Ville<PERSON>]
 *     summary: Récupérer toutes les villes
 *     responses:
 *       200:
 *         description: Liste de toutes les villes récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur
 */
