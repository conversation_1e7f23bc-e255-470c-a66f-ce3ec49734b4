/**
 * @swagger
 * tags:
 *   name: Types de Commandes
 *   description: Opérations liées à la gestion des types de commandes
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     TypeCommande:
 *       type: object
 *       required:
 *         - nom_type
 *       properties:
 *         id:
 *           type: integer
 *           description: Identifiant unique du type de commande
 *         nom_type:
 *           type: string
 *           description: Nom du type de commande
 */

/**
 * @swagger
 * /api/typecmd:
 *   post:
 *     tags: [Types de Commandes]
 *     summary: Créer un nouveau type de commande
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TypeCommande'
 *     responses:
 *       201:
 *         description: Type de commande créé avec succès
 *       400:
 *         description: Le contenu ne peut pas être vide
 *       500:
 *         description: Erreur interne du serveur
 */

/**
 * @swagger
 * /api/typecmd:
 *   get:
 *     tags: [Types de Commandes]
 *     summary: Récupérer tous les types de commandes
 *     responses:
 *       200:
 *         description: Liste de tous les types de commandes récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur
 */

/**
 * @swagger
 * /api/typecmd/{id}:
 *   delete:
 *     tags: [Types de Commandes]
 *     summary: Supprimer un type de commande par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du type de commande à supprimer
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Type de commande supprimé avec succès
 *       404:
 *         description: Type de commande non trouvé pour cet identifiant
 *       500:
 *         description: Erreur interne du serveur
 */
