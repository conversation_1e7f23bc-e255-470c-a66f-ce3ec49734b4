const sql = require("./db.js");

// Constructor
const DestinationModel = function(destination) {
  this.nom_locale = destination.nom_locale;
  this.adresse = destination.adresse;
  this.phone = destination.phone;
  this.email = destination.email;
  this.id_region = destination.id_region;
  this.id_fournisseur = destination.id_fournisseur;
  this.id_types = destination.id_types;
};

DestinationModel.create = (newDestination, result) => {
  // Copier newDestination pour éviter de modifier l'objet d'origine
  const destinationWithoutTypes = { ...newDestination };

  // Retirer id_types de destinationWithoutTypes
  delete destinationWithoutTypes.id_types;

  sql.query("INSERT INTO destination SET ?", destinationWithoutTypes, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

  //  //console.log("created destination: ", { id: res.insertId, ...destinationWithoutTypes });

    if (newDestination.id_types && newDestination.id_types.length > 0) {
      const destinationId = res.insertId;

      // Créer une liste de tableaux pour chaque paire (id_destination, id_typecommande)
      const destinationTypeCommandeValues = newDestination.id_types.map(typeId => [destinationId, typeId]);

      // Ajouter chaque paire (id_destination, id_typecommande) individuellement
      sql.query("INSERT INTO destination_typecommande (id_destination, id_typecommande) VALUES ?", [destinationTypeCommandeValues], (err, res) => {
        if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
        }

        //console.log("created destination_typecommande records");

        // Renvoyer le résultat avec l'ID de la destination
        result(null, { id: destinationId, ...newDestination });
      });
    } else {
      // Si pas d'ID de types, renvoyer le résultat avec l'ID de la destination
      result(null, { id: res.insertId, ...destinationWithoutTypes });
    }
  });
};





DestinationModel.findAll = (result) => {
    sql.query("SELECT * FROM destination", (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(err, null);
            return;
        }

        //console.log("destination: ", res);
        result(null, res);
    });
};

DestinationModel.delete = (destinationId, result) => {
  sql.query("DELETE FROM destination WHERE id = ?", destinationId, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {

        result({ kind: "not_found" }, null);
      return;
    }

    //console.log("Deleted destination with id: ", destinationId);
    result(null, res);
  });
};


DestinationModel.findByVilleAndFournisseur = (id_ville, id_fournisseur, id_typecommande, result) => {
  const query = 'SELECT destination.* FROM destination ' +
    'JOIN region ON destination.id_region = region.id ' +
    'JOIN destination_typecommande ON destination.id = destination_typecommande.id_destination ' +
    'WHERE region.id_ville = ' + id_ville + ' AND destination.id_fournisseur = ' + id_fournisseur +
    ' AND destination_typecommande.id_typecommande = ' + id_typecommande;

  //console.log("Executing query:", query);

  sql.query(
    query,
    (err, res) => {
      if (err) {
        console.error("Error executing query: ", err);
        result(err, null);
        return;
      }

    //  //console.log("destination: ", res);
      result(null, res);
    }
  );
};

DestinationModel.findAllDestinationHaveType = (result) => {
  sql.query(
    'SELECT DISTINCT destination.* FROM destination ' +
    'JOIN destination_typecommande ON destination.id = destination_typecommande.id_destination ' +
    'WHERE EXISTS (' +
    '   SELECT 1 FROM destination_typecommande ' +
    '   WHERE destination_typecommande.id_destination = destination.id ' +
    ')',
    [],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

    //  //console.log("destination: ", res);
      result(null, res);
    }
  );
};




DestinationModel.findByIdClient = (idClient, result) => {
  const query = "SELECT destination.id AS destination_id, destination.nom_locale AS destination_nom, destination.adresse, destination.phone, destination.email, region.id AS region_id, region.nom_region AS nom_region, ville.id AS ville_id, ville.nom_ville AS nom_ville, typecommande.id AS type_id, typecommande.nom_type AS type_nom FROM destination " +
                "JOIN destination_typecommande ON destination.id = destination_typecommande.id_destination " +
                "JOIN typecommande ON destination_typecommande.id_typecommande = typecommande.id " +
                "JOIN region ON destination.id_region = region.id " +
                "JOIN ville ON region.idVille = ville.id " +
                "WHERE destination.id_fournisseur = ?";
  sql.query(query, idClient, (err, res) => {
      if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
      }

      // Création d'un objet pour stocker les destinations avec leurs types, régions et villes
      const destinationsWithTypes = {};

      // Itérer sur les résultats pour créer une structure de données appropriée
      res.forEach(row => {
          const destinationId = row.destination_id;
          const destinationNom = row.destination_nom;
          const adresse = row.adresse;
          const phone = row.phone;
          const email = row.email;
          const regionId = row.region_id; // Ajout de l'ID de la région
          const nomRegion = row.nom_region;
          const villeId = row.ville_id; // Ajout de l'ID de la ville
          const nomVille = row.nom_ville;
          const typeId = row.type_id;
          const typeNom = row.type_nom;

          // Vérifier si la destination existe déjà dans l'objet
          if (!destinationsWithTypes.hasOwnProperty(destinationId)) {
              // Si la destination n'existe pas encore, l'ajouter avec un tableau vide pour stocker ses types
              destinationsWithTypes[destinationId] = {
                  id: destinationId,
                  nom: destinationNom,
                  adresse: adresse,
                  phone: phone,
                  email: email,
                  region_id: regionId, // Ajout de l'ID de la région
                  nom_region: nomRegion,
                  ville_id: villeId, // Ajout de l'ID de la ville
                  nom_ville: nomVille,
                  types: []
              };
          }

          // Ajouter le type à la destination correspondante
          destinationsWithTypes[destinationId].types.push({
              id: typeId,
              nom_type: typeNom
          });
      });

      //console.log("Destinations avec types, régions et villes: ", destinationsWithTypes);

      // Mettre les destinations dans un objet 'data'
      result(null, destinationsWithTypes);
  });
};


DestinationModel.updateById = (id, nom_locale, adresse, phone, email, id_region,code_erp, result) => {
  sql.query(
    "UPDATE destination SET nom_locale = ?, adresse = ?, phone = ?, email = ?, id_region = ?,code_erp =? WHERE id = ?",
    [nom_locale, adresse, phone, email, id_region,code_erp, id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      if (res.affectedRows == 0) {
        // Destination non trouvée avec l'ID
        result({ kind: "not_found" }, null);
        return;
      }

      //console.log("Destination mise à jour: ");
      result(null, { id: id});
    }
  );
};





DestinationModel.deleteDestination = (id, result) => {
  // Vérifier si l'ID de la destination est utilisé dans des lignes de commande
  sql.query(
    "SELECT * FROM point_chargement WHERE from_destination = ? OR to_destination = ? LIMIT 1",
    [id, id],
    (err, res) => {
      if (err) {
        //console.log("Erreur lors de la recherche dans la table point_chargement: ", err);
        result({ message: "Erreur lors de la recherche dans la table point_chargement." }, null);
        return;
      }

      // Si des lignes de commande utilisent cette destination, annuler la suppression
      if (res.length > 0) {
        //console.log(res, "--------------+++++++++++++++++++++++")
        result({ kind: "existe" }, null);

        return ;
    }
    

      // Supprimer la destination si elle n'est pas utilisée dans des lignes de commande
      sql.query(
        "DELETE FROM destination_typecommande WHERE id_destination = ?",
        [id],
        (err, res) => {
            if (err) {
                //console.log("Erreur lors de la suppression des lignes de destination_typecommande: ", err);
                result({ message: "Erreur lors de la suppression des lignes de destination_typecommande." }, null);
                return;
            }
    
            // Après avoir supprimé les lignes de destination_typecommande, supprimer la destination de la table destination
            sql.query(
                "DELETE FROM destination WHERE id = ?",
                [id],
                (err, res) => {
                    if (err) {
                        //console.log("Erreur lors de la suppression de la destination: ", err);
                        result({ message: "Erreur lors de la suppression de la destination." }, null);
                        return;
                    }
    
                    if (res.affectedRows == 0) {
                        // Destination non trouvée avec l'ID
                        result({ kind: "not_found" }, null);
                        return;
                    }
    
                    //console.log("Destination supprimée avec succès: ");
                    result(null, { id: id });
                }
            );
        }
    );
    }
  );
};



DestinationModel.updateTypes = (id, idList, result) => {
  sql.query("SELECT * FROM destination_typecommande WHERE id_destination = ?", id, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    //console.log("destination: ", res);
    // Récupérer les id_typecommande de chaque ligne retournée par la requête
    const existingIdTypeCommandes = res.map(row => row.id_typecommande);

    // Comparer idList avec existingIdTypeCommandes
    const idListSorted = idList.slice().sort();
    const existingIdTypeCommandesSorted = existingIdTypeCommandes.slice().sort();

    if (JSON.stringify(idListSorted) === JSON.stringify(existingIdTypeCommandesSorted)) {
      // Si les deux listes sont identiques, lancer une erreur
      const error = new Error("Aucune modification détectée.");
      error.status = 400;
      result(error, null);
      return;
    }

    // Continuer avec l'ajout et la suppression comme précédemment
    // Ajouter des lignes pour les id_typecommande manquants dans idList
    const missingIdTypeCommandes = idList.filter(id => !existingIdTypeCommandes.includes(id));
    missingIdTypeCommandes.forEach(idTypeCommande => {
      sql.query("INSERT INTO destination_typecommande (id_destination, id_typecommande) VALUES (?, ?)", [id, idTypeCommande], (err, res) => {
        if (err) {
          console.log("error: ", err);
          return;
        }
        //console.log("Inserted id_typecommande:", idTypeCommande);
      });
    });

    // Supprimer des lignes pour les id_typecommande qui ne sont pas dans idList
    existingIdTypeCommandes.forEach(idTypeCommande => {
      if (!idList.includes(idTypeCommande)) {
        sql.query("DELETE FROM destination_typecommande WHERE id_destination = ? AND id_typecommande = ?", [id, idTypeCommande], (err, res) => {
          if (err) {
            console.log("error: ", err);
            return;
          }
          //console.log("Deleted id_typecommande:", idTypeCommande);
        });
      }
    });

    result(null, res);
  });
};

DestinationModel.findDestinationById = (id, result) => {
  sql.query(
    'SELECT * FROM destination WHERE id = ?',
    [id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      //console.log("destination: ", res);
      result(null, res);
    }
  );
};

DestinationModel.findDestinationByCompany = (id, result) => {
  sql.query(
    'SELECT * FROM destination WHERE id_fournisseur = ?',
    [id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

     // //console.log("destination: ", res);
      result(null, res);
    }
  );
};

DestinationModel.assignDestinationToUser = (userId, newDestinations, callback) => {
  // Récupérer les destinations associées à l'utilisateur
  sql.query(
    'SELECT id_destination FROM user_destination WHERE id_user = ?',
    [userId],
    (err, existingResults) => {
      if (err) {
        console.error("Erreur lors de la récupération des destinations associées:", err);
        callback(err, null);
        return;
      }

      // Convertir les destinations existantes en un ensemble pour garantir l'unicité
      const existingDestinationsSet = new Set(existingResults.map((r) => r.id_destination));

      // Assurez-vous de ne garder que les nouvelles destinations uniques
      const uniqueNewDestinations = [...new Set(newDestinations)];

      // Filtrer pour obtenir uniquement les destinations qui ne sont pas dans `existingDestinationsSet`
      const destinationsToAdd = uniqueNewDestinations.filter(
        (destinationId) => !existingDestinationsSet.has(parseInt(destinationId))
      );

      if (destinationsToAdd.length === 0) {
        callback(null, "Aucune nouvelle destination à ajouter.");
        return;
      }

      // Préparer les valeurs pour l'insertion
      const valuesToInsert = destinationsToAdd.map((destinationId) => [userId, destinationId]);

      // Insérer les nouvelles destinations dans la base de données
      sql.query(
        'INSERT INTO user_destination (id_user, id_destination) VALUES ?',
        [valuesToInsert],
        (insertErr, insertRes) => {
          if (insertErr) {
            console.error("Erreur lors de l'ajout des nouvelles destinations:", insertErr);
            callback(insertErr, null);
            return;
          }

          callback(null, `Nouvelles destinations ajoutées avec succès : ${insertRes.affectedRows}`);
        }
      );
    }
  );
};







DestinationModel.findDestinationByUser = (id, callback) => {
  const query = `
    SELECT 
      ud.id_user, 
      ud.id_destination, 
      d.nom_locale, 
      d.id
    FROM 
      user_destination ud
    JOIN 
      destination d ON ud.id_destination = d.id 
    WHERE 
      ud.id_user = ?
  `;

  sql.query(query, [id], (err, res) => {
    if (err) {
      console.error("Erreur lors de la récupération des destinations:", err);
      callback(err, null);
      return;
    }

    //console.log("Destinations associées à l'utilisateur:", res);
    callback(null, res); // Retourner les résultats des destinations associées
  });
};






DestinationModel.deleteDestinationByUser = (destinationId, callback) => {
  const query = `
    DELETE FROM 
      user_destination
    WHERE 
      id_destination = ?  -- Assurez-vous de mentionner la bonne colonne
  `;

  sql.query(query, [destinationId], (err, res) => {
    if (err) {
      console.error("Erreur lors de la suppression de la destination:", err);
      callback(err, null);
      return;
    }

    // Vérifiez le nombre de lignes affectées pour confirmer la suppression
    if (res.affectedRows === 0) {
      callback(null, `Aucune destination trouvée avec l'ID ${destinationId}.`);
      return;
    }

    //console.log("Destination supprimée avec succès:", res);
    callback(null, `Destination supprimée avec succès: ${res.affectedRows} ligne(s) affectée(s).`);
  });
};

DestinationModel.findAllWarhouses = (callback) => {
  const query = "SELECT * FROM `warehouse` WHERE depot = TRUE ORDER BY `id` ASC";
  sql.query(query, (err, rows) => {
    if (err) {
      console.error("Erreur lors de la récupération des entrepôts :", err);
      callback(err, null);
      return;
    }
    callback(null, rows);
  });
};


DestinationModel.findWarhousesByBrand = (id_brand, type, result) => {
  sql.query(
    "SELECT * FROM `warehouse` WHERE id = 1 OR (id_brand = ? AND source = 'CEGID' AND type = ? AND depot = true) ORDER BY id ASC",
    [id_brand, type], 
    (err, res) => {
      if (err) {
        console.error("Erreur SQL: ", err);
        result(err, null);
        return;
      }
      result(null, res);
    }
  );
};


DestinationModel.disableWharehouse = (id, result) => {
  sql.query("UPDATE warehouse SET depot = false WHERE id = ?", [id], (err, res) => {
      if (err) {
          console.log("SQL Error: ", err);
          result(err, null);
          return;
      }

      if (res.affectedRows === 0) {
          // Aucun enregistrement trouvé avec l'ID spécifié
          result({ kind: "not_found" }, null);
          return;
      }

      result(null, { message: "warehouse disabled successfully" });
  });
};




DestinationModel.addWarehouse = (warehouse, result) => {
  const query = "INSERT INTO warehouse (name, code, depot) VALUES (?, ?, ?)";
  const values = [warehouse.name, warehouse.code, warehouse.depot || false];

  sql.query(query, values, (err, res) => {
    if (err) {
      console.error("error: ", err);
      result(err, null);
      return;
    }

    console.log("Warehouse added: ", { id: res.insertId, ...warehouse });
    result(null, { id: res.insertId, ...warehouse });
  });
};





module.exports = DestinationModel;
