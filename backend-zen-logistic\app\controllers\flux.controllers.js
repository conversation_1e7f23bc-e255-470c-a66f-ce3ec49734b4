const Flux = require ("../models/flux.model.js") ; 


exports.getAll = (req, res) => {
    Flux.getAll((err, data) => {
      if (err)
        res.status(500).send({
          message:
            err.message || "Some error occurred while retrieving Impot."
        });
      else res.send(data);
    });
  };

  exports.getByClientAndDate = (req, res) => {
    const { id_ste, datedebut, datefin } = req.body;
    if (!id_ste || !datedebut || !datefin) {
      return res.status(400).send({
        message: "id_societe, datedebut, and datefin sont obligatoire."
      });
    }
    if (new Date(datedebut) >= new Date(datefin)) {
      return res.status(400).send({
        message: "La date de début doit être inférieure à la date de fin."
      });
    }
      Flux.getByClientAndDate(id_ste, datedebut, datefin, (err, data) => {
      if (err) {
        res.status(500).send({
          message: err.message || "Some error occurred while retrieving data."
        });
      } else {
        res.send(data);
      }
    });
  };
  
  exports.getFluxByidFacture = (req, res) => {
    const id_facture = req.params.id_facture; 

    Flux.getFluxByidFacture(id_facture, (err, data) => {
        if (err) {
            res.status(500).send({
                message: err.message || "Une erreur s'est produite lors de la récupération des flux."
            });
        } else {
            res.send(data); 
        }
    });
};
  


exports.AllBrand = (req, res) => {
  Flux.AllBrand((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving Impot."
      });
    else res.send(data);
  });
};


exports.AddFormula = (req, res) => {
  const data = req.body
  Flux.AddFormula(data, (err, data) => {
    if (err)
      res.status(500).send({
        message: err.message || "Some error occurred while adding the formula."
      });
    else
      res.send({ message: "Formula added successfully.", data: data });
  });
};



exports.getAllFormula = (req, res) => {
  Flux.getAllFormula((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving Impot."
      });
    else res.send(data);
  });
};



exports.disableFormula = (req, res) => {
  const formulaId = req.params.id; // Récupération de l'ID depuis l'URL

  if (!formulaId) {
    return res.status(400).send({ message: "Formula ID is required" });
  }

  Flux.disableFormula(formulaId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).send({ message: "Formula not found" });
      }
      return res.status(500).send({ message: "Error disabling formula" });
    }

    res.send({ message: "Formula disabled successfully", data });
  });
};

