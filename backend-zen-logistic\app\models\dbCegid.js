const mssql = require('mssql');
const config = require('../config/db.cegid.js');

const poolPromise = new mssql.ConnectionPool(config)
  .connect()
  .then(pool => {
    console.log('Connected to CEGID');
    return pool;  // Retourne le pool pour être utilisé ailleurs
  })
  .catch(err => {
    console.error('SQL Server connection failed', err);
    throw err;
  });

module.exports = poolPromise;  // Exporter le pool connecté
