/**
 * @swagger
 * tags:
 *   - name: Customer
 *     description: API pour gérer les utilisateurs (clients, expéditeurs, chefs, etc.)
 */

/**
 * @swagger
 * /api/customer/lastConnexion/{id}:
 *   put:
 *     summary: Met à jour la date de dernière connexion d'un utilisateur
 *     tags: [Customer]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID de l'utilisateur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Dernière connexion mise à jour avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   example: 1
 *                 last_connexion:
 *                   type: string
 *                   format: date-time
 *                   example: '2025-05-26T14:48:00.000Z'
 *       404:
 *         description: Utilisateur non trouvé
 *       500:
 *         description: Erreur serveur interne
 */

/**
 * @swagger
 * /api/customer/entreprises:
 *   get:
 *     summary: Récupère toutes les entreprises distinctes des expéditeurs
 *     tags: [Customer]
 *     responses:
 *       200:
 *         description: Liste des entreprises
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   num_tva:
 *                     type: string
 *                     example: "123456789"
 *                   raison_sociale:
 *                     type: string
 *                     example: "Entreprise ABC"
 *                   adresse:
 *                     type: string
 *                     example: "123 rue Exemple"
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customer/client:
 *   post:
 *     summary: Associer un client à un expéditeur
 *     tags: [Customer]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               idExp:
 *                 type: integer
 *                 example: 5
 *               idClient:
 *                 type: integer
 *                 example: 12
 *     responses:
 *       200:
 *         description: Client associé avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 exp:
 *                   type: integer
 *                 client:
 *                   type: integer
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customer/client:
 *   delete:
 *     summary: Supprimer l'association entre un client et un expéditeur
 *     tags: [Customer]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               idExp:
 *                 type: integer
 *                 example: 5
 *               idClient:
 *                 type: integer
 *                 example: 12
 *     responses:
 *       200:
 *         description: Association supprimée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 exp:
 *                   type: integer
 *                 client:
 *                   type: integer
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customer/client/disable:
 *   put:
 *     summary: Désactive un client
 *     tags: [Customer]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               idClient:
 *                 type: integer
 *                 example: 12
 *     responses:
 *       200:
 *         description: Client désactivé avec succès
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customer/clients:
 *   get:
 *     summary: Récupère tous les clients actifs
 *     tags: [Customer]
 *     responses:
 *       200:
 *         description: Liste des clients actifs
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   nom:
 *                     type: string
 *                   prenom:
 *                     type: string
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customer/clients/exp/{id}:
 *   get:
 *     summary: Récupère les clients liés à un expéditeur
 *     tags: [Customer]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID de l'expéditeur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des clients pour l'expéditeur
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   nom:
 *                     type: string
 *                   prenom:
 *                     type: string
 *       404:
 *         description: Aucun client trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customer/email/{email}:
 *   get:
 *     summary: Recherche un utilisateur par email
 *     tags: [Customer]
 *     parameters:
 *       - in: path
 *         name: email
 *         required: true
 *         description: Email à rechercher
 *         schema:
 *           type: string
 *           format: email
 *     responses:
 *       200:
 *         description: Utilisateur trouvé
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *       404:
 *         description: Utilisateur non trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customer/clients/notInChef:
 *   get:
 *     summary: Récupère tous les utilisateurs qui ne sont pas chefs
 *     tags: [Customer]
 *     responses:
 *       200:
 *         description: Liste des utilisateurs sans rôle de chef
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   nom_utilisateur:
 *                     type: string
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customer/chefs:
 *   get:
 *     summary: Récupère tous les utilisateurs avec rôle "Chef Departement"
 *     tags: [Customer]
 *     responses:
 *       200:
 *         description: Liste des chefs de département
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customer/chefUser:
 *   post:
 *     summary: Crée ou met à jour les associations entre chef et utilisateurs
 *     tags: [Customer]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id_chef:
 *                 type: integer
 *               id_users:
 *                 type: array
 *                 items:
 *                   type: integer
 *             required:
 *               - id_chef
 *               - id_users
 *     responses:
 *       200:
 *         description: Associations mises à jour ou créées avec succès
 *       400:
 *         description: Requête invalide (id_chef ou id_users manquant)
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customer/usersByChef/{chefId}:
 *   get:
 *     summary: Récupère les utilisateurs associés à un chef
 *     tags: [Customer]
 *     parameters:
 *       - in: path
 *         name: chefId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID du chef
 *     responses:
 *       200:
 *         description: Liste des utilisateurs associés au chef
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   nom_utilisateur:
 *                     type: string
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customer/client/color/{id}:
 *   put:
 *     summary: Met à jour la couleur d'un client
 *     tags: [Customer]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID du client
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               couleur:
 *                 type: string
 *                 example: "#FF0000"
 *     responses:
 *       200:
 *         description: Couleur mise à jour
 *       404:
 *         description: Client non trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customers:
 *   post:
 *     summary: Crée un nouvel utilisateur
 *     tags: [Customer]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sexe:
 *                 type: string
 *               nom:
 *                 type: string
 *               prenom:
 *                 type: string
 *               cin:
 *                 type: string
 *               image_cin:
 *                 type: string
 *               type_utilisateur:
 *                 type: string
 *               forme_juridique:
 *                 type: string
 *               raison_sociale:
 *                 type: string
 *               num_tva:
 *                 type: string
 *               email:
 *                 type: string
 *               adresse:
 *                 type: string
 *               mobile:
 *                 type: string
 *               nom_utilisateur:
 *                 type: string
 *               mot_de_passe:
 *                 type: string
 *               statut:
 *                 type: string
 *               cle_activation:
 *                 type: string
 *               client_direct:
 *                 type: string
 *     responses:
 *       200:
 *         description: Utilisateur créé avec succès
 *       400:
 *         description: Requête invalide
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customers/login:
 *   post:
 *     summary: Authentifie un utilisateur par email et mot de passe
 *     tags: [Customer]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               mot_de_passe:
 *                 type: string
 *     responses:
 *       200:
 *         description: Utilisateur authentifié
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       404:
 *         description: Utilisateur non trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customers:
 *   get:
 *     summary: Récupère tous les utilisateurs
 *     tags: [Customer]
 *     responses:
 *       200:
 *         description: Liste des utilisateurs
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customers/{customerId}:
 *   get:
 *     summary: Récupère un utilisateur par son ID
 *     tags: [Customer]
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID de l'utilisateur
 *     responses:
 *       200:
 *         description: Utilisateur trouvé
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       404:
 *         description: Utilisateur non trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/custom/{customerId}:
 *   put:
 *     summary: Met à jour un utilisateur par son ID
 *     tags: [Customer]
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID de l'utilisateur
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Utilisateur mis à jour
 *       404:
 *         description: Utilisateur non trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/customers/delate/{customerId}:
 *   get:
 *     summary: Supprime un utilisateur par son ID
 *     tags: [Customer]
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID de l'utilisateur
 *     responses:
 *       200:
 *         description: Utilisateur supprimé
 *       404:
 *         description: Utilisateur non trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/email:
 *   post:
 *     summary: Envoie un email d'activation ou autre
 *     tags: [Customer]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               content:
 *                 type: string
 *     responses:
 *       200:
 *         description: Email envoyé
 *       400:
 *         description: Requête invalide
 *       500:
 *         description: Erreur serveur
 */
