const NotUser = require ("../models/notification-user.model.js") ; 

 
 // Create a Camion
 exports.create = (req, res)=>
 { 
   console.log('notification')
   if(!req.body){
     res.status(400).send({
       message: "Content can not be empty"
     })
   }
  
  
  const notUser = new NotUser({
   // id:req.body.id ,
   id_user:req.body.id_user ,
   onesignal: req.body.onesignal,
     
   

  });


  NotUser.create(notUser, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while creating the camion ."
      });
    else res.send(data);
  });
}


// Retrieve all Camion from the database.
/*exports.findAll = (req, res) => {
  Camion.getAll((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving Camion."
      });
    else res.send(data);
  });
 
};

// Delete a camion with the specified camionId in the request
exports.delete = (req, res) => {
  Camion.remove(req.params.camionId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found camion with id ${req.params.camionId}.`
        });
      } else {
        res.status(500).send({
          message: "Could not delete camion with id " + req.params.camionId
        });
      }
    } else res.send({ message: `camion was deleted successfully!` });
  });
};

// Find a single camion with a camionId
exports.findOne = (req, res) => {

  Camion.findById(req.params.camionId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found camion with id ${req.params.camionId}.`
        });
      } else {
        res.status(500).send({
          message: "Error retrieving camion with id " + req.params.camionId
        });
      }
    } else res.send(data);
  });
};



   // Retrieve all Camion from the database 
   exports.findAllCamion= (req, res) => {
    //console.log('rrrr')
    Camion.findCamion(req.params.idUser ,  (err, data) => {
      console.log(req.params)
    console.log(data)
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Camion with idUser ${req.params.idUser} `
        });
      } else {
        res.status(500).send({
          message: "Error retrieving Camion with idUser " + req.params.idUser 
        });
      }
    } else res.send(data);
  });
  };
*/
