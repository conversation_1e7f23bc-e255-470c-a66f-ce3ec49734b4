import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, NgForm, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from "@angular/router";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { id } from '@swimlane/ngx-charts/release/utils';
import { RegisterServiceService } from 'app/services/register-service.service';
import { AuthGuard } from 'app/shared/auth/auth-guard.service';
import { AuthService } from 'app/shared/auth/auth.service';
import { Md5 } from 'md5-typescript';
//import { NGXToastrService } from 'app/components/extra/toastr/toastr.service';
import { ToastrService } from 'ngx-toastr'

@Component({
    selector: 'app-login-page',
    templateUrl: './login-page.component.html',
    styleUrls: ['./login-page.component.scss'],
    providers: [
        //NGXToastrService
    ]

})

export class LoginPageComponent implements OnInit {
    loginForm: FormGroup;
    loading = false;
    submitted = false;
    statut: string;
    logos=[]
    constructor(private toastr: ToastrService, private router: Router,
        private route: ActivatedRoute,
        private formBuilder: FormBuilder,
        private loginService: RegisterServiceService,
        private modalService: NgbModal,
        private authService: AuthService) { }

    ngOnInit() {
        this.logos = new Array(100)
        this.loginForm = this.formBuilder.group({

            nomutilisateur: ['', Validators.required],
            motdepasse: ['', Validators.required],
        })
        sessionStorage.clear();

    };

    get f() { return this.loginForm.controls; }





    // On Forgot password link click
    onForgotPassword() {
        this.router.navigate(['forgotpassword'], { relativeTo: this.route.parent });
    }
    // On registration link click
    onRegister() {
        this.router.navigate(['register'], { relativeTo: this.route.parent });
    }

    generateMdp(mdp) {
        return Md5.init(mdp);
    }
ng 
    connexion() {
        
        // console.log(this.f.nomutilisateur.value) 
        //console.log(this.f.motdepasse.value) 

        this.loading = true;
        this.submitted = true;
        sessionStorage.setItem('loggedIn', 'true');

        this.loginService.findauth({ "email": this.f.nomutilisateur.value, "mot_de_passe": this.generateMdp(this.f.motdepasse.value) }).subscribe(data => {
            this.loading = false;
            console.log("utilisateur: ", data)
            console.log(data.statut == "activé" && (data.type_utilisateur == "Expediteur" || data.type_utilisateur == "Client" || data.type_utilisateur == "Inspection"));
            this.authService.saveRole(data.type_utilisateur);
            console.log("eeeeeeeeee", data.type_utilisateur)
            this.loginService.lastConnexion(data.id).subscribe(res=>{console.log(res)})

            // console.log(data) ; 
            if (data.email !== this.f.nomutilisateur.value || (data.mot_de_passe !== this.generateMdp(this.f.motdepasse.value))) {

                this.toastr.error('utilisateur introuvable', 'Oups!', { closeButton: true });

            }
            else
                if (data.statut == "Invalide") {
                    this.loading = false;
                    this.toastr.info('Veuillez consulter votre email pour activer votre compte', '', { closeButton: true });
                }
                else
                    if (data.statut == "En cours de validation") {

                        this.loading = false;
                        this.toastr.info('Veuillez attendre la validation de lAdmin', '', { closeButton: true });
                    }
                    

                    else
                    sessionStorage.setItem('iduser', (data['id']))
                    sessionStorage.setItem('color', (data['color']))
                    sessionStorage.setItem('code', (data['code']))

                    sessionStorage.setItem('nom_utilisateur', (data['nom_utilisateur']))     
                        if (data.statut == "activé" && data.type_utilisateur == "Transporteur") {
                            this.router.navigate(['profiletransporteur'], { relativeTo: this.route.parent });

                        }
                        else
                            if (data.statut == "activé" && data.type_utilisateur == "Administrateur") {
                                this.router.navigate(['home'], { relativeTo: this.route.parent });
                            }

                            else
                                if (data.statut == "activé" && data.type_utilisateur == "SuperAdmin") {
                                    this.router.navigate(['home'], { relativeTo: this.route.parent });
                                }

                                else
                                    if (data.statut == "activé" && (data.type_utilisateur == "Expediteur" || data.type_utilisateur == "Client" || data.type_utilisateur == "Inspection" || data.type_utilisateur == "GS" || data.type_utilisateur == "GE")) {
                                        this.router.navigate(['profilexpediteur'], { relativeTo: this.route.parent });
                                    }
                                    else
                                        if (data.statut == "activé" && data.type_utilisateur == "Chef Departement") {
                                            this.router.navigate(['profileChef'], { relativeTo: this.route.parent });
                                        } else
                                            if (data.statut == "activé" && data.type_utilisateur == "Depot") {
                                                this.router.navigate(['profilexpediteur'], { relativeTo: this.route.parent });
                                            }
                                            else
                                            if (data.statut == "activé" && data.type_utilisateur == "FACTURATION") {
                                                this.router.navigate(['profilexpediteur'], { relativeTo: this.route.parent });
                                            }
                                            else
                                            if (data.statut == "activé" && data.type_utilisateur == "MAGASIN") {
                                                this.router.navigate(['profilexpediteur'], { relativeTo: this.route.parent });
                                            }

                                            else
                                                if (data.statut == "activé" && data.type_utilisateur == "") {
                                                    this.router.navigate(['pages/login'], { relativeTo: this.route.parent });

                                                }

                                                

        }
    )
            , error => {
                console.log(error)
                this.loading = false;
                ;

            }

    }

}


