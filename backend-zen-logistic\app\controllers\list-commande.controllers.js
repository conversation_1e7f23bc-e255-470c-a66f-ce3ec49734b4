const Commande = require ("../models/list-commande.model.js") ;
const { v4: uuidv4 } = require('uuid');




require('dotenv').config();
multer = require('multer'),
  bodyParser = require('body-parser');
  const PATH = './uploads';

  // Create a Customer
  var storage = multer.diskStorage({
    destination: function (req, file, cb) {
      cb(null, 'uploads/')
    },
    filename: function (req, file, cb) {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
      cb(null, file.fieldname + '-' + uniqueSuffix + '.pdf');
    },
    
  } )
   
  exports.upload = multer({ storage: storage })


// Creer commande 
exports.create = (req, res)=>
{ 

  if(!req.body){
    res.status(400).send({
      message: "Content can not be empty"
    })
  }
 
 
 const commande = new Commande({
    //id:req.body.id ,
    //ville_depart:req.body.ville_depart ,
  //  ville_arrivee:req.body.ville_arrivee ,
  //  id_type : req.body.id_type,
    type_camion:req.body.type_camion ,
    type_chargement:req.body.type_chargement ,
    //id_circuit : req.body.id_circuit,
    date_depart:req.body.date_depart ,
    date_arrivee:req.body.date_arrivee ,
   // heure_chargement:req.body.heure_chargement ,
  // heure_dechargement_de:req.body.heure_dechargement_de,
   // heure_dechargement_a:req.body.heure_dechargement_a,
    type_conditionnement:req.body.type_conditionnement ,
    type_marchandise:req.body.type_marchandise ,
    poids:req.body.poids ,
    volume:req.body.volume ,
    quantite:req.body.quantite ,
    mention:req.body.mention ,
    fragile:req.body.fragile ,
    code:req.body.code ,
    enrg_commande:req.body.enrg_commande ,
    ajoutee_par:req.body.ajoutee_par ,
    reservee_par:req.body.reservee_par	 ,
    statut: req.body.statut, 
    prix: req.body.prix,
    id_camion : req.body.id_camion,
    id_conducteur : req.body.id_conducteur,
    labelle : req.body.labelle,
    nom_conducteur : req.body.nom_conducteur,
    nom_utilisateur : req.body.nom_utilisateur


 });

 /*console.log('ttt')
console.log(Commande)  */

 // Save Conducteur in the database
 Commande.create(commande, (err, data) => {
   if (err)
     res.status(500).send({
       message:
         err.message || "Some error occurred while creating the Commande ."
     });
     
   else res.send(data);
 });
}


// Retrieve all Commande from the database.
exports.findAll = (req, res) => {
  Commande.getAll((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving Commande."
      });
    else res.send(data);
  });
 
};

// Find a single Commande with a commandeId
exports.findOne = (req, res) => {
  Commande.findById(req.params.commandeId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found commande with id ${req.params.commandeId}.`
        });
      } else {
        res.status(500).send({
          message: "Error retrieving commande with id " + req.params.commandeId
        });
      }
    } else res.send(data);
  });
};


exports.sendEmail = (req, res) => {
  if (!req.body.email || !req.body.content ) {

    res.status(400).send({
      message: "Content can not be empty!"
    });
  }
  else
  {
  const sgMail = require('@sendgrid/mail')
  sgMail.setApiKey(process.env.SENDGRID_API_KEY) ; 
  
  const msg = {
    to: req.body.email, 
    from: {email : process.env.EMAIL, name : process.env.NAME }, 
    subject: 'activation ',
    html: req.body.content,
  }
  
  sgMail
    .send(msg)
    .then(() => {
      console.log('Email sent')
      res.send({message: 'Email sent'})
    })
    .catch((error) => {
      console.error(error)
      res.send({message: error})
    })}

};









    // Retrieve all Commande Enrg from the database 
    exports.findCommEnrg = (req, res) => {
      //console.log('rrrr')
      Commande.findEnrg(req.params.idUser ,  (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found commande enregistre with idUser ${req.params.idUser} `
          });
        } else {
          res.status(500).send({
            message: "Error retrieving commande with idUser " + req.params.idUser 
          });
        }
      } else res.send(data);
    });
  };

    // delete Commande Enrg from commande enrigistrer  
    exports.updateEnrgCommande = (req, res) => {
      const commandeId = req.params.commandeId; 
      Commande.updateEnrgCommande(commandeId, (err, result) => {
        if (err) {
          if (err.kind === 'not_found') {
            return res.status(404).json({ message: 'Commande non trouvée.' });
          } else {
            return res.status(500).json({ message: 'Erreur lors de la suppression  de la modele.' });
          }
        }
    
        return res.status(200).json({ message: 'suppression avec succès' });
      });
    };


  // Update a commande by the CommandeId  ( component administrateur )
exports.updateByIdComm = (req, res) => {
  
  // Validate Request
 if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    }); 
  } 
 // else //res.send('tout va bien')
  Commande.updateWithIdComm(req.params.commandeId,
    new Commande(req.body),
    (err, data) =>
     {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found commande with Id  ${req.params.commandeId}.`
          });
        } else {
        //  console.log('test')
          res.status(500).send({
            message: "Error updating commande with Id " + req.params.commandeId
          });
        }
      } else res.send(data); 
    } 
  );
}; 
  

 // Retrieve all Commande Enrg from the database 
 exports.findAllComm = (req, res) => {
  //console.log('rrrr')
  Commande.getAllComm(req.params.idUser ,  (err, data) => {

  if (err) {
    if (err.kind === "not_found") {
      res.status(404).send({
        message: `Not found commande with idUser ${req.params.idUser} `
      });
    } else {
      res.status(500).send({
        message: "Error retrieving commande with idUser " + req.params.idUser 
      });
    }
  } else res.send(data);
});
};







  // Update a commande by the Iduser  component (expéditeur)
  exports.updateCommById = (req, res) => {
    
    // Validate Request
   if (!req.body) {
      res.status(400).send({
        message: "Content can not be empty!"
      }); 
    } 
   // else //res.send('tout va bien')
    Commande.updateCommWithId(req.params.id,
      new Commande(req.body),
      (err, data) =>
       {
        if (err) {
          if (err.kind === "not_found") {
            res.status(404).send({
              message: `Not found commande with id  ${req.params.id}.`
            });
          } else {
          //  console.log('test')
            res.status(500).send({
              message: "Error updating commande with idU " + req.params.id
            });
          }
        } else res.send(data); 
      } 
    );
  }

  exports.updateNote = (req, res) => {
    
    
    // Validate Request
   if (!req.body) {
      res.status(400).send({
        message: "Content can not be empty!"
      }); 
    } 
   // else //res.send('tout va bien')
 
    Commande.updateNote(req.params.id,
      new Commande(req.body),
      (err, data) =>
       {
        if (err) {
          if (err.kind === "not_found") {
            res.status(404).send({
              message: `Not found commande with id  ${req.params.id}.`
            });
          } else {
          //  console.log('test')
            res.status(500).send({
              message: "Error updating commande with idU " + req.params.id
            });
          }
        } else res.send(data); 
      } 
    );
  }

   // Retrieve all Commande validées from the database 
 exports.findAllCom = (req, res) => {
  Commande.getAllCom(req.params.idUser ,  (err, data) => {
  if (err) {
    if (err.kind === "not_found") {
      res.status(404).send({
        message: `Not found commande with idUser ${req.params.idUser} `
      });
    } else {
      res.status(500).send({
        message: "Error retrieving commande with idUser " + req.params.idUser 
      });
    }
  } else res.send(data);
});
};
 // Retrieve all Commande validées from the database of transporteur
   exports.findAllComTrpValide = (req, res) => {
    //console.log('rrrr')
    Commande.getAllComTrpValide(req.params.idUser ,  (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found commande with idUser ${req.params.idUser} `
        });
      } else {
        res.status(500).send({
          message: "Error retrieving commande with idUser " + req.params.idUser 
        });
      }
    } else res.send(data);
  });
  };
  // Retrieve all Commande validées from the database 


  exports.findComm = (req, res) => {
    Commande.getCom(req.params.idUser , (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found commande with idUser ${req.params.idUser} `
        });
      } else {
        res.status(500).send({
          message: "Error retrieving commande with idUser " + req.params.idUser 
        });
      }
    } else res.send(data);
  });
  };


  
   // Update a commande by the transporteurId  component (transporteur!!!!! )
  exports.updateCommParTr = (req, res) => {
    
    // Validate Request
   if (!req.body) {
      res.status(400).send({
        message: "Content can not be empty!"
      }); 
    } 
   // else //res.send('tout va bien')
    Commande.updateCommParTrp(req.params.commandeId,
      new Commande(req.body),
      (err, data) =>
       {
        if (err) {
          if (err.kind === "not_found") {
            res.status(404).send({
              message: `Not found commande with idUser ${req.params.commandeId}.`
            });
          } else {
          //  console.log('test')
            res.status(500).send({
              message: "Error updating commande with idUser " + req.params.commandeId
            });
          }
        } else res.send(data); 
      } 
    );
  }  
  
  

   // Retrieve all Commande reservées from the database (pour trp )
   
 exports.findAllCommRes= (req, res) => {
  //console.log('rrrr')
  Commande.getCommRes(req.params.idUser ,  (err, data) => {

  if (err) {
    if (err.kind === "not_found") {
      res.status(404).send({
        message: `Not found commande with idUser ${req.params.idUser} `
      });
    } else {
      res.status(500).send({
        message: "Error retrieving commande with idUser " + req.params.idUser 
      });
    }
  } else res.send(data);
});
};
  

 // Retrieve all Commande reservées from the database (pour exp )
 exports.findCommRes= (req, res) => {
  //console.log('rrrr')
  Commande.getCommResExp(req.params.idUser ,  (err, data) => {

  if (err) {
    if (err.kind === "not_found") {
      res.status(404).send({
        message: `Not found commande with idUser ${req.params.idUser} `
      });
    } else {
      res.status(500).send({
        message: "Error retrieving commande with idUser " + req.params.idUser 
      });
    }
  } else res.send(data);
});


};
exports.findCom = (req, res) => {

 Commande.findCom(req.params.conducteurId ,  (err, data) => {
    
  
  if (err) {
    console.log(err)
    if (err.kind === "not_found") {
      res.status(404).send({
        message: `Not found commande with conducteurId ${req.params.conducteurId}`
      });
    } else {
      res.status(500).send({
        message: "Error retrieving commande with conducteurId " + req.params.conducteurId
      });
    }
  } else res.send(data);
});
};



exports.uploadPdfCommande = async (req, res) => {
  try {
    if (!req.files || Object.keys(req.files).length === 0) {
      console.log("Aucun fichier disponible!");
      return res.status(400).send({
        success: false,
        message: 'Aucun fichier disponible.'
      });
    }

    const pdfAttachment = req.files.pdfAttachment;

    // Ajoutez des logs pour afficher des informations utiles
    console.log('Taille du fichier reçu :', pdfAttachment.size);

    // Reste du code pour déplacer le fichier, enregistrer les informations dans la base de données, etc.
    // Vous pouvez utiliser la même logique que celle que vous avez utilisée pour l'image.

    return res.status(200).send({
      success: true,
      message: 'Fichier PDF importé avec succès.',
      fileName: pdfAttachment.name,
      fileSize: pdfAttachment.size
    });
  } catch (error) {
    console.error(error);
    return res.status(500).send({
      success: false,
      message: 'Erreur lors de l\'importation du fichier PDF.'
    });
  }
};


exports.findComARes = (req, res) => {
  Commande.findComARes((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving Commande."
      });
    else res.send(data);
  });
 
};

exports.delete = (req, res) => {
  const id = req.params.id; // Récupérer l'ID de la commande à supprimer depuis les paramètres de la requête
  Commande.delete(id, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while deleting Commande."
      });
    else res.send({ message: "Commande was deleted successfully!" });
  });
};



exports.updateVolume = (req, res) => {
  // Récupérez l'ID de la commande depuis les paramètres de l'URL
  const commandeId = req.params.id;

  // Récupérez le nouveau volume depuis le corps de la requête
  const newVolume = req.body.volume;

  // Appelez la méthode updateVolume du modèle Commande
  Commande.updateVolume(commandeId, newVolume, (err, result) => {
    if (err) {
      // Gérez l'erreur en renvoyant une réponse appropriée
      if (err.kind === "not_found") {
        return res.status(404).json({ message: `Aucune commande trouvée avec l'ID ${commandeId}.` });
      } else {
        return res.status(500).json({ message: `Erreur lors de la mise à jour de la commande.` });
      }
    }

    // Si la mise à jour a réussi, renvoyez une réponse appropriée
    return res.status(200).json({ message: `Mise à jour de la commande réussie.` });
  });
};


exports.getAllRes = (req, res) => {
  Commande.getAllRes((err, data) => {

    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found commande with idUser `
        });
      } else {
        res.status(500).send({
          message: "Error retrieving commande with idUser "
        });
      }
    } else {
      res.send(data);
    }
  });
};


exports.infoAdmin = (req, res) => {
  Commande.infoAdmin((err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: `Aucun administrateur trouvé.` });
      } else {
        return res.status(500).json({ message: "Erreur lors de la récupération des administrateurs." });
      }
    } else {
      return res.status(200).json({ message: "E-mail envoyé avec succès aux administrateurs." });
    }
  });
};

exports.getAllCommandeByChef = (req, res) => {
  Commande.getAllCommandeByChef(req.params.idChef ,  (err, data) => {

  if (err) {
    if (err.kind === "not_found") {
      res.status(202).send({
        message: `Not found commande with idUser ${req.params.idChef} `
      });
    } else {
      res.status(500).send({
        message: "Error retrieving commande with idUser " + req.params.idChef 
      });
    }
  } else res.send(data);
});
};

exports.getCommandeToValidateByChef = (req, res) => {
  Commande.getCommandeToValidateByChef(req.params.idChef ,  (err, data) => {

  if (err) {
    if (err.kind === "not_found") {
      res.status(202).send({
        message: `Not found commande with idUser ${req.params.idChef} `
      });
    } else {
      res.status(500).send({
        message: "Error retrieving commande with idUser " + req.params.idChef 
      });
    }
  } else res.send(data);
});
};


exports.findEnrgBychef = (req, res) => {
  //console.log('rrrr')
  Commande.findEnrgBychef(req.params.idChef ,  (err, data) => {

  if (err) {
    if (err.kind === "not_found") {
      res.status(202).send({
        message: `Not found commande with idUser ${req.params.idChef} `
      });
    } else {
      res.status(500).send({
        message: "Error retrieving commande with idUser " + req.params.idChef 
      });
    }
  } else res.send(data);
});
};


exports.getAllComValidByChef = (req, res) => {
  Commande.getAllComValidByChef(req.params.idChef ,  (err, data) => {

  if (err) {
    if (err.kind === "not_found") {
      res.status(202).send({
        message: `Not found commande with idUser ${req.params.idChef} `
      });
    } else {
      res.status(500).send({
        message: "Error retrieving commande with idUser " + req.params.idChef 
      });
    }
  } else res.send(data);
});
};



