import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class VoyageService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Cancel voyage
   * @param id - Voyage ID
   * @returns Observable with cancellation result
   * @originalName cancelVoyage
   */
  cancelVoyage(id: number): Observable<any> {
    return this.http.put(`${this.apiURL}cancelVoyage/${id}`, null, httpOptions);
  }

  /**
   * Search voyage by date
   * @param searchData - Search criteria
   * @returns Observable with voyage results
   * @originalName searchVoyageByDate
   */
  searchVoyageByDate(searchData: any): Observable<any> {
    return this.http.post(`${this.apiURL}voyages`, searchData, httpOptions);
  }

  /**
   * Update voyage to adjust
   * @param id - Voyage ID
   * @returns Observable with update result
   * @originalName updateVoyageToAdjust
   */
  updateVoyageToAdjust(id: any): Observable<any> {
    return this.http.get<any>(`${this.apiURL}updateVoyageToAdjust?id_voyage=${id}`, httpOptions);
  }
}
