const sql = require("./db.js");
const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');
require('dotenv').config();
const os = require('os');
const cron = require('node-cron');



const Facture = function (facture) {
  this.id_client = facture.id_client;
  this.created_at = facture.created_at;
  this.tot_facture = facture.tot_facture;
  this.tot_factureTTC = facture.tot_factureTTC;
  this.created_by = facture.created_by;
  this.ligne_ids = facture.ligne_ids;
  this.adresse = facture.adresse;
  this.tva=facture.tva,
  this.timbre=facture.timbre,
  this.type=facture.type,
  this.invoice_date  = facture.invoice_date  
  
};

Facture.create = (newFacture, result) => {
  const ligneIds = newFacture.ligne_ids; // Retrieve ligne_ids from newFacture

  // Get the current year and month
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = (currentDate.getMonth() + 1).toString().padStart(2, '0'); // Ensure the month is two digits

  // Get the incremental number
  sql.query(
    "SELECT COUNT(*) as count FROM facture WHERE YEAR(created_at) = ? ",
    [year],
    (err, res) => {
      if (err) {
        console.log("err: ", err);
        rollbackAndHandleError(err, result, null, null);
        return;
      }

      const numeroIncremental = (res[0].count + 1).toString().padStart(4, '0');

      // Generate the 'code' field in the format year-month-number
      const code = `${year}${numeroIncremental}`;

      // Declare variables to store temporary data for potential rollback
      let factureId = null;
      let rollbackNeeded = false;

      // Perform the insertion with the current date and time, creator, and generated 'code' field
      sql.query(
        "INSERT INTO facture (id_client, created_at, tot_facture, tot_factureTTC, created_by, code, adresse,tva,type,timbre,status,validation,invoice_date) VALUES (?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?,?,?,?)",
        [newFacture.id_client, newFacture.tot_facture, newFacture.tot_factureTTC, newFacture.created_by, code, newFacture.adresse,newFacture.tva,newFacture.type,newFacture.timbre,false,"En attente de validation",newFacture.invoice_date],
        (err, res) => {
          if (err) {
            console.log("err: ", err);
            rollbackNeeded = true;
            rollbackAndHandleError(err, result, factureId, ligneIds);
            return;
          }

          factureId = res.insertId;

          // Now, insert the associated line IDs into the facture_commande table
          const insertLineIdsQuery = "INSERT INTO facture_commande (id_facture, id_ligne) VALUES ?";
          const lineIdsValues = ligneIds.map(id => [factureId, id]);

          sql.query(insertLineIdsQuery, [lineIdsValues], (err, res) => {
            if (err) {
              console.log("err: ", err);
              rollbackNeeded = true;
              rollbackAndHandleError(err, result, factureId, ligneIds);
              return;
            }

            // Update the associated point_chargement records
            const updatePointChargementQuery = "UPDATE point_chargement SET status = 'facturé', date_facturation = NOW() WHERE id IN (?)";
            sql.query(updatePointChargementQuery, [ligneIds], (err, res) => {
              if (err) {
                console.log("err: ", err);
                rollbackNeeded = true;
                rollbackAndHandleError(err, result, factureId, ligneIds);
                return;
              }

              if (!rollbackNeeded) {
                result(null, { id: factureId, ...newFacture, code });
              }
            });
          });
        }
      );
    }
  );

  // Function to rollback changes and handle errors
  function rollbackAndHandleError(err, result, factureId, ligneIds) {
    // Implement rollback logic here
    console.log("Rolling back changes...",factureId,ligneIds);

    // If a facture was inserted, delete it
    if (factureId) {
      sql.query("DELETE FROM facture WHERE id_facture = ?", [factureId], (deleteErr, deleteRes) => {
        if (deleteErr) {
          console.log("Error deleting facture:", deleteErr);
        }
      });
    }

    // If facture_commande entries were inserted, delete them
    if (ligneIds && ligneIds.length > 0) {
      sql.query("DELETE FROM facture_commande WHERE id_facture = ?", [factureId], (deleteErr, deleteRes) => {
        if (deleteErr) {
          console.log("Error deleting facture_commande entries:", deleteErr);
        }
      });
    }

    // If point_chargement records were updated, revert the changes
    if (ligneIds && ligneIds.length > 0) {
      sql.query("UPDATE point_chargement SET statut = 'Livré', date_facturation = NULL WHERE id IN (?)", [ligneIds], (updateErr, updateRes) => {
        if (updateErr) {
          console.log("Error reverting point_chargement updates:", updateErr);
        }
      });
    }

    // Call the result function with the original error
    result(err, null);
  }
};


Facture.getAll = (page, pageSize, idClient, invoiceDate, result) => {
  const offset = (page - 1) * pageSize;
  let query = `
    SELECT facture.*, fournisseur.nom_fournisseur, fournisseur.mat_fisc
    FROM facture
    JOIN fournisseur ON facture.id_client = fournisseur.id
    WHERE 1 = 1
  `;

  let countQuery = `
    SELECT COUNT(*) as totalRows
    FROM facture
    JOIN fournisseur ON facture.id_client = fournisseur.id
    WHERE 1 = 1
  `;

  let queryParams = [];

  // Ajout de l'id_client s'il est fourni
  if (idClient) {
    query += ` AND facture.id_client = ?`;
    countQuery += ` AND facture.id_client = ?`;
    queryParams.push(idClient);
  }

  // Ajout du filtre sur la date s'il est fourni
  if (invoiceDate) {
    query += ` AND DATE_FORMAT(facture.invoice_date, '%Y-%m') = ?`;
    countQuery += ` AND DATE_FORMAT(facture.invoice_date, '%Y-%m') = ?`;
    queryParams.push(invoiceDate);
  }

  // Ajout de l'ordre et de la pagination
  query += ` ORDER BY facture.id_facture DESC LIMIT ? OFFSET ?`;
  queryParams.push(parseInt(pageSize), offset);

  // Exécution de la requête principale
  sql.query(query, queryParams, (err, data) => {
    if (err) {
      console.error("Error: ", err);
      result(null, err);
      return;
    }

    // Exécution de la requête pour compter le nombre total de résultats
    sql.query(countQuery, queryParams.slice(0, queryParams.length - 2), (err, countResult) => {
      if (err) {
        console.error("Error retrieving total rows: ", err);
        result(null, err);
        return;
      }

      const totalRows = countResult[0].totalRows;
      console.log("Total Rows: ", totalRows);

      result(null, { data, totalRows });
    });
  });
};






  Facture.findById = (factureId, result) => {
    sql.query(
      `SELECT point_chargement.*
       FROM facture_commande
       JOIN point_chargement ON facture_commande.id_ligne = point_chargement.id
       WHERE facture_commande.id_facture = ?`,
      factureId,
      (err, res) => {
        if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
        }
  
        if (res.length) {
          result(null, res);
          return;
        }
  
        // not found facture with the id
        result({ kind: "not_found" }, null);
      }
    );
  };
  
  Facture.getAllE = (result) => {
    const query = `
      SELECT *
      FROM facture
      
      ORDER BY created_at DESC;
    `;
  
    sql.query(query, (err, res) => {
      if (err) {
        console.error("Error: ", err);
        result(null, err);
        return;
      }
  
      console.log("Factures: ", res);
      result(null, res);
    });
};

Facture.findLigneFac = (ids, result) => {
  try {
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      throw new Error("Liste d'identifiants invalide.");
    }

    const query = `
      SELECT id_ligne
      FROM facture_commande
      WHERE id_ligne  IN (?);
    `;

    sql.query(query, [ids], (err, res) => {
      if (err) {
        result(err, null);
        return;
      }

      if (res.length === 0) {
        // Utilisation d'une erreur personnalisée pour signaler que rien n'a été trouvé
        const noResultsError = new Error("Aucun résultat trouvé pour les identifiants fournis.");
        noResultsError.kind = 'NoResults';
        result(noResultsError, null);
        return;
      }

      result(null, res);
    });
  } catch (error) {
    console.error("Erreur lors de la recherche des lignes de facture :", error);
    result(error, null);
  }
};

Facture.updateTrue = (factureId, result) => {
    const query = `UPDATE facture SET status = true , validation = 'Par Mail' WHERE id_facture = ? AND (status IS NULL OR status = FALSE)`;

  sql.query(query, [factureId], (error, results) => {
    if (error) {
      console.error("Erreur lors de la mise à jour de la facture :", error);
      return result(error, null);
    }

    if (results.affectedRows === 0) {
      return result({ kind: "not_updated" }, null);
    }

    return result(null, "Facture mise à jour avec succès.");
  });
};


Facture.updateFalse = (factureId, result) => {
  const query = `UPDATE facture SET status = false, validation = 'Par Mail' WHERE id_facture = ? AND (status IS NULL)`;

  sql.query(query, [factureId], (error, results) => {
    if (error) {
      console.error("Erreur lors de la mise à jour de la facture :", error);
      return result(error, null);
    }

    if (results.affectedRows === 0) {
      return result({ kind: "not_updated" }, null);
    }

    return result(null, "Facture mise à jour avec succès.");
  });
};

Facture.createFactureFlux = (newFacture, result) => {
  const ligneIds = newFacture.ligne_ids;
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
  console.log("from model",newFacture)
  sql.query(
    "SELECT COUNT(*) as count FROM facture WHERE YEAR(created_at) = ? ",
    [year],
    (err, res) => {
      if (err) {
        console.log("err: ", err);
        rollbackAndHandleError(err, result, null, null);
        return;
      }

      const numeroIncremental = (res[0].count + 1).toString().padStart(4, '0'); // Assurez-vous que le numéro a quatre chiffres
      const code = `${year}${numeroIncremental}`;

      let factureId = null;
      let rollbackNeeded = false;

      // Étape 1: Vérifier si l'une des lignes flux contient déjà un id_facture
      sql.query(
        "SELECT id FROM flux WHERE id IN (?) AND id_facture IS NOT NULL",
        [ligneIds],
        (err, res) => {
          if (err) {
            console.log("Erreur de vérification id_facture dans flux: ", err);
            result(err, null);
            return;
          }

          if (res.length > 0) {
            // Si au moins une ligne a un id_facture, nous levons une erreur
            result({ message: "Une ou plusieurs lignes flux ont déjà une facture associée." }, null);
            return;
          }

          // Étape 2: Si aucune ligne n'a id_facture, continuer l'insertion de la facture
          console.log("------------------------",newFacture)
          sql.query(
            "INSERT INTO facture (id_client, created_at, tot_facture, tot_factureTTC, created_by, code, adresse,tva,type,timbre,invoice_date) VALUES (?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?,?)",
            [newFacture.id_client, newFacture.tot_facture, newFacture.tot_factureTTC, newFacture.created_by, code, newFacture.adresse,newFacture.tva,newFacture.type,newFacture.timbre,newFacture.invoice_date],
            (err, res) => {
              if (err) {
                console.log("Erreur d'insertion dans facture: ", err);
                rollbackNeeded = true;
                rollbackAndHandleError(err, result, factureId, ligneIds);
                return;
              }

              factureId = res.insertId;
                const updatePointChargementQuery = "UPDATE flux SET id_facture = ? WHERE id IN (?)";
                sql.query(updatePointChargementQuery, [factureId, ligneIds], (err, res) => {
                  if (err) {
                    console.log("Erreur de mise à jour dans flux: ", err);
                    rollbackNeeded = true;
                    rollbackAndHandleError(err, result, factureId, ligneIds);
                    return;
                  }

                  if (!rollbackNeeded) {
                    result(null, { id: factureId, ...newFacture, code });
                  }
                });
            }
          );
        }
      );
    }
  );

  // Fonction de rollback et gestion des erreurs
  function rollbackAndHandleError(err, result, factureId, ligneIds) {
    console.log("Annulation des changements...", factureId, ligneIds);

    if (factureId) {
      sql.query("DELETE FROM facture WHERE id_facture = ?", [factureId], (deleteErr, deleteRes) => {
        if (deleteErr) {
          console.log("Erreur lors de la suppression de facture:", deleteErr);
        }
      });
    }

    result(err, null); // Retourner l'erreur d'origine
  }
};

Facture.createFactureEntreposage = (newFacture, result) => {
  const ligneIds = newFacture.ligne_ids;
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
  sql.query(
    "SELECT COUNT(*) as count FROM facture WHERE YEAR(created_at) = ? ",
    [year],
    (err, res) => {
      if (err) {
        console.log("err: ", err);
        rollbackAndHandleError(err, result, null, null);
        return;
      }

      const numeroIncremental = (res[0].count + 1).toString().padStart(4, '0'); // Assurez-vous que le numéro a quatre chiffres
      const code = `${year}${numeroIncremental}`;

      let factureId = null;
      let rollbackNeeded = false;

      // Étape 1: Vérifier si l'une des lignes flux contient déjà un id_facture
      sql.query(
        "SELECT id FROM entreposage WHERE id IN (?) AND id_facture IS NOT NULL",
        [ligneIds],
        (err, res) => {
          if (err) {
            console.log("Erreur de vérification id_facture dans flux: ", err);
            result(err, null);
            return;
          }

          if (res.length > 0) {
            // Si au moins une ligne a un id_facture, nous levons une erreur
            result({ message: "Une ou plusieurs lignes flux ont déjà une facture associée." }, null);
            return;
          }

          // Étape 2: Si aucune ligne n'a id_facture, continuer l'insertion de la facture
          console.log("------------------------",newFacture)
          sql.query(
            "INSERT INTO facture (id_client, created_at, tot_facture, tot_factureTTC, created_by, code, adresse,tva,type,timbre,invoice_date) VALUES (?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?,?)",
            [newFacture.id_client, newFacture.tot_facture, newFacture.tot_factureTTC, newFacture.created_by, code, newFacture.adresse,newFacture.tva,newFacture.type,newFacture.timbre,newFacture.invoice_date],
            (err, res) => {
              if (err) {
                console.log("Erreur d'insertion dans facture: ", err);
                rollbackNeeded = true;
                rollbackAndHandleError(err, result, factureId, ligneIds);
                return;
              }

              factureId = res.insertId;
                const updatePointChargementQuery = "UPDATE entreposage SET id_facture = ? WHERE id IN (?)";
                sql.query(updatePointChargementQuery, [factureId, ligneIds], (err, res) => {
                  if (err) {
                    console.log("Erreur de mise à jour dans flux: ", err);
                    rollbackNeeded = true;
                    rollbackAndHandleError(err, result, factureId, ligneIds);
                    return;
                  }

                  if (!rollbackNeeded) {
                    result(null, { id: factureId, ...newFacture, code });
                  }
                });
            }
          );
        }
      );
    }
  );

  // Fonction de rollback et gestion des erreurs
  function rollbackAndHandleError(err, result, factureId, ligneIds) {
    console.log("Annulation des changements...", factureId, ligneIds);

    if (factureId) {
      sql.query("DELETE FROM facture WHERE id_facture = ?", [factureId], (deleteErr, deleteRes) => {
        if (deleteErr) {
          console.log("Erreur lors de la suppression de facture:", deleteErr);
        }
      });
    }

    result(err, null); // Retourner l'erreur d'origine
  }
};
const formatDateTime = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Ajoute un zéro devant si nécessaire
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  // Retourne une chaîne formatée
  return `${year}-${month}-${day}_${hours}-${minutes}-${seconds}`;
};

const validateInvoiceData = (facture) => {
  const requiredFields = ['ref', 'site', 'type', 'client', 'devise', 'timbre', 'date', 'items','client_type' ];
  for (const field of requiredFields) {
      if (!facture[field]) {
          throw new Error(`Champ manquant dans la facture : ${field}`);
      }
  }

  if (!Array.isArray(facture.items)) {
      throw new Error("Les articles de la facture doivent être un tableau.");
  }

  facture.items.forEach((item) => {
      const requiredItemFields = ['code', 'unite', 'qte', 'ht'];
      for (const itemField of requiredItemFields) {
          if (!item[itemField]) {
              throw new Error(`Champ manquant dans l'article : ${itemField}`);
          }
      }
  });
};

// Fonction pour formater une facture
const formatInvoice = (facture) => {
  let invoiceLines = [];
  const baseInvoice = `${facture.ref};${facture.site};${facture.type};${facture.client};${facture.client};${facture.devise};${facture.timbre};${facture.date};${facture.client_type};${facture.date};${facture.devise};${facture.site}`;
  facture.items.forEach((item) => {
      let itemLine = `${baseInvoice};${item.code};${item.unite};1;${facture.NTAXE};${item.ht};`;
      invoiceLines.push(itemLine);
  });
  return invoiceLines.join('\n');
};

// Fonction pour écrire un fichier local
const writeLocalFile = (localFilePath, dataToWrite) => {
  return new Promise((resolve, reject) => {
      fs.writeFile(localFilePath, dataToWrite, (err) => {
          if (err) {
              reject(err);
          } else {
              resolve();
          }
      });
  });
};

const connectToNetworkShare = (remoteDir, username, password) => {
  return new Promise((resolve, reject) => {
    const platform = os.platform();

    if (platform === 'win32') {
      console.log(`Removing any existing connection to: ${remoteDir}...`);
      exec(`net use ${remoteDir} /delete`, (deleteErr, deleteStdout, deleteStderr) => {
        if (deleteErr && !deleteStderr.includes("The network connection could not be found")) {
          console.warn(`Warning while deleting connection: ${deleteStderr.trim()}`);
        }

        console.log("Connecting to the network share...");
        exec(`net use ${remoteDir} /user:${username} ${password}`, (connectErr, connectStdout, connectStderr) => {
          if (connectErr) {
            return reject(`Error connecting to network share: ${connectStderr.trim()}`);
          }
          console.log("Successfully connected to the network share.");
          resolve();
        });
      });
    } else if (platform === 'linux' || platform === 'darwin') {
      console.log("Connecting to the network share using smbclient...");
      remoteDir = '//192.168.10.158/FAC-ZLG'

      const command = `smbclient ${remoteDir} -U ${username}%${password}`;

      exec(command, (err, stdout, stderr) => {
        if (err) {
          return reject(`Error connecting to network share with smbclient: ${stderr.trim()}`);
        }
        console.log("Successfully connected to the network share via smbclient.");
        resolve();
      });
    } else {
      reject('Unsupported operating system.');
    }
  });
};

const copyFileToNetworkShare = (localFilePath) => {
  return new Promise((resolve, reject) => {
    const platform = os.platform();  // Récupérer le système d'exploitation

    if (platform === 'win32') {
      // Si Windows, utiliser 'copy'
      console.log(`Copie du fichier ${localFilePath} vers le partage réseau...`);
      exec(`copy "${localFilePath}" "\\\\192.168.10.158\\FAC-ZLG"`, (err, stdout, stderr) => {
        if (err) {
          console.error('Erreur complète :', stderr);  // Afficher l'erreur complète
          reject(`Erreur lors de la copie du fichier sur Windows : ${stderr.trim()}`);
        } else {
          console.log(`Fichier copié avec succès.`);
          resolve();
        }
      });
    } else if (platform === 'linux' || platform === 'darwin') {
      // Si Linux ou MacOS, utiliser le montage réseau et 'cp'
      console.log(`Copie du fichier ${localFilePath} vers le partage réseau sur Linux/MacOS...`);

      // Copier le fichier local vers le répertoire monté
      const mountDir = '/mnt/partage_windows/';
      exec(`sudo cp "${localFilePath}" "${mountDir}"`, (cpErr, cpStdout, cpStderr) => {
        if (cpErr) {
          console.error('Erreur lors de la copie du fichier :', cpStderr);  // Afficher l'erreur de copie
          reject(`Erreur lors de la copie du fichier : ${cpStderr.trim()}`);
        } else {
          console.log(`Fichier copié avec succès.`);
          resolve();
        }
      });

    } else {
      reject('Système d\'exploitation non pris en charge.');
    }
  });
};




Facture.sendToSage = async (factureData, result) => {
  const currentDateTime = formatDateTime(new Date());
  const localFilePath = path.join(__dirname, `../../factures/facture_${currentDateTime}.txt`);
  const remoteDir = '\\\\Sagex3-bd-v12\\FAC-ZLG';
  const username = 'mahdi.samet';
  const password = 'Zen1234';

  try {
    console.log("Début du processus d'envoi de la facture à Sage...");

    // Valider les données de la facture
    console.log("Validation des données de la facture...");
    if (Array.isArray(factureData)) {
      factureData.forEach((facture, index) => {
        try {
          validateInvoiceData(facture); // Valide chaque facture
        } catch (validationError) {
          throw new Error(`Erreur de validation pour la facture à l'index ${index}: ${validationError.message}`);
        }
      });
    } else {
      try {
        validateInvoiceData(factureData); // Valide une seule facture
      } catch (validationError) {
        throw new Error(`Erreur de validation de la facture : ${validationError.message}`);
      }
    }

    console.log("Formatage des données de la facture...");
    const dataToWrite = Array.isArray(factureData)
      ? factureData.map(formatInvoice).join('\n')
      : formatInvoice(factureData);

    console.log("Écriture du fichier local...");
    await writeLocalFile(localFilePath, dataToWrite);
    console.log("Fichier local créé avec succès :", localFilePath);

    // Si nécessaire, connecter au partage réseau
    // await connectToNetworkShare(remoteDir, username, password);
    // console.log("Partage réseau monté avec succès.");

    console.log("Copie du fichier vers le partage réseau...");
    await copyFileToNetworkShare(localFilePath, remoteDir);
    console.log("Fichier copié avec succès vers le partage réseau.");

    console.log("Mise à jour du champ `to_sage`...");
    if (Array.isArray(factureData)) {
      await updateToSage(factureData);
    } else {
      await updateToSage([factureData]); // Passer une seule facture dans un tableau
    }

    console.log("Processus terminé avec succès.");
    result(null, "Fichier facture créé et copié avec succès.");
  } catch (error) {
    console.error("Erreur détectée : ", error);
    result({ message: error.message }, null);
  }
};


const updateToSage = async (factureData) => {
  for (const facture of factureData) {
      // Assurez-vous que l'ID de la facture est présent
      if (facture.id_facture) {
          const query = `UPDATE facture SET to_sage = TRUE WHERE id_facture = ?`;
          const values = [facture.id_facture];
          await sql.query(query, values); // Remplacez `db.execute` par votre méthode d'exécution de requêtes
      } else {
          console.error("ID de facture manquant :", facture);
      }
  }
};


Facture.findInvoiceByDate = (date, result) => {
  const query = `
    SELECT * FROM facture
    WHERE invoice_date > ?
    LIMIT 1; 
  `;

  sql.query(query, [date], (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(err, null);
      return;
    }

    // Renvoie la liste (même vide ou avec 1 résultat)
    result(null, res);  // Si aucune facture n'est trouvée, res sera un tableau vide
  });
};


Facture.findFactureByClientAndDate = (id_client, invoiced_date, page, pageSize, result) => {
  const offset = (page - 1) * pageSize;

  const query = `
    SELECT facture.*, fournisseur.nom_fournisseur, fournisseur.mat_fisc
    FROM facture
    JOIN fournisseur ON facture.id_client = fournisseur.id
    WHERE facture.id_client = ? 
    AND DATE_FORMAT(facture.invoice_date, '%Y-%m') = ?
    ORDER BY facture.id_facture DESC
    LIMIT ? OFFSET ?;
  `;

  const countQuery = `
    SELECT COUNT(*) as totalRows
    FROM facture
    JOIN fournisseur ON facture.id_client = fournisseur.id
    WHERE facture.id_client = ? 
    AND DATE_FORMAT(facture.invoice_date, '%Y-%m') = ?;
  `;

  sql.query(query, [id_client, invoiced_date, pageSize, offset], (err, data) => {
    if (err) {
      console.error("Erreur SQL :", err);
      result(err, null);
      return;
    }

    sql.query(countQuery, [id_client, invoiced_date], (err, countResult) => {
      if (err) {
        console.error("Erreur lors du comptage des lignes :", err);
        result(err, null);
        return;
      }

      const totalRows = countResult[0].totalRows;
      console.log("Total Rows :", totalRows);

      result(null, { data, totalRows });
    });
  });
};



function updateFactures() {
  // Calculer la date limite (48 heures avant maintenant)
  const limiteDate = new Date();
  limiteDate.setHours(limiteDate.getHours() - 48);
  
  // Définir la requête SQL pour mettre à jour les factures
  const query = `
    UPDATE facture
    SET status = true, validation = 'Auto'
    WHERE created_at <= ?
    AND status = FALSE
  `;
  
  // Exécuter la requête SQL
  sql.query(query, [limiteDate], (err, results) => {
    if (err) {
      console.error('Erreur lors de la mise à jour des factures :', err);
      return;
    }
    
    console.log('Les factures ont été mises à jour avec succès.');
    deletePDFFiles();

    // Fermer la connexion à la base de données
  });
}

cron.schedule('40 0 * * *', () => {
  console.log('Exécution du cron job pour mettre à jour les factures...');
  updateFactures();
});


  module.exports = Facture ; 