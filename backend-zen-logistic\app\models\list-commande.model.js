const sql = require("./db.js");
const axios = require("axios");
const notification = require("./notification.model.js");
const mailModel = require("../models/mail.model.js");


//constructeur  

const Commande = function(commande) {
    this.type_camion = commande.type_camion;
    this.type_chargement = commande.type_chargement;
    this.date_depart = commande.date_depart;
    this.date_arrivee= commande.date_arrivee;
    this.heure_chargement = commande.heure_chargement;
    this.type_conditionnement = commande.type_conditionnement;
    this.type_marchandise = commande.type_marchandise;
    this.poids = commande.poids;
    this.volume = commande.volume;
    this.quantite = commande.quantite;
    this.mention = commande.mention;
    this.fragile = commande.fragile;
    this.code = commande.code;
    this.enrg_commande= commande.enrg_commande ;
    this.ajoutee_par= commande.ajoutee_par ;
    this.reservee_par= commande.reservee_par ;
    this.statut= commande.statut;
    this.prix= commande.prix;
    this.id_camion = commande.id_camion ;
    this.id_conducteur = commande.id_conducteur ;
    this.labelle = commande.labelle ;
    this.note = commande.note 
    this.nom_conducteur = commande.nom_conducteur
    this.nom_utilisateur = commande.nom_utilisateur
  } ;



  // Fuction create commande 

  Commande.create = (newCommande, result) => {
    // Recherche de l'utilisateur correspondant à l'identifiant ajoutee_par
    sql.query("SELECT nom_utilisateur FROM utilisateurs WHERE id = ?", newCommande.ajoutee_par, (err, userRes) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      // Vérifiez si l'utilisateur existe
      if (userRes.length === 0) {
        console.log("Utilisateur non trouvé avec l'identifiant:", newCommande.ajoutee_par);
        result({ kind: "not_found" }, null);
        return;
      }
  
      // Mettre à jour newCommande.reservee_par avec l'identifiant de l'utilisateur récupéré
      const nom_utilisateur = userRes[0].nom_utilisateur;
      newCommande.nom_utilisateur = nom_utilisateur;
  
      // Insérer la nouvelle commande dans la base de données
      sql.query("INSERT INTO commande SET ?", newCommande, (err, res) => {
        if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
        }
  
        console.log("created commande: ", { id: res.insertId, ...newCommande });
  
        // Renvoyer la commande insérée dans le callback
        result(null, { id: res.insertId, ...newCommande });
      });
    });
  };
  
  Commande.delete = (id, result) => {
    // Supprimer les points de chargement liés à cette commande
    sql.query("DELETE FROM point_chargement WHERE idCommande = ?", id, (err, res) => {
        if (err) {
            console.log("Error deleting points de chargement with id_commande: ", err);
            result(err, null);
            return;
        }

        console.log("Deleted points de chargement with id_commande: ", id);
        // Ensuite, supprimer la commande
        sql.query("DELETE FROM commande WHERE id = ?", id, (err, res) => {
            if (err) {
                console.log("Error deleting commande with id: ", err);
                result(err, null);
                return;
            }
            console.log("Deleted commande with id: ", id);
            result(null, res);
        });
    });
};

  Commande.getAll = result => {
    sql.query("SELECT c.id,c.nom_utilisateur, c.ville_depart,c.nom_conducteur, c.ville_arrivee,c.type_camion, c.type_chargement, c.date_depart, c.date_arrivee, c.heure_chargement, c.heure_dechargement_de, c.heure_dechargement_a , c.type_conditionnement , c.type_marchandise , c.poids, c.volume , c.quantite , c.mention , c.fragile, c.code , c.enrg_commande, c.id_camion,c.id_conducteur,c.prix, c.statut, c.labelle , c.ajoutee_par,CONCAT(ue.prenom,' ',ue.nom) as expediteur,c.reservee_par, CONCAT(ut.prenom,' ',ut.nom) as transporteur FROM `commande` c INNER JOIN utilisateurs ue on ue.id=c.ajoutee_par left join utilisateurs ut on ut.id=c.reservee_par ORDER BY c.id DESC;", (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }
  
      console.log("Commande: ", res);
      result(null, res);
    });
  }

   Commande.findById = (commandeId, result) => {
    sql.query(`SELECT * FROM commande WHERE  id = ${commandeId}`, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      if (res.length) {
        console.log("found commande: ", res[0]);
        result(null, res[0]);
        return;
      }
  
      // not found commande with the id
      result({ kind: "not_found" }, null);
    });
  };
  
  Commande.updateEnrgCommande = (commandeId, result) => {
    sql.query(`UPDATE commande SET enrg_commande = 'non' WHERE id = ${commandeId}`, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      if (res.affectedRows == 0) {
        // Aucune commande trouvée avec l'ID
        result({ kind: "not_found" }, null);
        return;
      }
  
      console.log("Mise à jour de la commande : ", commandeId);
      result(null, res);
    });
  };

  Commande.updateVolume = (commandeId,newVolume, result) => {
    sql.query(`UPDATE commande SET volume = ${newVolume} WHERE id = ${commandeId}`, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      if (res.affectedRows == 0) {
        // Aucune commande trouvée avec l'ID
        result({ kind: "not_found" }, null);
        return;
      }
  
      console.log("Mise à jour de la commande : ", commandeId);
      result(null, res);
    });
  };
  
  Commande.findEnrg = (idUser, result) => {
    //console.log(`SELECT * FROM commande WHERE enrg_commande ='oui'  and ajoutee_par = ${idUser} `)
    sql.query(`SELECT id, labelle FROM commande WHERE enrg_commande ='Oui'  and ajoutee_par = ${idUser}`, (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(err, null); // Correction ici
            return;
        }

        if (res.length) {
            console.log("found commande: ");
            result(null, res);
            return;
        }

        // not found commande with the iduser
        result({ kind: "not_found" }, null);
    });
};







Commande.updateCommWithId = (id, commande, result) => {
  const now = new Date();
  const hour = now.getHours();

  // Obtenez la date de départ de la commande correspondant à l'ID spécifié
  sql.query(
      `SELECT DATE(date_depart) AS date_depart,nom_utilisateur FROM commande WHERE id = ?`,
      [id],
      (err, res) => {
          if (err) {
              console.log("error: ", err);
              result(err, null);
              return;
          }

          if (res.length) {
              const dateDepart = new Date(res[0].date_depart);
              const user = res[0].nom_utilisateur;
              console.log("Date de départ récupérée :", dateDepart.toISOString());
              console.log("Date now :", now.toISOString());
              if (isTomorrowOrLater(dateDepart) || isDayAfterTomorrowOrLater(dateDepart)) {
                  console.log("Date de départ autorisée pour la mise à jour");
                  // Mettez à jour la commande avec le statut spécifié
                  sql.query(
                      `UPDATE commande SET statut = ? WHERE id = ?`,
                      [commande.statut, id],
                      (err, res) => {
                          if (err) {
                              console.log("error: ", err);
                              result(err, null);
                              return;
                          }
                          console.log("Commande mise à jour avec succès");

                          result(null, { ...commande });

                          notification.createNotificationForAdmins(`Une commande est passée par ${user}`,"pages/listcommande", (err, notifications) => {
                            if (err) {
                                console.error("Error creating notifications:", err);
                            } else {
                                console.log("Notifications created for admin users:", notifications);
                            }
                        });
                        
                                               }
                  );
              } else {
                  // Si la date de départ ne correspond pas aux critères, lancez une erreur
                  console.log("Erreur : Date de départ non autorisée pour la mise à jour");
                  result({ message: "Date de départ non autorisée pour la mise à jour" }, null);
              }
          } else {
              // Aucune commande trouvée avec l'ID spécifié
              console.log("Erreur : Aucune commande trouvée avec l'ID spécifié");
              result({ kind: "not_found" }, null);
          }
      }
  );
};














// Fonction pour vérifier si la date est demain ou plus tard
function isTomorrowOrLater(date) {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1); // Demain
  tomorrow.setHours(0, 0, 0, 0); // Définit l'heure à 00:00:00
  return date >= tomorrow;
}

// Fonction pour vérifier si la date est après-demain ou plus tard
function isDayAfterTomorrowOrLater(date) {
  const dayAfterTomorrow = new Date();
  dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2); // Après-demain
  dayAfterTomorrow.setHours(0, 0, 0, 0); // Définit l'heure à 00:00:00
  return date >= dayAfterTomorrow;
}

Commande.getAllComm = (idUser, result) => {
  // Calculer la date d'il y a 30 jours
  let thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  thirtyDaysAgo = thirtyDaysAgo.toISOString().split('T')[0]; // Format AAAA-MM-JJ

  sql.query(`
    SELECT 
      c.id,
      c.note,
      c.nom_conducteur,
      c.nom_utilisateur,
      c.ville_depart,
      c.ville_arrivee,
      c.type_camion,
      c.type_chargement,
      c.date_depart,
      c.date_arrivee,
      c.heure_chargement,
      c.heure_dechargement_de,
      c.heure_dechargement_a,
      c.type_conditionnement,
      c.type_marchandise,
      c.poids,
      c.volume,
      c.quantite,
      c.mention,
      c.fragile,
      c.code,
      c.enrg_commande,
      c.id_camion,
      c.id_conducteur,
      c.prix,
      c.statut,
      c.labelle,
      c.ajoutee_par,
      c.reservee_par
    FROM 
      commande c 
    WHERE 
      c.ajoutee_par = "${idUser}"
      AND c.date_arrivee >= '${thirtyDaysAgo}' -- Date_arrivee supérieure ou égale à il y a 30 jours
    ORDER BY 
      c.id DESC;
  `, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.length) {
      console.log("found commande: ");
      result(null, res);
      return;
    }

    // not found commande with the iduser 
    result({ kind: "not_found" }, null);
  });
};


Commande.updateNote = (id , commande, result) => {
    
console.log(`UPDATE commande  SET note =? WHERE id = "${id}" `,[  commande.note ]);
  sql.query(
    `UPDATE commande  SET note ='?' WHERE id = "${id}" `,
    [  commande.note ],
   
    (err, res) => {
      console.log(sql)
     
      if (err) {

        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        // not found Commande with the cle 
        result({ kind: "not_found" }, null);
        return;
      }

      
      result(null, {  ...commande });
    } 
  
    
  );  
};

Commande.getAllCom = (idUser, result) => {
  // Calculer la date d'il y a 30 jours
  let thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 90);
  thirtyDaysAgo = thirtyDaysAgo.toISOString().split('T')[0]; // Format AAAA-MM-JJ

  sql.query(`
    SELECT 
      c.id, 
      c.ville_depart,
      c.nom_conducteur, 
      c.ville_arrivee, 
      c.nom_utilisateur, 
      c.type_camion, 
      c.type_chargement, 
      c.date_depart, 
      c.date_arrivee, 
      c.heure_chargement, 
      c.heure_dechargement_de, 
      c.heure_dechargement_a , 
      c.type_conditionnement , 
      c.type_marchandise , 
      c.poids, 
      c.volume , 
      c.quantite , 
      c.mention , 
      c.fragile, 
      c.code , 
      c.enrg_commande, 
      c.id_camion, 
      c.id_conducteur, 
      c.prix, 
      c.statut, 
      c.labelle , 
      c.ajoutee_par, 
      CONCAT(ut.prenom,' ',ut.nom) as transporteur, 
      c.reservee_par 
    FROM 
      commande c 
    LEFT JOIN 
      utilisateurs ut ON ut.id=c.reservee_par 
    WHERE 
      c.statut='Valide' 
      AND c.ajoutee_par= "${idUser}" 
      AND c.date_arrivee >= '${thirtyDaysAgo}' -- Date_arrivee >= 30 jours précédents
    ORDER BY 
      c.id DESC;
  `, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.length) {
      console.log("found commande: ");
      result(null, res);
      return;
    }

    // not found commande with the iduser 
    result({ kind: "not_found" }, null);
  });
};

Commande.getAllComTrpValide =(idUser , result )=> {
  sql.query(`SELECT c.id,c.nom_utilisateur, c.ville_depart,c.nom_conducteur, c.ville_arrivee,c.type_camion, c.type_chargement, c.date_depart, c.date_arrivee, c.heure_chargement, c.heure_dechargement_de, c.heure_dechargement_a , c.type_conditionnement , c.type_marchandise , c.poids, c.volume , c.quantite , c.mention , c.fragile, c.code , c.enrg_commande, c.id_camion,c.id_conducteur,c.prix, c.statut, c.labelle , c.ajoutee_par,CONCAT(ue.prenom,' ',ue.nom) as expediteur,c.reservee_par FROM commande c INNER JOIN utilisateurs ue on ue.id=c.ajoutee_par WHERE c.statut='valide' and c.reservee_par= "${idUser}" ORDER BY c.id DESC;`, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result( err) ,null;
      return;
    }

    if (res.length) {
      console.log("found commande: ");
      result(null, res);
      return;
    }

    // not found commande with the iduser 
    result({ kind: "not_found" }, null);
  });
};

Commande.getCom = (idUser, result) => {
  let now = new Date();
  let hour = now.getHours();
  let interval;
  if (hour < 12) {
    // Si l'heure actuelle est avant 12h, alors date_depart >= demain
    interval = 'INTERVAL 1 DAY';
  } else {
    // Sinon, date_depart >= après-demain
    interval = 'INTERVAL 2 DAY';
  }

  // Créer la requête SQL en utilisant les variables assignées
  let query = `
    SELECT 
      c.id, 
      c.type_chargement,
      c.date_depart, 
      c.date_arrivee,
      c.type_conditionnement, 
      c.type_marchandise, 
      c.mention, 
      c.fragile,
      c.code, 
      c.enrg_commande,
      c.statut, 
      c.labelle,
      c.ajoutee_par,
      c.reservee_par,
      c.nom_utilisateur
    FROM 
      commande c
    WHERE 
      c.statut = 'en attente de confirmation'
      AND c.date_depart >= 
        CASE 
          WHEN HOUR(NOW()) < 12 THEN DATE_ADD(CURDATE(), INTERVAL 1 DAY) -- Si avant 12h, récupérer les commandes pour demain
          ELSE DATE_ADD(CURDATE(), INTERVAL 2 DAY) -- Sinon, récupérer les commandes pour après-demain
        END
      AND c.ajoutee_par = ${idUser}  -- Ajout de la condition ajoutee_par = idUser
    ORDER BY 
      c.id DESC;
  `;
  
  // Exécuter la requête SQL
  sql.query(query, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.length) {
      console.log("found commande: ");
      result(null, res);
      return;
    }

    // not found commande with the iduser
    result({ kind: "not_found" }, null);
  });
};

  Commande.updateCommParTrp = (id , commande, result) => {
  sql.query(
    `UPDATE commande SET reservee_par=? , statut=? , id_camion=? , id_conducteur=? , nom_conducteur=? WHERE id = "${id}"` ,   
    [  commande.reservee_par , commande.statut , commande.id_camion , commande.id_conducteur, commande.nom_conducteur], 
    (err, res) => {
      console.log(sql)
     
      if (err) {

        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        // not found Commande with the cle 
        result({ kind: "not_found" }, null);
        return;
      }

      
      result(null, {  ...commande });
    } 
  
    
  );  
};

Commande.getCommRes =(idUser , result )=> {
  sql.query(`SELECT c.id,c.nom_utilisateur, c.ville_depart,c.nom_conducteur, c.ville_arrivee,c.type_camion, c.type_chargement, c.date_depart, c.date_arrivee, c.heure_chargement, c.heure_dechargement_de, c.heure_dechargement_a , c.type_conditionnement , c.type_marchandise , c.poids, c.volume , c.quantite , c.mention , c.fragile, c.code , c.enrg_commande, c.id_camion,c.id_conducteur,c.prix, c.statut, c.labelle , c.ajoutee_par,CONCAT(ue.prenom,' ',ue.nom) as expediteur,c.reservee_par FROM commande c INNER JOIN utilisateurs ue on ue.id=c.ajoutee_par WHERE c.reservee_par= "${idUser}" and  (c.statut='reservée' or c.statut='livree') ORDER BY c.id DESC;`, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result( err) ,null;
      return;
    }

    if (res.length) {
      console.log("found commande: ");
      result(null, res);
      return;
    }

    // not found commande with the iduser 
    result({ kind: "not_found" }, null);
  });
};

Commande.getCommResExp =(idUser , result )=> {
  sql.query(`SELECT c.id,c.nom_utilisateur, c.ville_depart,c.nom_conducteur, c.ville_arrivee,c.type_camion, c.type_chargement, c.date_depart, c.date_arrivee, c.heure_chargement, c.heure_dechargement_de, c.heure_dechargement_a , c.type_conditionnement , c.type_marchandise , c.poids, c.volume , c.quantite , c.mention , c.fragile, c.code , c.enrg_commande, c.id_camion,c.id_conducteur,c.prix, c.statut,c.nom_conducteur, c.labelle , c.ajoutee_par,c.reservee_par, CONCAT(ut.prenom,' ',ut.nom) as transporteur FROM commande c left join utilisateurs ut on ut.id=c.reservee_par WHERE c.ajoutee_par= "${idUser}" and c.statut='reservée'`, (err, res) => {
    if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
    }

    if (res.length) {
      console.log("found commande: ");
      result(null, res);
      return;
    }

    // not found commande with the iduser 
    result({ kind: "not_found" }, null);
  });
};
Commande.findCom =(conducteurId , result )=> {
  console.log(conducteurId)
  //console.log(`SELECT * FROM commande WHERE enrg_commande ='oui'  and ajoutee_par = ${idUser} `)
      sql.query(`SELECT * FROM commande WHERE statut='Reservée' and id_conducteur = ${conducteurId} `, (err, res) => {
        if (err) {
          console.log("error: ", err.kind);
          result( err) ,null;
          return;
        }
    
        if (res.length) {
          console.log("found commande: ");
          result(null, res);
          return;
        }
    
        // not found commande with the iduser 
        result({ kind: "not_found" }, null);
      });
    };

    
    Commande.findComARes = result => {
      sql.query("SELECT c.id,c.nom_utilisateur, c.ville_depart, c.nom_conducteur, c.ville_arrivee, c.type_camion, c.type_chargement, c.date_depart, c.date_arrivee, c.heure_chargement, c.heure_dechargement_de, c.heure_dechargement_a, c.type_conditionnement, c.type_marchandise, c.poids, c.volume, c.quantite, c.mention, c.fragile, c.code, c.enrg_commande, c.id_camion, c.id_conducteur, c.prix, c.statut, c.labelle, c.ajoutee_par, CONCAT(ue.prenom,' ',ue.nom) as expediteur, c.reservee_par FROM commande c INNER JOIN utilisateurs ue ON ue.id = c.ajoutee_par WHERE c.statut='valide'", (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(null, err);
            return;
        }
    
        console.log("Commande: ", res);
        
        // Transformez le résultat pour obtenir la structure souhaitée
        const transformedResult = res.map(row => {
          return {
            id: row.id,
            ville_depart: row.ville_depart,
            nom_conducteur: row.nom_conducteur,
            ville_arrivee: row.ville_arrivee,
            type_camion: row.type_camion,
            type_chargement: row.type_chargement,
            date_depart: row.date_depart,
            date_arrivee: row.date_arrivee,
            heure_chargement: row.heure_chargement,
            heure_dechargement_de: row.heure_dechargement_de,
            heure_dechargement_a: row.heure_dechargement_a,
            type_conditionnement: row.type_conditionnement,
            type_marchandise: row.type_marchandise,
            poids: row.poids,
            volume: row.volume,
            quantite: row.quantite,
            mention: row.mention,
            fragile: row.fragile,
            code: row.code,
            enrg_commande: row.enrg_commande,
            id_camion: row.id_camion,
            id_conducteur: row.id_conducteur,
            prix: row.prix,
            statut: row.statut,
            labelle: row.labelle,
            ajoutee_par: row.ajoutee_par,
            expediteur: row.expediteur,
            reservee_par: row.reservee_par,

            
            // ... autres propriétés ...
          };
        });
    
        result(null, transformedResult);
      });
    };
    
    
    Commande.getAllRes = (result) => {
      sql.query(`SELECT * FROM commande c WHERE c.statut='reservée'`, (err, res) => {
        if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
        }
    
        if (res.length) {
          console.log("found commande: ");
          result(null, res);
          return;
        }
    
        result({ kind: "not_found" }, null);
      });
    };
    
    Commande.infoAdmin = (result) => {
      sql.query(`SELECT email FROM utilisateurs WHERE type_utilisateur = 'Administrateur'`, (err, res) => {
        if (err) {
          return result(err, null);
        }

        if (res.length > 0) {
          const adminEmails = res.map(admin => admin.email);
          const emailData = {
            sender: { email: '<EMAIL>' },
            to: adminEmails, 
            subject: 'Nouvelle commande',
            htmlContent: `
              <p>Bonjour,</p>
              <p>Vous avez une nouvelle commande sur Zen Logistic. Veuillez consulter les commandes en suivant le lien ci-dessous :</p>
              <p><a href="http://zenlogistic.tn/#/pages/listcommande">Commande à Réservé</a></p>
              <p>Cordialement,</p>
              <p>L'équipe Zen Logistic</p>
              <p><b>Département Recherche & Dév</b></p>
            `
          };
          if (emailData.to.length === 0) {
            console.error("No recipients found");
            return result({ kind: "not_found", message: "No recipients found." }, null);
          }
    
          // Send the email
          mailModel.sendEmail(emailData)
            .then(emailResponse => {
              console.log("Email sent successfully. Response: ", emailResponse);
              return result(null, { message: "Email sent successfully." });
            })
            .catch(emailError => {
              console.error("Error while sending email: ", emailError);
              return result(emailError, null);
            });
    
        } else {
          // No administrators found
          console.warn("No administrators found.");
          return result({ kind: "not_found", message: "No administrators found." }, null);
        }
      });
    };
    
    
    



    Commande.getAllCommandeByChef = (idChef, result) => {
      // Rechercher tous les id_user associés à l'id_chef donné dans la table chef_user
      sql.query(`SELECT id_user FROM chef_user WHERE id_chef = ${idChef}`, (err, chefUserRes) => {
          if (err) {
              console.log("Error retrieving chef users:", err);
              result(err, null);
              return;
          }
  
          const userIds = chefUserRes.map(chefUser => chefUser.id_user).join(',') + ',' + idChef;
          console.log(";;;;;;;;;;;;;;;;;;;;;;;;", userIds);
  
          // Calculer la date d'il y a 30 jours
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          const formattedThirtyDaysAgo = thirtyDaysAgo.toISOString().split('T')[0]; // Format AAAA-MM-JJ
  
          // Récupérer toutes les commandes associées aux id_user trouvés
          sql.query(`
              SELECT 
                  c.id, c.note, c.nom_conducteur, c.ville_depart, c.ville_arrivee, c.type_camion, c.type_chargement,c.nom_utilisateur,
                  c.date_depart, c.date_arrivee, c.heure_chargement, c.heure_dechargement_de, c.heure_dechargement_a,
                  c.type_conditionnement, c.type_marchandise, c.poids, c.volume, c.quantite, c.mention, c.fragile,
                  c.code, c.enrg_commande, c.id_camion, c.id_conducteur, c.prix, c.statut, c.labelle, c.ajoutee_par,
                  c.reservee_par, CONCAT(ut.prenom, ' ', ut.nom) as transporteur
              FROM commande c
              LEFT JOIN utilisateurs ut ON ut.id = c.reservee_par
              WHERE c.ajoutee_par IN (${userIds})
              AND c.statut != 'Invalide'
              AND c.date_arrivee >= '${formattedThirtyDaysAgo}'
              ORDER BY c.id DESC;
          `, (err, res) => {
              if (err) {
                  console.log("Error retrieving commands for chef users:", err);
                  result(err, null);
                  return;
              }
  
              if (res.length) {
                  console.log("Found commands:", res);
                  result(null, res);
              } else {
                  // Aucune commande trouvée pour les utilisateurs associés au chef
                  result({ kind: "not_found" }, null);
              }
          });
      });
  };

  Commande.getCommandeToValidateByChef = (idChef, result) => {
    // Rechercher tous les id_user associés à l'id_chef donné dans la table chef_user
    sql.query(`SELECT id_user FROM chef_user WHERE id_chef = ${idChef}`, (err, chefUserRes) => {
        if (err) {
            console.log("Error retrieving chef users:", err);
            result(err, null);
            return;
        }

        const userIds = chefUserRes.map(chefUser => chefUser.id_user).join(',')+ ',' + idChef;

        // Créer la requête SQL en utilisant les variables assignées
        let query = `
            SELECT 
                c.id, 
                c.type_chargement,
                c.date_depart, 
                c.date_arrivee,
                c.type_conditionnement, 
                c.type_marchandise, 
                c.mention, 
                c.fragile,
                c.code, 
                c.enrg_commande,
                c.statut, 
                c.labelle,
                c.ajoutee_par,
                c.reservee_par,
                c.nom_utilisateur

            FROM 
                commande c
            WHERE 
                c.statut = 'en attente de confirmation'
                AND c.ajoutee_par IN (${userIds})
                AND c.date_depart >= 
                    CASE 
                        WHEN HOUR(NOW()) < 12 THEN DATE_ADD(CURDATE(), INTERVAL 1 DAY) -- Si avant 12h, récupérer les commandes pour demain
                        ELSE DATE_ADD(CURDATE(), INTERVAL 2 DAY) -- Sinon, récupérer les commandes pour après-demain
                    END
            ORDER BY 
                c.id DESC;
        `;

        // Afficher la requête
        console.log("Requête SQL : ", query);

        // Exécuter la requête SQL
        sql.query(query, (err, res) => {
            if (err) {
                console.log("error: ", err);
                result(err, null);
                return;
            }

            if (res.length) {
                console.log("found commande: ");
                result(null, res);
                return;
            }

            // not found commande with the iduser
            result({ kind: "not_found" }, null);
        });
    });
};

Commande.findEnrgBychef = (idUser, result) => {
  // Sélectionner les identifiants des utilisateurs associés au chef
  sql.query(`SELECT id_user FROM chef_user WHERE id_chef = ${idUser}`, (err, chefUserRes) => {
      if (err) {
          console.log("Error retrieving chef users:", err);
          result(err, null);
          return;
      }

      // Construire une liste d'identifiants d'utilisateurs séparés par des virgules, y compris l'identifiant du chef
      const userIds = chefUserRes.map(chefUser => chefUser.id_user).join(',') + ',' + idUser;

      // Sélectionner les commandes avec enrg_commande = 'Oui' pour les utilisateurs spécifiés dans userIds
      sql.query(`
          SELECT c.id, c.labelle,nom_utilisateur
          FROM commande c
          WHERE c.enrg_commande = 'Oui'
          AND c.ajoutee_par IN (${userIds});
      `, (err, res) => {
          if (err) {
              console.log("Error retrieving enrg commandes for chef:", err);
              result(err, null);
              return;
          }

          if (res.length) {
              console.log("Found enrg commandes:", res);
              result(null, res);
          } else {
              // Aucune commande enrg trouvée pour le chef et ses utilisateurs associés
              result({ kind: "not_found" }, null);
          }
      });
  });
};








Commande.getAllComValidByChef = (idUser, result) => {
  sql.query(`SELECT id_user FROM chef_user WHERE id_chef = ${idUser}`, (err, chefUserRes) => {
    if (err) {
      console.log("Error retrieving chef users:", err);
      result(err, null);
      return;
    }

    // Construire une liste d'identifiants d'utilisateurs, y compris l'identifiant du chef
    const userIds = chefUserRes.map(chefUser => chefUser.id_user).join(',') + ',' + idUser;

    // Calculer la date d'il y a 30 jours
    let thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    thirtyDaysAgo = thirtyDaysAgo.toISOString().split('T')[0]; // Format AAAA-MM-JJ

    // Sélectionner toutes les commandes validées ajoutées par les utilisateurs associés au chef
    sql.query(`
      SELECT 
        c.id, 
        c.ville_depart,
        c.nom_conducteur, 
        c.ville_arrivee,
        c.nom_utilisateur, 
        c.type_camion, 
        c.type_chargement, 
        c.date_depart, 
        c.date_arrivee, 
        c.type_conditionnement , 
        c.type_marchandise , 
        c.mention , 
        c.fragile, 
        c.code , 
        c.enrg_commande, 
        c.prix, 
        c.statut, 
        c.labelle , 
        c.ajoutee_par,
        c.reservee_par
      FROM 
        commande c
      WHERE 
        c.statut = 'Valide' 
        AND c.ajoutee_par IN (${userIds})
        AND c.date_arrivee >= '${thirtyDaysAgo}' AND CURDATE() + INTERVAL 1 DAY  -- Date_arrivee entre aujourd'hui et 30 jours précédents (inclus)
      ORDER BY 
        c.id DESC;
    `, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      if (res.length) {
        console.log("found commande: ");
        result(null, res);
        return;
      }

      // Aucune commande trouvée pour l'utilisateur donné
      result({ kind: "not_found" }, null);
    });
  });
};







    

  module.exports = Commande ; 




