/**
 * @swagger
 * tags:
 *   name: Destinations
 *   description: Gestion des destinations et entrepôts logistiques
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Destination:
 *       type: object
 *       required:
 *         - nom_locale
 *         - adresse
 *         - phone
 *         - id_region
 *         - id_fournisseur
 *       properties:
 *         id:
 *           type: integer
 *           description: ID auto-généré de la destination
 *         nom_locale:
 *           type: string
 *           description: Nom de la destination
 *         adresse:
 *           type: string
 *           description: Adresse physique
 *         phone:
 *           type: string
 *           description: Numéro de téléphone
 *         email:
 *           type: string
 *           format: email
 *           description: Adresse email (optionnelle)
 *         id_region:
 *           type: integer
 *           description: ID de la région associée
 *         id_fournisseur:
 *           type: integer
 *           description: ID du fournisseur/client
 *         id_types:
 *           type: array
 *           items:
 *             type: integer
 *           description: Liste des IDs des types de commande associés
 *         code_erp:
 *           type: string
 *           description: Code ERP de la destination (optionnel)
 * 
 *     Warehouse:
 *       type: object
 *       required:
 *         - name
 *         - code
 *       properties:
 *         id:
 *           type: integer
 *           description: ID auto-généré
 *         name:
 *           type: string
 *           description: Nom de l'entrepôt
 *         code:
 *           type: string
 *           description: Code unique
 *         depot:
 *           type: boolean
 *           description: Si l'entrepôt est actif
 *         id_brand:
 *           type: integer
 *           description: ID de la marque associée
 *         type:
 *           type: string
 *           description: Type d'entrepôt
 * 
 *     UserDestinationAssignment:
 *       type: object
 *       required:
 *         - id_user
 *         - id_destination
 *       properties:
 *         id_user:
 *           type: integer
 *           description: ID de l'utilisateur
 *         id_destination:
 *           type: integer
 *           description: ID de la destination
 */

/**
 * @swagger
 * /api/destination:
 *   post:
 *     summary: Créer une nouvelle destination
 *     tags: [Destinations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Destination'
 *     responses:
 *       200:
 *         description: Destination créée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Destination'
 *       400:
 *         description: Données invalides
 *       500:
 *         description: Erreur serveur
 *
 *   get:
 *     summary: Lister toutes les destinations
 *     tags: [Destinations]
 *     responses:
 *       200:
 *         description: Liste des destinations
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Destination'
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/destination/{id_ville}/{id_fournisseur}/{id_type}:
 *   get:
 *     summary: Trouver destinations par ville, fournisseur et type
 *     tags: [Destinations]
 *     parameters:
 *       - in: path
 *         name: id_ville
 *         required: true
 *         schema:
 *           type: integer
 *       - in: path
 *         name: id_fournisseur
 *         required: true
 *         schema:
 *           type: integer
 *       - in: path
 *         name: id_type
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Destinations trouvées
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Destination'
 *       404:
 *         description: Aucune destination trouvée
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/findAllDestinationHaveType:
 *   get:
 *     summary: Lister destinations avec type de commande
 *     tags: [Destinations]
 *     responses:
 *       200:
 *         description: Destinations avec types
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Destination'
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/destination/{idClient}:
 *   get:
 *     summary: Trouver destinations par client
 *     tags: [Destinations]
 *     parameters:
 *       - in: path
 *         name: idClient
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Destinations du client
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 $ref: '#/components/schemas/Destination'
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/updateDestination/{id}:
 *   put:
 *     summary: Mettre à jour une destination
 *     tags: [Destinations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               nom_locale:
 *                 type: string
 *               adresse:
 *                 type: string
 *               phone:
 *                 type: string
 *               email:
 *                 type: string
 *               id_region:
 *                 type: integer
 *               code_erp:
 *                 type: string
 *     responses:
 *       200:
 *         description: Destination mise à jour
 *       400:
 *         description: Données invalides
 *       404:
 *         description: Destination non trouvée
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/delete/{id}:
 *   delete:
 *     summary: Supprimer une destination
 *     tags: [Destinations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Destination supprimée
 *       400:
 *         description: Destination utilisée dans des commandes
 *       404:
 *         description: Destination non trouvée
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/updateDestinationType/{id}:
 *   put:
 *     summary: Mettre à jour les types de commande d'une destination
 *     tags: [Destinations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               listeIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *     responses:
 *       200:
 *         description: Types mis à jour
 *       400:
 *         description: Aucune modification détectée
 *       404:
 *         description: Destination non trouvée
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/findDestinationById/{id}:
 *   get:
 *     summary: Trouver destination par ID
 *     tags: [Destinations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Destination trouvée
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Destination'
 *       404:
 *         description: Destination non trouvée
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/findDestinationByCompany/{id}:
 *   get:
 *     summary: Trouver destinations par entreprise
 *     tags: [Destinations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Destinations trouvées
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Destination'
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/assignDestinationToUser/{id}:
 *   post:
 *     summary: Assigner des destinations à un utilisateur
 *     tags: [Destinations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: integer
 *     responses:
 *       200:
 *         description: Destinations assignées
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/findDestinationByUser/{id}:
 *   get:
 *     summary: Trouver destinations par utilisateur
 *     tags: [Destinations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Destinations trouvées
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id_user:
 *                     type: integer
 *                   id_destination:
 *                     type: integer
 *                   nom_locale:
 *                     type: string
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/findAllWarhouses:
 *   get:
 *     summary: Lister tous les entrepôts actifs
 *     tags: [Destinations]
 *     responses:
 *       200:
 *         description: Liste des entrepôts
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Warehouse'
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/findWarhousesByBrand/{id_brand}/{type}:
 *   get:
 *     summary: Trouver entrepôts par marque et type
 *     tags: [Destinations]
 *     parameters:
 *       - in: path
 *         name: id_brand
 *         required: true
 *         schema:
 *           type: integer
 *       - in: path
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Entrepôts trouvés
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Warehouse'
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/disabledDepot/{id}:
 *   put:
 *     summary: Désactiver un entrepôt
 *     tags: [Destinations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Entrepôt désactivé
 *       404:
 *         description: Entrepôt non trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/deleteDestinationByUser/{id}:
 *   get:
 *     summary: Supprimer destination assignée à un utilisateur
 *     tags: [Destinations]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Destination supprimée
 *       404:
 *         description: Destination non trouvée
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/addWarehouse:
 *   post:
 *     summary: Ajouter un nouvel entrepôt
 *     tags: [Destinations]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Warehouse'
 *     responses:
 *       200:
 *         description: Entrepôt créé
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Warehouse'
 *       400:
 *         description: Données invalides
 *       500:
 *         description: Erreur serveur
 */