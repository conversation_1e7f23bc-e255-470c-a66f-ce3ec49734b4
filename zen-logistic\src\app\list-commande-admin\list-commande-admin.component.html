<section id="filter">
  <div class="row text-left">
    <div class="col-sm-12">
      <div class="card">
        <div class="card-header">
          <h4 class="card-title"> Liste des commandes (int Admin ) </h4>
        </div>
        <div class="card-content">
          <div class="card-body">
            <div class="table-responsive">
              <ng2-smart-table [settings]="settings" [source]="source" (custom)="confirmCommande($event,content)"
                >
              </ng2-smart-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>


<ng-template #content let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      Plus de détail :
      
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="modal.dismiss('Cross click')">

      <span aria-hidden="true">
        ×
      </span>
    </button>
  </div>
  <div *ngIf="detail !=0" class="modal-body">
    <hr>
    <div class="row">
      <div class="col-12 col-md-2 ">
        <ul class="no-list-style">

          <!--li class="mb-2">
            <span class="text-bold-500 primary"><a><i class="icon-present font-small-3"></i> Id:</a></span>
            <span class="d-block overflow-hidden"><h6>//{{detail.id}}</h6></span>
          </li-->
          <!-- <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-map-pin"></i> Ville arrivée
                :</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.ville_arrivee}}</h6>
            </span>
          </li> -->
          <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-calendar"></i> Date
                arrivée:</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.date_arrivee}}</h6>
            </span>
          </li>

          <!-- <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-info"></i> Type de chargement
                :</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.type_chargement}}</h6>
            </span>
          </li> -->

        </ul>
      </div>

      <div class="col-12 col-md-2 ">
        <ul class="no-list-style">
          <!-- <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-map-pin"></i> Ville
                départ:</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.ville_depart}}</h6>
            </span>
          </li> -->
          <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-calendar"></i> Date départ
                :</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.date_depart}}</h6>
            </span>
          </li>
          <!-- <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-info"></i> Prix:</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.prix}}</h6>
            </span>
          </li> -->



        </ul>
      </div>
      <div class="col-12 col-md-2 ">
        <ul class="no-list-style">
          <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-circle"></i> Poids :</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.poids}} Kg</h6>
            </span>
          </li>
          <!-- <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-award"></i> Mention :</a></span>
            <a class="d-block overflow-hidden">
              <h6>{{detail.mention}}</h6>
            </a>
          </li> -->
          <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-info"></i>Type marchandise
                :</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.type_marchandise}}</h6>
            </span>
          </li>
        </ul>
      </div>
      <div class="col-12 col-md-2 ">
        <ul class="no-list-style">
          <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-package"></i>
                Quantité:</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.quantite}}</h6>
            </span>
          </li>
          <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-alert-octagon"></i> fragile
                :</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.fragile}}</h6>
            </span>
          </li>
          <!-- <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-info"></i>Type camion
                :</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.type_camion}}</h6>
            </span>
          </li> -->


        </ul>
      </div>

      <div class="col-12 col-md-2 ">
        <ul class="no-list-style">
          <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-info"></i> Type
                conditionnement:</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.type_conditionnement}}</h6>
            </span>
          </li>

          <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-package"></i> Volume:</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.volume}} M³</h6>
            </span>
          </li>
          <!-- <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-toggle-right"></i>
                Statut:</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.statut}}</h6>
            </span>
          </li> -->
        </ul>
      </div>
      <!-- <div class="col-12 col-md-2 ">
        <ul class="no-list-style">
          <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-watch"></i> Heure
                chargement:</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.heure_chargement}}</h6>
            </span>
          </li>

          <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-watch"></i> h.début
                déchargement:</a></span>
            <a class="d-block overflow-hidden">
              <h6>{{detail.heure_dechargement_de}}</h6>
            </a>
          </li>
          <li class="mb-2">
            <span class="text-bold-500 primary" style="font-size: 77%;"><a><i class="ft-watch"></i> h.fin
                déchargement:</a></span>
            <span class="d-block overflow-hidden">
              <h6>{{detail.heure_dechargement_a}}</h6>
            </span>
          </li>

        </ul>
      </div> -->

    </div>



    
    <div style="max-height: 400px; overflow-y: auto;" *ngIf="ligneTable.length>0">
      <table class="table" >
        <thead>
          <tr>
            <th>Départ</th>
            <th>Arrivé</th>

            <th>Address Départ</th>
            <th>Address Arrivé</th>

            <th>Type Ligne</th>
            <th>Kilometrage</th>
            <th>Volume</th>
            <th>Telephone</th>

          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of ligneTable ; let i = index">
           
            <td>{{ item.from_destination.nom_locale }}</td>
            <td>{{ item.to_destination.nom_locale }}</td>
            <td>{{item.from_destination.adresse}}</td>
            <td>{{item.to_destination.adresse}}</td>
            <td>{{ item.type_ligne }}</td>
            <td>{{ item.kilometrage }}</td>
            <td>{{ item.volume }}</td>
            <td>{{ item.telephone }}</td>

          </tr>
        </tbody>
      </table>
    </div>
    



  </div>
</ng-template>