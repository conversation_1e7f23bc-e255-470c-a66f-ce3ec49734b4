/**
 * @swagger
 * tags:
 *   name: Roles
 *   description: Opérations liées à la gestion des rôles
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Role:
 *       type: object
 *       required:
 *         - nom
 *         - value
 *       properties:
 *         id:
 *           type: integer
 *           description: Identifiant unique du rôle
 *         nom:
 *           type: string
 *           description: Nom du rôle
 *         value:
 *           type: string
 *           description: Valeur associée au rôle
 */

/**
 * @swagger
 * /api/roles:
 *   get:
 *     tags: [Roles]
 *     summary: Récupérer tous les rôles
 *     responses:
 *       200:
 *         description: Liste de tous les rôles récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur
 */
