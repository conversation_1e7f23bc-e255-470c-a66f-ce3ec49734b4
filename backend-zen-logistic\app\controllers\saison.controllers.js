const Saison = require ("../models/saison.model.js") ; 


exports.getAll = (req, res) => {
    Saison.getAll((err, data) => {
      if (err)
        res.status(500).send({
          message:
            err.message || "Some error occurred while retrieving <PERSON>."
        });
      else res.send(data);
    });
   
  };

  exports.updateQte = (req, res) => {
    const { id, qte } = req.params;

    Saison.updateQte(id, qte, (err, data) => {
        if (err) {
            if (err.kind === "not_found") {
                return res.status(404).send({
                    message: `<PERSON><PERSON> with id ${id} not found.`
                });
            } else {
                return res.status(500).send({
                    message: "Error updating <PERSON><PERSON> with id " + id
                });
            }
        } else {
            res.send(data);
        }
    });
};