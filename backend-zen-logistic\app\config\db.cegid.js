require('dotenv').config();

module.exports = {
    user: process.env.USER_DB_CEGID, // Respecte la casse (minuscules)
    password: process.env.PASSWORD_CEGID,
    server: process.env.HOST_CEGID,
    database: process.env.DB_CEGID,
    port: parseInt(process.env.PORT_DB_CEGID, 10) || 1433, // S'assurer que c'est un nombre
    options: {
        encrypt: false, // Désactiver le chiffrement si non requis
        enableArithAbort: true, // Pour éviter certaines erreurs
        trustServerCertificate: true, // Ignorer les problèmes de certificat TLS
    },
};
