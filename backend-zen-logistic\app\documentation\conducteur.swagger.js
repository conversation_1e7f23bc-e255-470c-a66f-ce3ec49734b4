/**
 * @swagger
 * components:
 *   schemas:
 *     Conducteur:
 *       type: object
 *       required:
 *         - nom
 *         - prenom
 *         - nom_utilisateur
 *         - mot_de_passe
 *         - cin
 *         - mobile
 *         - ajoutee_par
 *       properties:
 *         id:
 *           type: integer
 *           description: ID du conducteur
 *           example: 1
 *         nom:
 *           type: string
 *           description: Nom du conducteur
 *           example: Dupont
 *         prenom:
 *           type: string
 *           description: Prénom du conducteur
 *           example: Jean
 *         nom_utilisateur:
 *           type: string
 *           description: Nom d'utilisateur pour la connexion
 *           example: jdupont
 *         mot_de_passe:
 *           type: string
 *           description: Mot de passe
 *           example: motdepasse123
 *         mobile:
 *           type: string
 *           description: Numéro mobile
 *           example: "+33612345678"
 *         cin:
 *           type: string
 *           description: Numéro CIN
 *           example: AB123456
 *         image_cin:
 *           type: string
 *           description: Image du CIN (URL ou base64)
 *           example: "url_ou_base64_de_limage"
 *         ajoutee_par:
 *           type: integer
 *           description: ID de l'utilisateur qui a ajouté le conducteur
 *           example: 5
 *         bloqued:
 *           type: boolean
 *           description: Indique si le conducteur est bloqué
 *           example: false
 *         transporteur:
 *           type: string
 *           description: Nom complet du transporteur
 *           example: Paul Martin
 */

/**
 * @swagger
 * /api/conducteur:
 *   post:
 *     summary: Créer un nouveau conducteur
 *     tags: [Conducteur]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Conducteur'
 *     responses:
 *       200:
 *         description: Conducteur créé avec succès
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Conducteur'
 *       400:
 *         description: Requête invalide
 */

/**
 * @swagger
 * /api/conducteur:
 *   get:
 *     summary: Récupérer tous les conducteurs non bloqués
 *     tags: [Conducteur]
 *     responses:
 *       200:
 *         description: Liste des conducteurs
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Conducteur'
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/conducteur/{id}:
 *   get:
 *     summary: Trouver un conducteur par ID
 *     tags: [Conducteur]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: ID du conducteur
 *     responses:
 *       200:
 *         description: Conducteur trouvé
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Conducteur'
 *       404:
 *         description: Conducteur non trouvé
 */

/**
 * @swagger
 * /api/conducteur/conducteurBloqued/{id}:
 *   put:
 *     summary: Bloquer un conducteur par ID
 *     tags: [Conducteur]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID du conducteur à bloquer
 *     responses:
 *       200:
 *         description: Conducteur bloqué avec succès
 *       404:
 *         description: Conducteur non trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/conducteur/login:
 *   post:
 *     summary: Authentification conducteur
 *     tags: [Conducteur]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - nom_utilisateur
 *               - mot_de_passe
 *             properties:
 *               nom_utilisateur:
 *                 type: string
 *                 example: jdupont
 *               mot_de_passe:
 *                 type: string
 *                 example: motdepasse123
 *     responses:
 *       200:
 *         description: Conducteur authentifié
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Conducteur'
 *       404:
 *         description: Conducteur non trouvé
 */

