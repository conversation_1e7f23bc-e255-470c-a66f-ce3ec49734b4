import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class SupplierService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Add new supplier
   * @param supplierData - Supplier data
   * @returns Observable with creation result
   * @originalName addFournisseur
   */
  addSupplier(supplierData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'fournisseur', supplierData, httpOptions);
  }

  /**
   * Get all suppliers
   * @returns Observable with suppliers list
   * @originalName getAllFournisseur
   */
  getAllSuppliers(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'fournisseur', httpOptions);
  }

  /**
   * Find supplier by matricule
   * @param matricule - Supplier matricule
   * @returns Observable with supplier data
   * @originalName findFournisseurByMatricule
   */
  findSupplierByMatricule(matricule: string): Observable<any> {
    const encodedMatricule = encodeURIComponent(matricule);
    const url = `${this.apiURL}fournisseur/${encodedMatricule}`;
    return this.http.get(url, httpOptions);
  }

  /**
   * Find supplier by type
   * @param type - Supplier type
   * @returns Observable with suppliers list
   * @originalName findFournisseurByType
   */
  findSupplierByType(type: any): Observable<any> {
    const url = `${this.apiURL}getFou/${type}`;
    return this.http.get(url, httpOptions);
  }

  /**
   * Update supplier
   * @param id - Supplier ID
   * @param supplierData - Updated supplier data
   * @returns Observable with update result
   * @originalName updateFournisseur
   */
  updateSupplier(id: number, supplierData: any): Observable<any> {
    const url = `${this.apiURL}fournisseur/${id}`;
    return this.http.put(url, supplierData, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Find suppliers with storage
   * @returns Observable with suppliers list
   * @originalName findFournisseurWhithEntreposage
   */
  findSuppliersWithStorage(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'findFournisseurWhithEntreposage', httpOptions);
  }

  /**
   * Find suppliers with flux
   * @returns Observable with suppliers list
   * @originalName findFournisseurWhithFLux
   */
  findSuppliersWithFlux(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'findFournisseurWhithFLux', httpOptions);
  }

  /**
   * Find supplier by ID
   * @param id - Supplier ID
   * @returns Observable with supplier data
   * @originalName findFournisseurById
   */
  findSupplierById(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `fournisseurById/${id}`, httpOptions);
  }

  /**
   * Find client from Sage by matricule
   * @param matricule - Client matricule
   * @returns Observable with client data
   * @originalName findClientFromSageByMat
   */
  findClientFromSageByMatricule(matricule: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `findClientFromSageByMat/${matricule}`, httpOptions);
  }

  /**
   * Find clients to invoice for transport
   * @returns Observable with clients list
   * @originalName findClientToInvoiceTransport
   */
  findClientsToInvoiceTransport(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'findClientToInvoice', httpOptions);
  }

  /**
   * Find invoiced clients
   * @returns Observable with clients list
   * @originalName findClientInvoiced
   */
  findInvoicedClients(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'findClientInvoiced', httpOptions);
  }

  /**
   * Handle HTTP errors
   * @param error - Error object
   * @returns Observable error
   */
  private handleError(error: any): Observable<never> {
    console.error('An error occurred:', error);
    return throwError(() => new Error(error.message || 'Server error'));
  }
}
