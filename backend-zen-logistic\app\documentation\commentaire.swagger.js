/**
 * @swagger
 * tags:
 *   name: Commentaire
 *   description: Gestion des commentaires sur les lignes
 */

/**
 * @swagger
 * /api/commentaire:
 *   post:
 *     summary: Crée un nouveau commentaire
 *     tags: [Commentaire]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id_ligne
 *               - value
 *               - id_author
 *             properties:
 *               id_ligne:
 *                 type: integer
 *                 example: 101
 *               value:
 *                 type: string
 *                 example: "Retard de chargement"
 *               id_author:
 *                 type: integer
 *                 example: 3
 *               nom_author:
 *                 type: string
 *                 example: "Ali"
 *     responses:
 *       200:
 *         description: Commentaire créé avec succès
 *       400:
 *         description: Données manquantes
 *       500:
 *         description: Erreur interne
 */

/**
 * @swagger
 * /api/commentaire/{id}:
 *   get:
 *     summary: Obtenir les commentaires d'une ligne spécifique
 *     tags: [Commentaire]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID de la ligne
 *     responses:
 *       200:
 *         description: Liste des commentaires trouvés
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commentaire'
 *       404:
 *         description: Aucun commentaire trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/findAllComments:
 *   get:
 *     summary: Liste paginée de tous les commentaires
 *     tags: [Commentaire]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: Commentaires récupérés avec pagination
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 totalItems:
 *                   type: integer
 *                 commentaire:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       ajoutee_par:
 *                         type: string
 *                       commentaire:
 *                         type: string
 *                       nom_arrivee:
 *                         type: string
 *                       nom_depart:
 *                         type: string
 *                       type_ligne:
 *                         type: string
 *                       volume:
 *                         type: string
 *                       estimation:
 *                         type: string
 *       500:
 *         description: Erreur serveur
 */
