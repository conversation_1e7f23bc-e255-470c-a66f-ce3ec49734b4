// dbModel.js
const sql = require("./db.js");
const nodemailer = require('nodemailer');
const https = require('https');
const SmsModel = require ("../models/sms_history.model.js") ; 

const getDetailsByIds = (ids) => {
  return new Promise((resolve, reject) => {
      if (!Array.isArray(ids) || ids.length === 0) {
          return reject(new Error("Invalid IDs provided."));
      }

      const idsList = ids.join(","); // Convertir les identifiants en chaîne pour la clause `IN`

      const query = `
          SELECT 
  pc.id,
  pc.date_voyage,
  pc.H_depart,
  pc.H_arrivee,
  pc.tolerance,
  pc.quantite,
  pc.type_ligne,
  d1.nom_locale AS from_nom,
  d1.email AS from_email, 
  d2.nom_locale AS to_nom,
  d2.email AS to_email,
  CONCAT(conducteur.nom, ' ', conducteur.prenom) AS conducteur_nom_complet
FROM 
  point_chargement pc
LEFT JOIN 
  destination d1 ON pc.from_destination = d1.id
LEFT JOIN 
  destination d2 ON pc.to_destination = d2.id
LEFT JOIN 
  conducteur ON pc.id_conducteur = conducteur.id
WHERE 
  pc.id IN (${idsList})

      `;


      sql.query(query, (err, results) => {
          if (err) {
              console.error("SQL Query Error:", err.message);
              return reject(err);
          }
          if (results.length === 0) {
              console.warn("No results found for IDs:", idsList);
              return resolve([]); // Return empty array if no results found
          }

          resolve(results); // Return results
      });
  });
};




  async function getReservationData() {
    const query = `
        SELECT pc.*, c.nom, c.prenom, c.mobile, d.* 
        FROM point_chargement pc
        JOIN conducteur c ON pc.id_conducteur = c.id
        JOIN destination d ON pc.to_destination = d.id
        WHERE pc.date_voyage = CURDATE() + INTERVAL 1 DAY
        AND pc.id_camion != 14
        AND pc.id_conducteur != 61
    `;
    
    return new Promise((resolve, reject) => {
        sql.query(query, (err, results) => {
            if (err) {
                console.error("Erreur SQL : ", err);
                return reject(err);
            }
            resolve(results);
        });
    });
}


const getDetailsExcludingReservationDate = async (ids) => {
  const idsList = ids.join(","); // Convertir les identifiants en chaîne pour la clause `IN`

  const query = `
    SELECT 
      pc.id,
      pc.date_voyage,
      pc.type_ligne,
      pc.H_arrivee,
      pc.tolerance,
      pc.quantite,
      d1.nom_locale AS from_nom,
      d1.email AS from_email, 
      d2.nom_locale AS to_nom,
      d2.phone AS phone,
      d2.email AS to_email,
      pc.date_reservation,          
      c.nom AS conducteur_nom, 
      c.prenom AS conducteur_prenom,     
      c.mobile     
    FROM 
      point_chargement pc
    LEFT JOIN 
      destination d1 ON pc.from_destination = d1.id
    LEFT JOIN 
      destination d2 ON pc.to_destination = d2.id
    LEFT JOIN 
      conducteur c ON pc.id_conducteur = c.id  
    WHERE 
      pc.id IN (${idsList}) 
  `;

  try {
    const results = await new Promise((resolve, reject) => {
      sql.query(query, (err, results) => {
        if (err) {
          console.error("Erreur SQL : ", err);
          return reject(err);
        }
        resolve(results);
      });
    });
    if (results.length === 0) {
      throw new Error("Aucun résultat trouvé pour les identifiants fournis.");
    }
    console.log("Résultats de la requête SQL :", results);
    return results; // Retourner les résultats obtenus
  } catch (error) {
    console.error("Erreur lors de l'exécution de la requête:", error.message);
    throw error; // Propager l'erreur pour qu'elle soit gérée ailleurs
  }
};

const sendEmail = async (emailData) => {
  try {
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: process.env.SMTP_SECURE,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
      tls: {
        rejectUnauthorized: false, 
      },
    });
    const mailOptions = {
      from: `${process.env.NAME} <${process.env.EMAIL}>`,
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.htmlContent,
    };
console.log(emailData)
    if (emailData.attachments && emailData.attachments.length > 0) {
      mailOptions.attachments = emailData.attachments.map(attachment => ({
        filename: attachment.filename,    
        path: attachment.path, 
      }));
    }

    let info = await transporter.sendMail(mailOptions);

    console.log('Email sent: %s', info.messageId);
    return { message: "Email sent successfully", messageId: info.messageId };

  } catch (error) {
    console.error('Error sending email: ', error);
    throw new Error(error.message || "Email sending failed");
  }
};

async function sendingSMS(phone, message) {
  console.log(phone,message)
  if (!phone || !message) {
    throw new Error("Veuillez fournir un numéro de téléphone et un message");
  }

  const smsText = encodeURIComponent(message);
  const phoneNumber = encodeURIComponent(phone);

  const smsUrl = `https://41.226.169.210/API/sendsms.php?SPID=62&LOGIN=Zen%20&PASS=Zen%212019%24&TEXT=${smsText}&SC=ZN_LOGISTIC&MOBILE=216${phoneNumber}`;
  const options = {
    method: 'GET',
    rejectUnauthorized: false
  };

  return new Promise((resolve, reject) => {
    https.get(smsUrl, options, (response) => {
      let responseData = '';

      response.on('data', (chunk) => {
        responseData += chunk;
      });

      response.on('end', () => {
        try {
          // Supposons que la réponse soit au format JSON
          const responseJson = JSON.parse(responseData);

          if (responseJson.ref && responseJson.code) {
            const smsData = {
              ref: responseJson.ref,
              content: message,
              mobile: phone,
              status: responseJson.code === 200 ? "Sent" : "error",
              code: responseJson.code
            };

               SmsModel.create(smsData, (err, data) => {
                        if (err) {
                          console.error("Erreur lors de l'insertion dans la base de données : ", err);
                          return res.status(500).json({ error: "Erreur lors de l'enregistrement du SMS dans la base de données" });
                        }
                        console.log("SMS enregistré avec succès", data);
                      });

            console.log("SMS envoyé avec succès : ", smsData);
            resolve(smsData);
          } else {
            console.error("Réponse du service SMS invalide : ", responseJson);
            reject(new Error("Réponse du service SMS invalide"));
          }
        } catch (error) {
          console.error("Erreur lors du traitement de la réponse du service SMS : ", error);
          reject(new Error("Erreur interne du serveur"));
        }
      });
    }).on('error', (error) => {
      console.error("Erreur lors de l'envoi du SMS :", error);
      reject(new Error("Erreur lors de l'envoi du SMS : " + error.message));
    });
  });
}



  
  module.exports = {
    getDetailsByIds, 
    getReservationData,
    getDetailsExcludingReservationDate,
    sendEmail,
    sendingSMS
  };
  
