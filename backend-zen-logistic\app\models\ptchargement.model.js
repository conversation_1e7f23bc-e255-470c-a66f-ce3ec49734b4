const sql = require("./db.js");
const cron = require('node-cron');
const https = require('https');
const Notification = require("../models/notification.model.js");
require('dotenv').config();




DATAMATRIX_URL= process.env.DATAMATRIX_URL
DATAMATRIX_KEY= process.env.DATAMATRIX_KEY

const Ptchargement = function (ligneCmd) {
  //console.log('Received data in Ptchargement model:', ligneCmd);

  this.telephone = ligneCmd.telephone;
  this.idCommande = ligneCmd.idCommande;
  this.from_destination = ligneCmd.from_destination;
  this.to_destination = ligneCmd.to_destination;
  this.kilometrage = 0;
  this.type_ligne = ligneCmd.type_ligne;
  this.volume = ligneCmd.volume;
  this.id_client = ligneCmd.id_client;
  this.date_depart = ligneCmd.date_depart;
  this.date_arrivee = ligneCmd.date_arrivee;
  this.ajoutee_par = ligneCmd.ajoutee_par;
  this.quantite = ligneCmd.quantite;
  this.status = null;
  this.id_demandeur = ligneCmd.id_demandeur;
  this.nom_depart = ligneCmd.nom_depart;
  this.nom_arrivee = ligneCmd.nom_arrivee;
  this.emplacement = null
  this.estimation = ligneCmd.estimation
  this.sku = ligneCmd.sku

};

function notifyUser(userId,url, message) {

  console.log("******************",userId,message)
  sql.query("INSERT INTO notifications (id_user,url, message) VALUES (?,?,?)", [userId,url, message], (err, res) => {
      if (err) {
          console.log("error inserting notification: ", err);
      } else {
          console.log(`Notification sent to user ${userId}: ${message}`);
      }
  });
}



Ptchargement.create = (newPtchargement, result) => {

  sql.query(
    `SELECT nom_utilisateur FROM utilisateurs WHERE id = ?`,
    newPtchargement.id_demandeur,
    (err, resUser) => {
      if (err) {
        //console.log("Erreur lors de la récupération du nom_utilisateur : ", err);
        result(err, null);
        return;
      }

      // Si aucun utilisateur n'est trouvé avec cet ID, nous retournons une erreur
      if (resUser.length === 0) {
        //console.log("Aucun utilisateur trouvé avec l'ID_demandeur :", newPtchargement.id_demandeur);
        result({ message: "Aucun utilisateur trouvé avec cet ID" }, null);
        return;
      }

      // Si un utilisateur est trouvé, nous assignons le nom_utilisateur à la propriété ajoutee_par de newPtchargement
      newPtchargement.ajoutee_par = resUser[0].nom_utilisateur;

      // Maintenant, nous continuons avec le reste de la logique pour créer le point de chargement
      // ...

      // Par exemple, après cette section, vous pourriez exécuter l'insertion dans la base de données
      // sql.query('INSERT INTO ...', newPtchargement, (err, res) => {...});
    }
  );

  // Fetch nom_region for from_destination
      sql.query(
        `SELECT 
    r.nom_region AS nom_region_from,
    v.id AS idVille,
    v.nom_ville AS nom_ville,
    r.id
    FROM 
    destination d
    JOIN 
    region r ON d.id_region = r.id
    JOIN 
    ville v ON r.idVille = v.id
    WHERE 
    d.id = ?;
    `,
    newPtchargement.from_destination,
    (err, resFrom) => {
      if (err) {
        console.log("error fetching nom_region for from_destination: ", err);
        result(err, null);
        return;
      }

      // Fetch nom_region for to_destination
      sql.query(
        `SELECT 
        r.nom_region AS nom_region_from,
        v.id AS idVille,
        v.nom_ville AS nom_ville,
        r.id
        FROM 
        destination d
        JOIN 
        region r ON d.id_region = r.id
        JOIN 
        ville v ON r.idVille = v.id
        WHERE 
        d.id = ?;
        `,
        newPtchargement.to_destination,
        (err, resTo) => {
          if (err) {
            console.log("error fetching nom_region for to_destination: ", err);
            result(err, null);
            return;
          }


          if (resFrom[0].idVille == 1 && resTo[0].idVille == 1) {
            newPtchargement.emplacement = "sfax"
          } else {
            newPtchargement.emplacement = "hors sfax"
          }



          // const origin = 'délégation de '+  resFrom[0].nom_region_from + " "+resFrom[0].nom_ville + " "+"Tunisie";
          // const destination = 'délégation de '+ resTo[0].nom_region_from+ " "+resTo[0].nom_ville+ " "+"Tunisie";

          let origin = 'délégation de ' + resFrom[0].nom_region_from + ", " + resFrom[0].nom_ville + ", " + "Tunisie";
          let destination = 'délégation de ' + resTo[0].nom_region_from + ", " + resTo[0].nom_ville + ", " + "Tunisie";



          //console.log("-------------", destination)
          //console.log("-------------", origin)

          const id1 = resFrom[0].id;
          const id2 = resTo[0].id;



          sql.query(
            'SELECT * FROM matrix WHERE (id_from = ? AND id_to = ?) OR (id_from = ? AND id_to = ?)',
            [id1, id2, id2, id1],
            (err, existingRecord) => {
              if (err) {
                console.log("error checking existing record in matrix table: ", err);
                result(err, null);
                return;
              }

              if (existingRecord.length > 0) {
                // If a record already exists, you can handle it accordingly (update, skip, etc.)
                //console.log("Record already exists in matrix table:", existingRecord);

                const existingDistance = existingRecord[0].distance;
                //console.log("Record already exists in matrix table. Setting newPtchargement.kilometrage:", existingDistance);

                const distanceString = existingDistance.replace("Km", "");
                const distanceDecimal = parseFloat(distanceString);
                newPtchargement.kilometrage = distanceDecimal;

                if (id1 == id2) {
                  newPtchargement.kilometrage = 3;
                }

                executePointChargementInsertion();
              } else {






                calculateDistance(`${DATAMATRIX_URL}?origins=${origin}&destinations=${destination}&key=${DATAMATRIX_KEY}`)
                  .then(data => {
                    let distance = data.rows[0].elements[0].distance.text;
                    sql.query(
                      'INSERT INTO matrix (id_from, id_to, distance) VALUES (?, ?, ?)',
                      [id1, id2, distance],
                      (err, res) => {
                        if (err) {
                          console.log("error inserting into matrix table: ", err);
                          result(err, null);
                          return;
                        }

                        const distanceString = distance.replace("Km", "");
                        const distanceDecimal = parseFloat(distanceString);
                        newPtchargement.kilometrage = distanceDecimal;

                        executePointChargementInsertion();
                      }
                    );
                  })
                  .catch(error => {
                    console.error('Error calling distance matrix API:', error);
                    result(error, null);
                  });

              }
            }
          );

          function executePointChargementInsertion() {
            const id_dech = newPtchargement.to_destination;
            const id_charg = newPtchargement.from_destination;
          
            const selectQueryDech = `
              SELECT destination.id_fournisseur, fournisseur.sous_traitant, fournisseur.mat_fisc
              FROM destination
              JOIN fournisseur ON destination.id_fournisseur = fournisseur.id
              WHERE destination.id = ?;
            `;
          
            const selectQueryCharg = `
              SELECT destination.id_fournisseur, fournisseur.sous_traitant, fournisseur.mat_fisc
              FROM destination
              JOIN fournisseur ON destination.id_fournisseur = fournisseur.id
              WHERE destination.id = ?;
            `;
          
            // Fonction pour traiter les résultats de la première requête
            sql.query(selectQueryDech, [id_dech], (errDech, rowsDech) => {
              if (errDech) {
                //console.log("Erreur dans la première requête : ", errDech);
                return result(errDech, null);
              }
          
              // Traitement des résultats de la première requête (rowsDech)
              //console.log("Résultats de la première requête : ", rowsDech);
          
              // Ensuite, vous pouvez effectuer la deuxième requête ici
              sql.query(selectQueryCharg, [id_charg], (errCharg, rowsCharg) => {
                if (errCharg) {
                  //console.log("Erreur dans la deuxième requête : ", errCharg);
                  return result(errCharg, null);
                }
          
                // Traitement des résultats de la deuxième requête (rowsCharg)
                //console.log("Résultats de la deuxième requête : ", rowsCharg);
          
                // Accès aux propriétés de chaque ligne
                const sousTraitantCharg = rowsCharg[0].sous_traitant;
                const idFournisseurCharg = rowsCharg[0].id_fournisseur;
                const matFiscCharg = rowsCharg[0].mat_fisc;
          
                const sousTraitantDech = rowsDech[0].sous_traitant;
                const idFournisseurDech = rowsDech[0].id_fournisseur;
                const matFiscDech = rowsDech[0].mat_fisc;
          
                // Logique pour déterminer newPtchargement.id_client
                if (matFiscCharg === '0000' && matFiscDech !== '0000') {
                  newPtchargement.id_client = idFournisseurDech;
                  //console.log(newPtchargement, "matFiscCharg '000'");
                } else if (matFiscDech === '0000' && matFiscCharg !== '0000') {
                  newPtchargement.id_client = idFournisseurCharg;
                  //console.log(newPtchargement, "matFiscDech '0000'");
                } else {
                  if (sousTraitantCharg && !sousTraitantDech) {
                    newPtchargement.id_client = idFournisseurCharg;
                    //console.log(newPtchargement, "if00");
                  } else if (!sousTraitantCharg && sousTraitantDech) {
                    newPtchargement.id_client = idFournisseurDech;
                    //console.log(newPtchargement, "if01");
                  } else {
                    if (
                      newPtchargement.type_ligne == "Livraison PSF" ||
                      newPtchargement.type_ligne == "livraison MP (tissu)" ||
                      newPtchargement.type_ligne == "livraison PF (magasin)" ||
                      newPtchargement.type_ligne == "Agencement et Matériels" ||
                      newPtchargement.type_ligne == "Transfert PF inter magasin" ||
                      newPtchargement.type_ligne == "Transfert Administratif inter magasin" ||
                      newPtchargement.type_ligne == "Transfert administratif ZEN Group-Magasin ou dépôt" ||
                      newPtchargement.type_ligne == "Transfert administratif technique et matériel informatique"
                    ) {
                      newPtchargement.id_client = idFournisseurDech;
                      //console.log(idFournisseurDech, "if2");
                    } else if (
                      newPtchargement.type_ligne == "livraison PF (dépôt)" ||
                      newPtchargement.type_ligne == "Retour articles"
                    ) {
                      newPtchargement.id_client = idFournisseurCharg;
                      //console.log(newPtchargement, "else");
                    }
                  }
                }
          
                if (
                  newPtchargement.type_ligne == "Livraison fourniture" &&
                  !newPtchargement.id_client
                ) {
                  const errorMessage = "Erreur: 'id_client' ne peut pas être null pour 'Livraison fourniture'.";
                  console.error(errorMessage);
                  return result(errorMessage, null); // Retourner l'erreur
                }
          
                newPtchargement.status = "en attente de confirmation";
          
                const insertQuery = `INSERT INTO point_chargement SET ?`;
          
                // Execute the query
                sql.query(insertQuery, newPtchargement, (err, res) => {
                  if (err) {
                    console.log("error: ", err);
                    return result(err, null);
                  }
          
                  //console.log("created Ptchargement: ", { id: res.insertId, ...newPtchargement });
                  result(null, { id: res.insertId, ...newPtchargement });
                });
              });
            });
          }
          

        }
      );
    }
  );
};
const calculateDistance = (url) => {
  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      let data = '';
      response.on('data', (chunk) => {
        data += chunk;
      });
      response.on('end', () => {
        resolve(JSON.parse(data));
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
};
Ptchargement.getAll = result => {
  sql.query("SELECT * FROM point_chargement  ", (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res);
  });
}


Ptchargement.findAllValid = (result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const query = `
    SELECT 
      pc.*, 
      d_from.adresse AS depart, 
      d_to.adresse AS arrivee 
    FROM 
      point_chargement pc 
    JOIN 
      destination d_from ON pc.from_destination = d_from.id 
    JOIN 
      destination d_to ON pc.to_destination = d_to.id 
    WHERE 
      pc.status = 'Valide' 
    ORDER BY 
      CASE 
        WHEN pc.driver_confirm = false AND pc.date_reservation IS NOT NULL THEN 1 
        ELSE 2 
      END,
      pc.date_arrivee DESC
  `;

  sql.query(query, (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(null, err);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res);
  });
};



Ptchargement.findAllValidWithLocalisation = result => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  sql.query(`
    SELECT pc.*, 
           d_from.adresse AS depart, 
           d_to.adresse AS arrivee, 
           v1.nom_ville AS nom_ville_depart, 
           v2.nom_ville AS nom_ville_arrivee, 
           r1.nom_region AS nom_region_depart, 
           r2.nom_region AS nom_region_arrivee
    FROM point_chargement pc
    JOIN destination d_from ON pc.from_destination = d_from.id
    JOIN destination d_to ON pc.to_destination = d_to.id
    LEFT JOIN region r1 ON d_from.id_region = r1.id
    LEFT JOIN region r2 ON d_to.id_region = r2.id
    LEFT JOIN ville v1 ON r1.idVille = v1.id
    LEFT JOIN ville v2 ON r2.idVille = v2.id
    WHERE pc.status = 'Valide'
    ORDER BY pc.id ASC
  `, (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(null, err);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res);
  });
};

Ptchargement.remove = (id, result) => {
  sql.query("DELETE FROM point_chargement WHERE id = ?", id, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }

    if (res.affectedRows == 0) {
      // not found Ptchargement with the id
      result({ kind: "not_found" }, null);
      return;
    }

    //console.log("deleted point chargement with id: ", id);
    result(null, res);
  });
};

Ptchargement.getComm = (idCommande, result) => {
  //console.log(`SELECT * FROM point_chargement WHERE idCommande = ${idCommande} `)
  sql.query(`SELECT * FROM point_chargement WHERE idCommande = ${idCommande} `, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err), null;
      return;
    }

    if (res.length) {
      //console.log("found commande: ");
      result(null, res);
      return;
    }

    // not found commande with the iduser 
    result({ kind: "not_found" }, null);
  });
};
Ptchargement.getPtdech = (idCommande, result) => {
  //console.log(`SELECT * FROM point_chargement WHERE idCommande= ${idCommande} `)
  sql.query(`SELECT * FROM point_chargement WHERE idCommande = ${idCommande} `, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err), null;
      return;
    }

    if (res.length) {
      //console.log("found commande: ");
      result(null, res);
      return;
    }

    // not found commande with the iduser 
    result({ kind: "not_found" }, null);
  });
};
Ptchargement.getALLByCMD = (idCommande, result) => {
  const query = `
  SELECT 
    point_chargement.*,
    destination_from.adresse as from_destination_adresse,
    destination_from.email as from_destination_email,
    destination_from.id as from_destination_id,
    destination_from.id_fournisseur as from_destination_id_fournisseur,
    destination_from.id_region as from_destination_id_region,
    destination_from.nom_locale as from_destination_nom_locale,
    destination_from.phone as from_destination_phone,
    destination_to.adresse as to_destination_adresse,
    destination_to.email as to_destination_email,
    destination_to.id as to_destination_id,
    destination_to.id_fournisseur as to_destination_id_fournisseur,
    destination_to.id_region as to_destination_id_region,
    destination_to.nom_locale as to_destination_nom_locale,
    destination_to.phone as to_destination_phone,
    kilometrage,
    telephone,
    type_ligne,
    volume,
    quantite,
    estimation,
    conducteur.nom as conducteur_nom,
    conducteur.prenom as conducteur_prenom,
    conducteur.mobile as conducteur_mobile,
    fournisseur.id as id_client,
    fournisseur.mat_fisc,
    fournisseur.nom_fournisseur
  FROM point_chargement
  LEFT JOIN destination as destination_from ON point_chargement.from_destination = destination_from.id
  LEFT JOIN destination as destination_to ON point_chargement.to_destination = destination_to.id
  LEFT JOIN fournisseur ON point_chargement.id_client = fournisseur.id -- Assurez-vous que la colonne id_client existe
  LEFT JOIN conducteur ON point_chargement.id_conducteur = conducteur.id

  WHERE point_chargement.idCommande = ?;
`;

  sql.query(query, [idCommande], (err, res) => {
    if (err) {
      console.error("Error executing query:", err);
      result(err, null);
      return;
    }

    if (res.length > 0) {
      // Transformez le résultat pour obtenir la structure souhaitée
      const transformedResult = res.map(row => {
        return {
          from_destination: {
            id: row.from_destination_id,
            nom_locale: row.from_destination_nom_locale,
            adresse: row.from_destination_adresse,
            phone: row.from_destination_phone,
            email: row.from_destination_email,
            // ... autres propriétés ...
          },
          to_destination: {
            id: row.to_destination_id,
            nom_locale: row.to_destination_nom_locale,
            adresse: row.to_destination_adresse,
            phone: row.to_destination_phone,
            email: row.to_destination_email,
            // ... autres propriétés ...
          },
          conducteur: {
            nom: row.conducteur_nom,
            prenom: row.conducteur_prenom,
            mobile: row.conducteur_mobile
            // ... autres propriétés ...
          },
          date_voyage: row.date_voyage,
          commentaire: row.commentaire,
          kilometrage: row.kilometrage,
          telephone: row.telephone,
          type_ligne: row.type_ligne,
          volume: row.volume,
          id: row.id,
          status: row.status,
          quantite: row.quantite,
          estimation: row.estimation

          // ... autres propriétés ...
        };
      });

      //console.log("Found points de chargement:", transformedResult);
      result(null, transformedResult);
    } else {
      // Aucun point de chargement trouvé avec l'idCommande spécifié
      result({ kind: "not_found" }, null);
    }
  });

};

Ptchargement.addLieuxDechar = (req, result) => {
  sql.query("INSERT INTO lieux_dech (  `user_id`, `libelle`) VALUES (?,?)", [req.user_id, req.libelle], (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    result(null, { exp: req.idExp, client: req.libelle });
  });
};

Ptchargement.updateVolume = (id, newVolume, result) => {
  sql.query("UPDATE point_chargement SET volume = ? WHERE id = ?", [newVolume, id], (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.affectedRows === 0) {
      // Aucun point de chargement trouvé avec l'id spécifié
      result({ kind: "not_found" }, null);
      return;
    }

    //console.log("updated Ptchargement: ", { id, volume: newVolume });
    result(null, { id, volume: newVolume });
  });
};
Ptchargement.updateStatusValid = (id, result) => {
  sql.query("UPDATE point_chargement SET status = 'Valide', date_validation = NOW() WHERE id = ?", [id], (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.affectedRows === 0) {
      // Aucun point de chargement trouvé avec l'id spécifié
      result({ kind: "not_found" }, null);
      return;
    }
    result(null, { id, status: 'Valide', date_validation: new Date() });
  });
};



Ptchargement.updateReserved = (updates, result) => {
  const updatePromises = updates.map((update) => {
    const { id, id_camion, id_conducteur, date_voyage, plage_horaire_heure_arr, plage_horaire_heure_dep, tolerance } = update;

    return new Promise((resolve, reject) => {
      sql.query(
        "SELECT id, id_demandeur, nom_depart, nom_arrivee, status FROM point_chargement WHERE id = ?",
        [id],
        (err, res) => {
          if (err) {
            return reject(err);
          }

          if (res.length === 0) {
            return reject({ kind: "not_found", id });
          }

          const status = res[0].status;
          const id_demandeur = res[0].id_demandeur;
          const destination = `du ${res[0].nom_depart} vers ${res[0].nom_arrivee}`;

          if (status === "Annulé") {
            return reject({ kind: `already_cancelled`, id });
          }

          sql.query(
            "UPDATE point_chargement SET status = 'Réservé', id_camion = ?, id_conducteur = ?, date_voyage = ?, date_reservation = NOW(), H_depart = ?, H_arrivee = ?, tolerance = ? WHERE id = ?",
            [id_camion, id_conducteur, date_voyage, plage_horaire_heure_dep, plage_horaire_heure_arr, tolerance, id],
            (err, res) => {
              if (err) {
                return reject(err);
              }

              if (res.affectedRows === 0) {
                return reject({ kind: "not_found", id });
              }

              resolve({ id, id_camion, id_conducteur, date_voyage, id_demandeur, destination });

              // Notification pour l'utilisateur (pas Firebase)
              notifyUser(
                id_demandeur,
                "pages/listcommande",
                `Le Transfert avec ID ${id} ${destination} a été réservé le ${date_voyage}.`
              );
            }
          );
        }
      );
    });
  });

  Promise.allSettled(updatePromises)
    .then((results) => {
      const successfulUpdates = results
        .filter((result) => result.status === "fulfilled")
        .map((result) => result.value);

      const failedUpdates = results
        .filter((result) => result.status === "rejected")
        .map((result) => result.reason);

      if (successfulUpdates.length > 0) {
        // Envoi d'une seule notification Firebase après toutes les mises à jour
        Notification.sendNotificationToUserFireBase(
          successfulUpdates[0].id_conducteur, 
          "Nouvelle Réservation Disponible", 
          "Une réservation vous attend. Merci de confirmer votre disponibilité."
        );
      }

      if (failedUpdates.length > 0) {
        result({ kind: "partial_failure", failedUpdates }, successfulUpdates);
      } else {
        result(null, successfulUpdates);
      }
    })
    .catch((err) => {
      result(err, null);
    });
};


Ptchargement.findAllReserved = result => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  sql.query("SELECT * FROM point_chargement WHERE status = 'Réservé'", (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(null, err);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res);
  });
};

Ptchargement.findReservedByDateAndCamion = (id_camion, date, id_conducteur, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  // Utilisation de paramètres préparés pour éviter les injections SQL
  const query = `
    SELECT pc.*, f.nom_fournisseur as nom_client
    FROM point_chargement pc
    LEFT JOIN fournisseur f ON pc.id_client = f.id
    WHERE pc.status = 'Réservé' AND pc.id_camion = ? AND pc.date_voyage = ? AND pc.id_conducteur = ?
  `;

  sql.query(query, [id_camion, date, id_conducteur], (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(err, null);
      return;
    }
    result(null, res);
  });
};

Ptchargement.updateReservation = (id, id_camion, id_conducteur, date_voyage, H_depart, H_arrivee, tolerance, result) => {

  sql.query(
    "SELECT id_camion, id_conducteur FROM point_chargement WHERE id = ?",
    [id],
    (err, res) => {
      if (err) {
        //console.log("Erreur lors de la récupération des anciennes valeurs :", err);
        result(err, null);
        return;
      }

      if (res.length === 0) {
        result({ kind: "not_found" }, null);
        return;
      }

      const old_camion = res[0].id_camion;
      const old_conducteur = res[0].id_conducteur;

      // Mettre à jour les colonnes old_camion et old_conducteur avec les anciennes valeurs
      sql.query(
        "UPDATE point_chargement SET old_camion = ?, old_conducteur = ?, id_camion = ?, id_conducteur = ?, date_voyage = ?, date_reservation = NOW(),H_depart=?,H_arrivee=?,tolerance=?  WHERE id = ?",
        [old_camion, old_conducteur, id_camion, id_conducteur, date_voyage, H_depart, H_arrivee, tolerance, id],
        (err, res) => {
          if (err) {
            //console.log("Erreur lors de la mise à jour du point de chargement :", err);
            result(err, null);
            return;
          }

          if (res.affectedRows === 0) {
            // Aucun point de chargement trouvé avec l'ID spécifié
            result({ kind: "not_found" }, null);
            return;
          }

          //console.log("Point de chargement mis à jour :", { id, id_camion, id_conducteur, date_voyage, date_reservation: new Date() });
          result(null, { id, id_camion, id_conducteur, date_reservation: new Date() });
        }
      );
    }
  );
};

Ptchargement.updateStatusCancel = (id, result) => {
  sql.query("UPDATE point_chargement SET status = 'Annulé', date_validation = NOW() WHERE id = ?", [id], (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.affectedRows === 0) {
      result({ kind: "not_found" }, null);
      return;
    }

    //console.log("updated Ptchargement: ", { id, status: 'Valide', date_validation: new Date() });
    result(null, { id, status: 'Valide', date_validation: new Date() });
  });
};
Ptchargement.updatePanne = (id, result) => {
  sql.query("UPDATE point_chargement SET status='Réservé',panne = true WHERE id = ?", [id], (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    if (res.affectedRows === 0) {
      result({ kind: "not_found" }, null);
      return;
    }
    //console.log("updated Ptchargement: ", { id, panne: false });
    result(null, { id, status: 'Valide', date_validation: new Date() });
  });
};

// Ptchargement.adjustPrice = (idList, result) => {
//   //console.log(idList)

//   if (res.affectedRows === 0) {
//     result({ kind: "not_found" }, null);
//     return;
//   }
//   //console.log("updated Ptchargement: ", { id, status: 'Livré', date_livraison: new Date() });
//   result(null, { id, status: 'Livré', date_livraison: new Date() });
// };



// const calculatePrice = (type_ligne, kilometrage, volume, quantite, priceRow) => {
//   let PU;
//   let PrixTot;
//   const roundedKilometrage = Math.ceil(kilometrage);

//   if (
//     type_ligne === "Agencement et Matériels" ||
//     type_ligne === "livraison MP (tissu)" ||
//     type_ligne === "livraison PF (dépôt)" ||
//     type_ligne === "livraison PF (magasin)" ||
//     type_ligne === "Livraison PSF" ||
//     type_ligne === "Retour articles" ||
//     type_ligne === "Transfert PF inter magasin"
//   ) {
//     if (roundedKilometrage < priceRow.distance) {
//       PU = priceRow.prix_court_distance;
//       PrixTot = parseFloat((priceRow.prix_court_distance * roundedKilometrage * volume).toFixed(3));
//       if (PrixTot < priceRow.montant) {
//         PrixTot = priceRow.montant;
//         PU = priceRow.montant;
//       }
//     } else {
//       PU = priceRow.prix_long_distance;
//       PrixTot = parseFloat((priceRow.prix_long_distance * roundedKilometrage * volume).toFixed(3));
//       if (PrixTot < priceRow.montant) {
//         PrixTot = priceRow.montant;
//         PU = priceRow.montant;
//       }
//     }
//   } else if (
//     type_ligne === "Livraison fourniture" ||
//     type_ligne === "Transfert administratif technique et matériel informatique"
//   ) {
//     if (roundedKilometrage < priceRow.distance) {
//       PU = priceRow.prix_court_distance;
//       PrixTot = priceRow.prix_court_distance * quantite;
//     } else {
//       PU = priceRow.prix_long_distance;
//       PrixTot = parseFloat((priceRow.prix_long_distance * quantite).toFixed(3));
//     }
//   } else if (
//     type_ligne === "Transfert Administratif inter magasin" ||
//     type_ligne === "Transfert administratif ZEN Group-Magasin ou dépôt"
//   ) {
//     if (roundedKilometrage < priceRow.distance) {
//       PU = priceRow.prix_court_distance;
//       PrixTot = priceRow.prix_court_distance;
//     } else {
//       PU = priceRow.prix_long_distance;
//       PrixTot = priceRow.prix_long_distance;
//     }
//   }

//   return { PU, PrixTot };
// };


Ptchargement.findExpedied = (id_camion, date, id_conducteur, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }
  const query = `
    SELECT pc.*, f.nom_fournisseur AS nom_client
    FROM point_chargement pc
    LEFT JOIN fournisseur f ON pc.id_client = f.id
    WHERE pc.status = 'Expédier' AND pc.id_camion = ? AND pc.date_voyage = ? AND pc.id_conducteur = ?
  `;

  //console.log("Requête SQL avant exécution :", query);

  sql.query(query, [id_camion, date, id_conducteur], (err, res) => {
    if (err) {
      console.error("Erreur lors de l'exécution de la requête :", err);
      result(err, null);
      return;
    }

    //console.log("Requête SQL après exécution :", query);

    //console.log("Ptchargement avec jointure fournisseur: ", res);
    result(null, res);
  });
};

Ptchargement.findLineToAdjust = (id_camion, date, id_conducteur, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }
  const query = `
    SELECT pc.*, f.nom_fournisseur AS nom_client
    FROM point_chargement pc
    LEFT JOIN fournisseur f ON pc.id_client = f.id
    WHERE (pc.status = 'no pick up' OR pc.status = 'Livré') 
    AND pc.id_camion = ? AND pc.date_voyage = ? AND pc.id_conducteur = ?
  `;

  sql.query(query, [id_camion, date, id_conducteur], (err, res) => {
    if (err) {
      console.error("Erreur lors de l'exécution de la requête :", err);
      result(err, null);
      return;
    }

    result(null, res);
  });
};


Ptchargement.findAFactureByDateAndClient = (date_debut, date_fin, id_client, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const date_debut1 = new Date(date_debut);
  const date_fin1 = new Date(date_fin);
  date_fin1.setHours(23, 59, 59, 999);
  const query = `
  SELECT pc.*, f.nom_fournisseur AS nom_client, f.mat_fisc AS mat_fisc,f.adresse AS adresse
  FROM point_chargement pc
  JOIN fournisseur f ON pc.id_client = f.id
  WHERE pc.status = 'à facturer' OR pc.status = 'no pick up'
  AND pc.date_livraison BETWEEN ? AND ?
    AND pc.id_client = ?
`;


  //console.log("Requête SQL avant exécution :", query);
  //console.log("Paramètres de la requête :", [date_debut1, date_fin1, id_client]);

  sql.query(query, [date_debut1, date_fin1, id_client], (err, res) => {
    if (err) {
      console.error("Erreur lors de l'exécution de la requête :", err);
      result(err, null);
      return;
    }

    //console.log("Requête SQL après exécution :", query);
    //console.log("Ptchargement avec jointure fournisseur: ", res);
    result(null, res);
  });
};

Ptchargement.findLivredByDateAndtype = (date_debut, date_fin, type, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const date_debut1 = new Date(date_debut);
  const date_fin1 = new Date(date_fin);
  date_fin1.setHours(23, 59, 59, 999);
  const query = `
  SELECT pc.*, f.nom_fournisseur AS nom_client
  FROM point_chargement pc
  JOIN fournisseur f ON pc.id_client = f.id
  WHERE pc.status = 'Livré'
    AND pc.date_livraison BETWEEN ? AND ?
    AND pc.type_ligne = ?
`;


  //console.log("Requête SQL avant exécution :", query);
  //console.log("Paramètres de la requête :", [date_debut1, date_fin1, type]);

  sql.query(query, [date_debut1, date_fin1, type], (err, res) => {
    if (err) {
      console.error("Erreur lors de l'exécution de la requête :", err);
      result(err, null);
      return;
    }

    //console.log("Requête SQL après exécution :", query);
    //console.log("Ptchargement avec jointure fournisseur: ", res);
    result(null, res);
  });
};


Ptchargement.updateClient = (id, newId, result) => {
  sql.query(
    "UPDATE point_chargement SET old_id_client = id_client, id_client = ? WHERE id = ?",
    [newId, id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      if (res.affectedRows === 0) {
        // Aucun point de chargement trouvé avec l'id spécifié
        result({ kind: "not_found" }, null);
        return;
      }

      //console.log("updated Ptchargement: ", { newId });
      result(null, { id, old_id_client: id, id_client: newId });
    }
  );
};



Ptchargement.findReservedByIdUser = (idDemandeur, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const query = `
    SELECT pc.*, CONCAT(c.nom, ' ', c.prenom) as conducteur_nom_prenom, c.mobile
    FROM point_chargement pc
    JOIN conducteur c ON pc.id_conducteur = c.id
    WHERE pc.status = 'Réservé' AND pc.id_demandeur = ?
    ORDER BY pc.id DESC;  -- Ajoutez cette ligne pour trier par date_depart en ordre décroissant
  `;

  sql.query(query, [idDemandeur], (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(null, err);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res);
  });
};



Ptchargement.findExpediedByIdUser = (idDemandeur, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const query = `
    SELECT pc.*, CONCAT(c.nom, ' ', c.prenom) as conducteur_nom_prenom, c.mobile
    FROM point_chargement pc
    JOIN conducteur c ON pc.id_conducteur = c.id
    WHERE pc.status = 'Expédier' AND pc.id_demandeur = ?
    ORDER BY pc.id DESC;  -- Ajoutez cette ligne pour trier par date_depart en ordre décroissant
  `;

  sql.query(query, [idDemandeur], (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(null, err);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res);
  });
};


Ptchargement.findLivredByIdUser = (idDemandeur, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const query = `
    SELECT pc.*, CONCAT(c.nom, ' ', c.prenom) as conducteur_nom_prenom, c.mobile
    FROM point_chargement pc
    JOIN conducteur c ON pc.id_conducteur = c.id
    WHERE pc.status = 'Livré' AND pc.id_demandeur = ?
    ORDER BY pc.id DESC;  -- Ajoutez cette ligne pour trier par date_depart en ordre décroissant
  `;

  sql.query(query, [idDemandeur], (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(null, err);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res);
  });
};

Ptchargement.countValid = (callback) => {
  const query = `
    SELECT COUNT(*) as count
    FROM point_chargement pc
    WHERE pc.status = 'Valide'
  `;

  sql.query(query, (err, result) => {
    if (err) {
      console.error("Error: ", err);
      // Gérer l'erreur et renvoyer une réponse appropriée
      return callback(err, null);
    }

    const numberOfValidPoints = result[0].count;
    //console.log("Number of Valid Points: ", numberOfValidPoints);

    // Appeler le rappel avec le résultat
    callback(null, numberOfValidPoints);
  });
};

Ptchargement.countReserved = (callback) => {
  const query = `
    SELECT COUNT(*) as count
    FROM point_chargement pc
    WHERE pc.status = 'Réservé'
  `;

  sql.query(query, (err, result) => {
    if (err) {
      console.error("Error: ", err);
      // Gérer l'erreur et renvoyer une réponse appropriée
      return callback(err, null);
    }

    const numberOfValidPoints = result[0].count;
    //console.log("Number of reserved Points: ", numberOfValidPoints);

    // Appeler le rappel avec le résultat
    callback(null, numberOfValidPoints);
  });
};

Ptchargement.countExpedied = (callback) => {
  const query = `
    SELECT COUNT(*) as count
    FROM point_chargement pc
    WHERE pc.status = 'Expédier'
  `;

  sql.query(query, (err, result) => {
    if (err) {
      console.error("Error: ", err);
      // Gérer l'erreur et renvoyer une réponse appropriée
      return callback(err, null);
    }

    const numberOfValidPoints = result[0].count;
    //console.log("Number of expedied Points: ", numberOfValidPoints);

    // Appeler le rappel avec le résultat
    callback(null, numberOfValidPoints);
  });
};
Ptchargement.countDelivred = (callback) => {
  const query = `
    SELECT COUNT(*) as count
    FROM point_chargement pc
    WHERE pc.status = 'Livré'
  `;

  sql.query(query, (err, result) => {
    if (err) {
      console.error("Error: ", err);
      // Gérer l'erreur et renvoyer une réponse appropriée
      return callback(err, null);
    }

    const numberOfValidPoints = result[0].count;
    //console.log("Number of delivererd Points: ", numberOfValidPoints);

    // Appeler le rappel avec le résultat
    callback(null, numberOfValidPoints);
  });
};

Ptchargement.findVoyageList = (result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const query = `
  SELECT DISTINCT
  co.nom,
  co.prenom,
  ca.immatriculation,
  ca.id AS id_camion,
  co.id AS id_conducteur,
  pc.date_voyage
FROM
  point_chargement pc
JOIN
  camion ca ON pc.id_camion = ca.id
JOIN
  conducteur co ON pc.id_conducteur = co.id
WHERE
  pc.status IN ('Réservé');

  `;

  sql.query(query, [], (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(null, err);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res);
  });
};


Ptchargement.findVoyageListExp = (result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const query = `
    SELECT DISTINCT
      co.nom,
      co.prenom,
      ca.immatriculation,
      ca.id AS id_camion,
      co.id AS id_conducteur,
      pc.date_voyage
    FROM
      point_chargement pc
    JOIN
      camion ca ON pc.id_camion = ca.id
    JOIN
      conducteur co ON pc.id_conducteur = co.id
    WHERE
      pc.status IN ('Expédier');
  `;

  sql.query(query, [], (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(null, err);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res);
  });
};


Ptchargement.findVoyageListToAdjust = (result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const query = `
    SELECT DISTINCT
      co.nom,
      co.prenom,
      ca.immatriculation,
      ca.id AS id_camion,
      co.id AS id_conducteur,
      pc.date_voyage
    FROM
      point_chargement pc
    JOIN
      camion ca ON pc.id_camion = ca.id
    JOIN
      conducteur co ON pc.id_conducteur = co.id
    WHERE
      pc.status IN ('no pick up', 'Livré') 
      AND pc.date_voyage > '2024-12-31';
  `;

  sql.query(query, [], (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(null, err);
      return;
    }

    result(null, res);
  });
};




Ptchargement.findLigneInSametrip = (idCamion, idConducteur, dateVoyage, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const query = `
  SELECT SUM(volume) AS totalVolume, SUM(estimation) AS totalEstimation FROM point_chargement 
  WHERE id_camion = ? 
  AND id_conducteur = ? 
  AND date_voyage = ? 
  AND status = 'Réservé'
  `;

  sql.query(query, [idCamion, idConducteur, dateVoyage], (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(err, null);
      return;
    }

    //console.log("Ptchargement: ", res);
    // Si la requête renvoie un résultat, le premier élément de l'array contient le totalVolume et le totalEstimation
    let totalVolume = res.length > 0 ? res[0].totalVolume : 0;
    let totalEstimation = res.length > 0 ? res[0].totalEstimation : 0;

    if (totalVolume == null) {
      totalVolume = 0;
    }

    if (totalEstimation == null) {
      totalEstimation = 0;
    }


    // Retourne un objet avec les totaux
    result(null, { volume: totalVolume, estimation: totalEstimation });
  });
};




Ptchargement.findLignesByTypeAndSku = (type, sku, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  let query = `
  SELECT 
  pc.id, 
  pc.nom_depart, 
  pc.nom_arrivee, 
  pc.sku, 
  pc.status, 
  pc.date_creation, 
  pc.id_demandeur,
  pc.estimation,       
  pc.volume,  
  pc.quantite,     
  pc.ajoutee_par,
  pc.date_voyage, 

  CONCAT(c.nom, ' ', c.prenom) AS conducteur_nom_complet
FROM 
  point_chargement pc
LEFT JOIN
  conducteur c ON pc.id_conducteur = c.id
WHERE 
  pc.type_ligne = ? 
  AND pc.date_creation >= DATE_SUB(CURDATE(), INTERVAL 2 MONTH)
`;

  // Ajouter la condition `sku LIKE ?` seulement si `sku` est défini et non vide
  let parameters = [type];

  if (sku && sku.trim() !== '') {
    query += ' AND pc.sku LIKE ?'; // Ajouter la condition `sku`
    parameters.push(`%${sku}%`);  // Ajouter `sku` aux paramètres de requête
  }

  query += ' ORDER BY pc.date_creation DESC'; // Clause ORDER BY

  sql.query(query, parameters, (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(null, err);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res); // Retourner les résultats via le callback
  });
};


Ptchargement.findLignesByIdToVerification = (id, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  let query = `
  SELECT 
  pc.id, 
  pc.nom_depart, 
  pc.nom_arrivee, 
  pc.sku, 
  pc.status, 
  pc.date_creation, 
  pc.id_demandeur,
  pc.estimation,       
  pc.volume,  
  pc.quantite,     
  pc.ajoutee_par,
  pc.date_voyage, 

  CONCAT(c.nom, ' ', c.prenom) AS conducteur_nom_complet
FROM 
  point_chargement pc
LEFT JOIN
  conducteur c ON pc.id_conducteur = c.id
WHERE 
  pc.id = ? 
`;

  // Ajouter la condition `sku LIKE ?` seulement si `sku` est défini et non vide
  let parameters = [id];

  sql.query(query, parameters, (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(null, err);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res); // Retourner les résultats via le callback
  });
};



Ptchargement.findAFactureByClient = (date_debut, date_fin, id_client, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const date_debut1 = new Date(date_debut);
  const date_fin1 = new Date(date_fin);
  date_debut1.setHours(0, 0, 0, 0); // Début de la journée
  date_fin1.setHours(23, 59, 59, 999); // Fin de la journée

  const query = `
  SELECT pc.*, f.nom_fournisseur AS nom_client, f.mat_fisc AS mat_fisc, f.adresse AS adresse
  FROM point_chargement pc
  JOIN fournisseur f ON pc.id_client = f.id
  WHERE pc.status = 'à facturer'
    AND pc.date_livraison BETWEEN ? AND ?
    AND pc.id_client = ?
  `;

  //console.log("Requête SQL avant exécution :", query);
  //console.log("Paramètres de la requête :", [date_debut1, date_fin1, id_client]);

  // Exécution de la requête avec les bons paramètres
  sql.query(query, [date_debut1, date_fin1, id_client], (err, res) => {
    if (err) {
      console.error("Erreur lors de l'exécution de la requête :", err);
      result(err, null);
      return;
    }

    //console.log("Requête SQL après exécution :", query);
    //console.log("Ptchargement avec jointure fournisseur: ", res);
    result(null, res);
  });
};

Ptchargement.findAllLivred = (result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const query = `
  SELECT pc.id,pc.nom_depart,pc.nom_arrivee,pc.kilometrage,pc.volume,pc.prix_tot,pc.type_ligne,pc.date_voyage ,f.nom_fournisseur AS nom_client, f.mat_fisc AS mat_fisc, f.adresse AS adresse
  FROM point_chargement pc
  JOIN fournisseur f ON pc.id_client = f.id
  WHERE pc.status = 'Livré' order by pc.date_voyage DESC
`;

  //console.log("Requête SQL avant exécution :", query);

  sql.query(query, (err, res) => {
    if (err) {
      console.error("Erreur lors de l'exécution de la requête :", err);
      result(err, null);
      return;
    }

    //console.log("Requête SQL après exécution :", query);
    //console.log("Ptchargement avec jointure fournisseur: ", res);
    result(null, res);
  });
};


Ptchargement.findAllAdjusted = (result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const query = `
  SELECT pc.id,pc.nom_depart,
  pc.nom_arrivee,pc.quantite,pc.ajoutee_par,
  pc.kilometrage,pc.volume,pc.prix_tot,pc.type_ligne,pc.date_voyage ,pc.station_price,prix_unit,pc.date_livraison,pc.date_expedition,
  f.nom_fournisseur AS nom_client, f.mat_fisc AS mat_fisc, f.adresse AS adresse
  FROM point_chargement pc
  JOIN fournisseur f ON pc.id_client = f.id
  WHERE pc.status = 'Ajusté' order by pc.date_voyage DESC
`;

  //console.log("Requête SQL avant exécution :", query);

  sql.query(query, (err, res) => {
    if (err) {
      console.error("Erreur lors de l'exécution de la requête :", err);
      result(err, null);
      return;
    }

    //console.log("Requête SQL après exécution :", query);
    //console.log("Ptchargement avec jointure fournisseur: ", res);
    result(null, res);
  });
};

Ptchargement.updateStatusToBeInvoiced = (id, result) => {
  sql.query("UPDATE point_chargement SET status = 'à facturer' WHERE id = ?", [id], (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.affectedRows === 0) {
      // Aucun point de chargement trouvé avec l'id spécifié
      result({ kind: "not_found" }, null);
      return;
    }

    //console.log("updated Ptchargement: ", { id, status: 'à facturer' });
    result(null, { id, status: 'à facturer' });
  });
};

cron.schedule('*/5 * * * *', () => {
  // Exécution de la requête SQL pour mettre à jour les lignes
  const query = `
    UPDATE point_chargement pc
    JOIN commande cmd ON pc.idCommande = cmd.id
    SET pc.status = 'Valide', pc.date_validation = NOW()
    WHERE pc.status = 'en attente de confirmation' AND cmd.statut = 'valide'
  `;

  sql.query(query, (error, results, fields) => {
    if (error) throw error;
    //  //console.log('Les lignes ont été mises à jour avec succès');
  });
});

Ptchargement.findAllToInspection = (result) => {
  const query = `
  SELECT 
  pc.*, 
  c.nom AS conducteur_nom, 
  c.prenom AS conducteur_prenom, 
  c.mobile AS conducteur_mobile, 
  cm.immatriculation AS camion_immatriculation,
  f.nom_fournisseur 
FROM 
  point_chargement pc
LEFT JOIN 
  conducteur c ON pc.id_conducteur = c.id
LEFT JOIN 
  camion cm ON pc.id_camion = cm.id
LEFT JOIN 
  fournisseur f ON pc.id_client = f.id  
WHERE 
  pc.status NOT IN ('en attente de confirmation','facturé','Annulé');

  `;

  sql.query(query, (err, data) => {
    if (err) {
      console.error("Error retrieving data: ", err);
      result(err, null);
      return;
    }

    result(null, data);
  });
};

Ptchargement.updatePieceCegid = (id, piece, result) => {
  sql.query("UPDATE point_chargement SET num_piece = ? WHERE id = ?", [piece, id], (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.affectedRows === 0) {
      // Aucun point de chargement trouvé avec l'id spécifié
      result({ kind: "not_found" }, null);
      return;
    }

    //console.log("updated Ptchargement: ");
    result(null, {});
  });
};

Ptchargement.findLigneDelivredByTruck = (id_camion, date_debut, date_fin, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  // Convertir les dates en format datetime
  const dateDebutFormatted = date_debut + ' 00:00:00';
  const dateFinFormatted = date_fin + ' 23:59:59';

  // Utilisation de paramètres préparés pour éviter les injections SQL
  const query = `
    SELECT *
    FROM voyage
    WHERE date_voyage >= ?
    AND date_voyage <= ?
    AND id_camion = ?
  `;

  sql.query(query, [dateDebutFormatted, dateFinFormatted, id_camion], (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(err, null);
      return;
    }

    ////console.log("Ptchargement avec jointure fournisseur: ", res);
    result(null, res);
  });
};


Ptchargement.findLigneDelivredByDriver = (id_conducteur, date_debut, date_fin, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  // Convertir les dates en format datetime
  const dateDebutFormatted = date_debut + ' 00:00:00';
  const dateFinFormatted = date_fin + ' 23:59:59';

  // Utilisation de paramètres préparés pour éviter les injections SQL
  //voyage
  const query = `
    SELECT 
    v.*, 
    (SELECT pc.date_voyage 
     FROM point_chargement pc 
     WHERE pc.id_voyage = v.id 
     ORDER BY pc.date_voyage ASC 
     LIMIT 1) AS first_date_voyage
FROM 
    voyage v
WHERE 
    v.date_voyage >= ?
    AND v.date_voyage <= ?
    AND v.id_conducteur = ?
    AND cancelled = FALSE
  `;

  sql.query(query, [dateDebutFormatted, dateFinFormatted, id_conducteur], (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(err, null);
      return;
    }

    ////console.log("Ptchargement avec jointure fournisseur: ", res);
    result(null, res);
  });
};

// Ptchargement.findLigneDelivredByDriver = (id_conducteur, date_debut, date_fin, result) => {
//   // Vérification de la validité des entrées
//   if (!id_conducteur || !date_debut || !date_fin) {
//     console.error("Données d'entrée invalides : id_conducteur, date_debut, ou date_fin manquants.");
//     result("Erreur : Données d'entrée invalides", null);
//     return;
//   }

//   // Vérification du format des dates
//   const dateRegex = /^\d{4}-\d{2}-\d{2}$/; // Vérifie si la date est au format YYYY-MM-DD
//   if (!dateRegex.test(date_debut) || !dateRegex.test(date_fin)) {
//     console.error("Erreur : Le format des dates est incorrect. Utilisez le format YYYY-MM-DD.");
//     result("Erreur : Format de date incorrect", null);
//     return;
//   }

//   // Formatage des dates pour MySQL avec heures spécifiques (00:00:00 pour la date de début, 23:59:59 pour la date de fin)
//   const dateDebutFormatted = date_debut + ' 00:00:00';
//   const dateFinFormatted = date_fin + ' 23:59:59';

//   // Requête SQL avec paramètres préparés pour éviter les injections SQL
//   const query = `
//     SELECT *
//     FROM point_chargement
//     WHERE date_livraison >= ?
//     AND date_livraison <= ?
//     AND id_conducteur = ?
//   `;

//   // Exécution de la requête SQL
//   sql.query(query, [dateDebutFormatted, dateFinFormatted, id_conducteur], (err, res) => {
//     if (err) {
//       // Gestion des erreurs de requête SQL
//       console.error("Erreur lors de l'exécution de la requête SQL :", err);
//       result(err, null);
//       return;
//     }

//     // Si aucune ligne n'est trouvée
//     if (res.length === 0) {
//       console.log("Aucune donnée trouvée pour les critères spécifiés.");
//       result("Aucune donnée trouvée", null);
//       return;
//     }

//     // Retour des résultats
//     result(null, res);
//   });
// };




Ptchargement.findLignesByweek = (intervale, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const query = `
    SELECT 
    pc.id,
    pc.nom_depart, 
    pc.nom_arrivee, 
    pc.sku, 
    pc.status, 
    pc.date_creation, 
    pc.estimation,       
    pc.volume,  
    pc.quantite,     
    pc.ajoutee_par,  
    pc.type_ligne,        
    pc.date_voyage,
    pc.prix_tot,
    f.nom_fournisseur  
FROM 
    point_chargement pc
JOIN 
    fournisseur f ON pc.id_client = f.id  
WHERE 
    pc.date_creation >= DATE_SUB(CURDATE(), INTERVAL ? WEEK)
ORDER BY 
    pc.date_creation DESC;

  `;

  //console.log("Executing query:", query);

  sql.query(query, [intervale], (err, res) => {
    if (err) {
      console.error("Error executing query:", err);
      result(err, null);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res);
  });
};

Ptchargement.findReservedByChef = (idUser, result) => {
  // Récupérer les identifiants des utilisateurs associés au chef
  sql.query(`SELECT id_user FROM chef_user WHERE id_chef = ${idUser}`, (err, chefUserRes) => {
    if (err) {
      console.error("Error retrieving chef users:", err);
      result(err, null);
      return;
    }

    // Construire une liste d'identifiants d'utilisateurs, y compris l'identifiant du chef
    const userIds = chefUserRes.map(chefUser => chefUser.id_user).join(',') + ',' + idUser;

    // Requête SQL pour sélectionner les points de chargement réservés par les utilisateurs associés au chef
    const query = `
      SELECT pc.*, CONCAT(c.nom, ' ', c.prenom) AS conducteur_nom_prenom, c.mobile
      FROM point_chargement pc
      JOIN conducteur c ON pc.id_conducteur = c.id
      WHERE pc.status = 'Réservé' AND pc.id_demandeur IN (${userIds})
      ORDER BY pc.id DESC;
    `;

    // Exécution de la requête SQL
    sql.query(query, (err, res) => {
      if (err) {
        console.error("Error: ", err);
        result(err, null);
        return;
      }

      //console.log("Ptchargement: ", res);
      result(null, res);
    });
  });
};

Ptchargement.findExpediedByChef = (idUser, result) => {
  // Récupérer les identifiants des utilisateurs associés au chef
  sql.query(`SELECT id_user FROM chef_user WHERE id_chef = ${idUser}`, (err, chefUserRes) => {
    if (err) {
      console.error("Error retrieving chef users:", err);
      result(err, null);
      return;
    }

    // Construire une liste d'identifiants d'utilisateurs, y compris l'identifiant du chef
    const userIds = chefUserRes.map(chefUser => chefUser.id_user).join(',') + ',' + idUser;

    const query = `
      SELECT pc.*, CONCAT(c.nom, ' ', c.prenom) AS conducteur_nom_prenom, c.mobile
      FROM point_chargement pc
      JOIN conducteur c ON pc.id_conducteur = c.id
      WHERE pc.status = 'Expédier' AND pc.id_demandeur IN (${userIds})
      ORDER BY pc.id DESC;
    `;

    // Exécution de la requête SQL
    sql.query(query, (err, res) => {
      if (err) {
        console.error("Error: ", err);
        result(err, null);
        return;
      }

      //console.log("Ptchargement: ", res);
      result(null, res);
    });
  });
};

Ptchargement.findDeliveredByChef = (idUser, result) => {
  // Récupérer les identifiants des utilisateurs associés au chef
  sql.query(`SELECT id_user FROM chef_user WHERE id_chef = ${idUser}`, (err, chefUserRes) => {
    if (err) {
      console.error("Error retrieving chef users:", err);
      result(err, null);
      return;
    }

    // Construire une liste d'identifiants d'utilisateurs, y compris l'identifiant du chef
    const userIds = chefUserRes.map(chefUser => chefUser.id_user).join(',') + ',' + idUser;

    // Requête SQL pour sélectionner les points de chargement réservés par les utilisateurs associés au chef
    const query = `
      SELECT pc.*, CONCAT(c.nom, ' ', c.prenom) AS conducteur_nom_prenom, c.mobile
      FROM point_chargement pc
      JOIN conducteur c ON pc.id_conducteur = c.id
      WHERE pc.status = 'Livré' AND pc.id_demandeur IN (${userIds})
      ORDER BY pc.id DESC;
    `;

    // Exécution de la requête SQL
    sql.query(query, (err, res) => {
      if (err) {
        console.error("Error: ", err);
        result(err, null);
        return;
      }

      //console.log("Ptchargement: ", res);
      result(null, res);
    });
  });
};


Ptchargement.findRetard = (date_debut, date_fin, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  // Convertir les dates JavaScript en chaînes de caractères au format YYYY-MM-DD HH:mm:ss
  let date_debut_query = date_debut ? `${date_debut.getFullYear()}-${(date_debut.getMonth() + 1).toString().padStart(2, '0')}-${date_debut.getDate().toString().padStart(2, '0')} 00:00:00` : `${new Date().getFullYear()}-01-01 00:00:00`;
  let date_fin_query = date_fin ? `${date_fin.getFullYear()}-${(date_fin.getMonth() + 1).toString().padStart(2, '0')}-${date_fin.getDate().toString().padStart(2, '0')} 23:59:59` : `${new Date().getFullYear()}-12-31 23:59:59`;

  const query = `
      SELECT 
          (
              SELECT 
                  SEC_TO_TIME(
                      SUM(
                          IF(
                              UNIX_TIMESTAMP(
                                  COALESCE(date_reservation, NOW())
                              ) - UNIX_TIMESTAMP(date_validation) > 0,
                              UNIX_TIMESTAMP(
                                  COALESCE(date_reservation, NOW())
                              ) - UNIX_TIMESTAMP(date_validation),
                              0
                          )
                      ) / COUNT(*)
                  ) AS retard_moyen_reservation
              FROM 
                  point_chargement
              WHERE 
                  (date_creation BETWEEN ? AND ?) AND
                  status NOT IN ('en attente de confirmation')
          ) AS retard_moyen_reservation,
          (
              SELECT 
                  SEC_TO_TIME(
                      SUM(
                          IF(
                              UNIX_TIMESTAMP(
                                  IFNULL(date_expedition, NOW())
                              ) - UNIX_TIMESTAMP(
                                  IFNULL(date_reservation, date_validation)
                              ) > 0,
                              UNIX_TIMESTAMP(
                                  IFNULL(date_expedition, NOW())
                              ) - UNIX_TIMESTAMP(
                                  IFNULL(date_reservation, date_validation)
                              ),
                              0
                          )
                      ) / COUNT(*)
                  ) AS retard_moyen_expedition
              FROM 
                  point_chargement
              WHERE 
                  (date_creation BETWEEN ? AND ?) AND
                  status NOT IN ('en attente de confirmation')
          ) AS retard_moyen_expedition,
          (
              SELECT 
                  SEC_TO_TIME(
                      SUM(
                          GREATEST(
                              UNIX_TIMESTAMP(IFNULL(date_livraison, NOW())) - UNIX_TIMESTAMP(IFNULL(date_expedition, IFNULL(date_reservation, date_validation))),
                              0
                          )
                      ) / COUNT(*)
                  ) AS retard_moyen_livraison
              FROM 
                  point_chargement
              WHERE 
                  (date_creation BETWEEN ? AND ?) AND
                  status NOT IN ('en attente de confirmation')
          ) AS retard_moyen_livraison
  `;

  // Passer les dates de début et de fin à la méthode query
  sql.query(query, [date_debut_query, date_fin_query, date_debut_query, date_fin_query, date_debut_query, date_fin_query], (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(err, null);
      return;
    }

    //console.log("Retards moyens: ", res);
    result(null, res);
  });
};

Ptchargement.getProductivity = (date_debut, date_fin, result) => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  const date_debut1 = new Date(date_debut);
  const date_fin1 = new Date(date_fin);
  date_debut1.setHours(0, 0, 0, 0);
  date_fin1.setHours(23, 59, 59, 999);
  const query = `
  SELECT
      SUM(CASE WHEN DATE(date_livraison) = DATE(date_arrivee) THEN 1 ELSE 0 END) AS nombre_livraison_a_lheure,
      SUM(CASE WHEN DATE(date_livraison) > DATE(date_arrivee) THEN 1 ELSE 0 END) AS nombre_livraison_en_retard,
      SUM(CASE WHEN DATE(date_livraison) < DATE(date_arrivee) THEN 1 ELSE 0 END) AS nombre_livraison_en_avance,
      COUNT(*) AS nombre_total_commandes_Livre,
      (SUM(CASE WHEN DATE(date_livraison) <= DATE(date_arrivee) THEN 1 ELSE 0 END) / COUNT(*)) * 100 AS pourcentage_commandes_reussies,
      AVG(CASE WHEN DATE(date_livraison) < DATE(date_arrivee) THEN TIMESTAMPDIFF(DAY, date_livraison, date_arrivee) END) AS moyenne_jours_en_avance,
      AVG(CASE WHEN DATE(date_livraison) > DATE(date_arrivee) THEN TIMESTAMPDIFF(DAY, date_arrivee, date_livraison) END) AS moyenne_jours_en_retard
  FROM
      point_chargement
  WHERE
      status IN ('Livré', 'à facturer', 'Facturé') AND
      date_livraison BETWEEN ? AND ?;
  `;

  //console.log("Requête SQL avant exécution :", query);
  //console.log("Paramètres de la requête :", [date_debut1, date_fin1]);

  sql.query(query, [date_debut1, date_fin1], (err, res) => {
    if (err) {
      console.error("Erreur lors de l'exécution de la requête :", err);
      result(err, null);
      return;
    }

    //console.log("Requête SQL après exécution :", query);
    //console.log("Ptchargement avec jointure fournisseur: ", res);
    result(null, res);
  });
};

Ptchargement.MoyVolumePerVoyage = (date_debut, date_fin, result) => {
  const date_debut1 = new Date(date_debut);
  const date_fin1 = new Date(date_fin);
  date_debut1.setHours(0, 0, 0, 0);
  date_fin1.setHours(23, 59, 59, 999);

  sql.query(`SELECT 
  SUM(volume) AS somme_volume,
  COUNT(DISTINCT CONCAT(id_conducteur, id_camion, date_expedition)) AS nbr_voyage 
FROM 
  point_chargement 
WHERE  
status IN ('Livré', 'à facturer', 'Facturé') 
AND type_ligne NOT IN ('Transfert administratif technique et matériel informatique', 'Transfert administratif ZEN Group-Magasin ou dépôt','Livraison fourniture')
  AND EXISTS (
      SELECT 1
      FROM point_chargement pc2
      WHERE pc2.id <> point_chargement.id -- Exclure l'enregistrement en cours

      AND (
          pc2.date_expedition < DATE_SUB(point_chargement.date_expedition, INTERVAL 5 MINUTE) OR
          pc2.date_expedition > DATE_ADD(point_chargement.date_expedition, INTERVAL 5 MINUTE)
      )
  )
  AND date_expedition BETWEEN ? AND ?;`, [date_debut1, date_fin1],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      //console.log("Ptchargement: ", res);
      if (res[0].somme_volume == null) {
        res[0].somme_volume = 0
      }
      // Calcul de la moyenne du volume par voyage
      const moyenne_volume = (res[0].somme_volume / res[0].nbr_voyage).toFixed(2);

      // Envoi des résultats à la fonction de rappel
      result(null, {
        somme_volume: isNaN(res[0].somme_volume) ? 0 : res[0].somme_volume,
        nbr_voyage: res[0].nbr_voyage || 0,
        moyenne_volume: isNaN(moyenne_volume) ? 0 : moyenne_volume
      });
    });
};

Ptchargement.voyagePerDestination = (date_debut, date_fin, id_destination, result) => {
  const date_debut1 = new Date(date_debut);
  const date_fin1 = new Date(date_fin);
  date_debut1.setHours(0, 0, 0, 0);
  date_fin1.setHours(23, 59, 59, 999);

  sql.query(`
    SELECT 
      COUNT(DISTINCT from_destination, to_destination, date_expedition) AS nombre_lignes,
      SUM(volume) AS somme_volume,
      COUNT(DISTINCT CONCAT(id_conducteur, id_camion, date_expedition)) AS nbr_voyage
    FROM point_chargement 
    WHERE 
      to_destination = ? 
      AND status IN ('Livré', 'à facturer', 'Facturé') 
      AND type_ligne NOT IN ('Transfert administratif technique et matériel informatique', 'Transfert administratif ZEN Group-Magasin ou dépôt','Livraison fourniture') 
      AND date_expedition BETWEEN ? AND ?;
  `, [id_destination, date_debut1, date_fin1],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      //console.log("Ptchargement: ", res);

      // Calcul de la moyenne du volume par voyage
      let moyenne_volume;
      if (res[0].nbr_voyage !== 0) {
        moyenne_volume = (res[0].somme_volume / res[0].nbr_voyage).toFixed(2);
      } else {
        // Attribuer une valeur par défaut si nbr_voyage est égal à zéro
        moyenne_volume = 0;
      }

      // Envoi des résultats à la fonction de rappel
      result(null, {
        somme_volume: res[0].somme_volume !== null ? res[0].somme_volume : 0,
        nbr_voyage: res[0].nbr_voyage !== null ? res[0].nbr_voyage : 0,
      });


    });
};

Ptchargement.getAllVoyage = (date_debut, date_fin, result) => {
  const date_debut1 = new Date(date_debut);
  const date_fin1 = new Date(date_fin);
  date_debut1.setHours(0, 0, 0, 0);
  date_fin1.setHours(23, 59, 59, 999);

  sql.query(`SELECT COUNT(DISTINCT from_destination, to_destination, id_conducteur, id_camion, date_expedition) AS nombre_lignes
  FROM point_chargement where AND type_ligne NOT IN ('Transfert administratif technique et matériel informatique', 'Transfert administratif ZEN Group-Magasin ou dépôt','Livraison fourniture') AND DATE(date_creation) BETWEEN ? AND ?;
  ;`, [date_debut1, date_fin1],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      //console.log("Ptchargement: ", res);

      // Calcul de la moyenne du volume par voyage

      // Envoi des résultats à la fonction de rappel
      result(null, { somme_volume: res[0].somme_volume, nbr_voyage: res[0].nbr_voyage });
    });
};

Ptchargement.getIndicateurByConducteur = (date_debut, date_fin, id_conducteur, id_destination, result) => {
  const date_debut1 = new Date(date_debut);
  const date_fin1 = new Date(date_fin);
  date_debut1.setHours(0, 0, 0, 0);
  date_fin1.setHours(23, 59, 59, 999);

  let sqlQuery;
  let queryParams;

  if (!id_conducteur && id_destination) {
    sqlQuery = `
    SELECT 
      COUNT(DISTINCT CONCAT(id_conducteur, id_camion, date_expedition)) AS nbr_voyage,
      SUM(volume) AS somme_volume
    FROM 
      point_chargement 
    WHERE 
      AND to_destination = ?
      AND status IN ('Livré', 'à facturer', 'Facturé')
      AND type_ligne NOT IN ('Transfert administratif technique et matériel informatique', 'Transfert administratif ZEN Group-Magasin ou dépôt', 'Livraison fourniture') 
      AND DATE(date_creation) BETWEEN ? AND ?;
  `;
    queryParams = [id_destination, date_debut1, date_fin1];
  } else

    if (id_destination && id_conducteur) {
      // Si l'ID de la destination est spécifié, filtrer par celui-ci
      sqlQuery = `
      SELECT 
        COUNT(DISTINCT CONCAT(id_conducteur, id_camion, date_expedition)) AS nbr_voyage,
        SUM(volume) AS somme_volume
      FROM 
        point_chargement 
      WHERE 
        id_conducteur = ? 
        AND to_destination = ?
        AND status IN ('Livré', 'à facturer', 'Facturé')
        AND type_ligne NOT IN ('Transfert administratif technique et matériel informatique', 'Transfert administratif ZEN Group-Magasin ou dépôt', 'Livraison fourniture') 
        AND DATE(date_creation) BETWEEN ? AND ?;
    `;
      queryParams = [id_conducteur, id_destination, date_debut1, date_fin1];
    } else {
      // Sinon, obtenir toutes les données sans filtrer par destination
      sqlQuery = `
      SELECT 
        COUNT(DISTINCT CONCAT(id_conducteur, id_camion, date_expedition)) AS nbr_voyage,
        SUM(volume) AS somme_volume
      FROM 
        point_chargement 
      WHERE 
        id_conducteur = ? 
        AND status IN ('Livré', 'à facturer', 'Facturé')
        AND type_ligne NOT IN ('Transfert administratif technique et matériel informatique', 'Transfert administratif ZEN Group-Magasin ou dépôt', 'Livraison fourniture') 
        AND DATE(date_creation) BETWEEN ? AND ?;
    `;
      queryParams = [id_conducteur, date_debut1, date_fin1];
    }

  sql.query(sqlQuery, queryParams,
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      //console.log("Ptchargement: ", res);

      // Envoi des résultats à la fonction de rappel
      result(null, {
        somme_volume: res[0].somme_volume || 0,
        nbr_voyage: res[0].nbr_voyage || 0
      });
    });
};

Ptchargement.updateStatusNoPickup = (id, result) => {
  sql.query(
    "UPDATE point_chargement SET status = 'Livré', no_pick = true WHERE id = ?",
    [id],
    (updateErr, updateRes) => {
      if (updateErr) {
        // Gérer une erreur SQL
        console.error("Erreur lors de la mise à jour du point de chargement:", updateErr);
        result(updateErr, null);
        return;
      }

      if (updateRes.affectedRows === 0) {
        // Aucun enregistrement mis à jour, l'ID n'existe pas
        result({ kind: "not_found" }, null);
        return;
      }

      // Succès : renvoyer l'ID et le nouveau statut
      result(null, { id, status: 'Livré', no_pick: true });
    }
  );
};




Ptchargement.getLigneByDestinationAndUser = (id_user, result) => {
  sql.query("SELECT id_destination FROM user_destination WHERE id_user = ?", [id_user], (err, destinations) => {
    if (err) {
      result(err, null);
      return;
    }

    const destinationIds = destinations.map(destination => destination.id_destination);

    if (destinationIds.length === 0) {
      result(null, []);
      return;
    }

    const query = `
    SELECT 
      pc.*, 
      CONCAT(c.nom, ' ', c.prenom) AS nom_conducteur, 
      c.mobile,
      p.id AS package_id,
      p.barcode,
      p.departure,
      p.arrival,
      p.created_at AS package_created_at,
      p.fk_id_ligne
    FROM point_chargement pc
    LEFT JOIN package p ON pc.id = p.fk_id_ligne
    LEFT JOIN conducteur c ON pc.id_conducteur = c.id
    WHERE pc.status IN ('Valide', 'Réservé','Expédier')
      AND pc.from_destination IN (?)
  `;
  

    sql.query(query, [destinationIds], (err, rows) => {
      if (err) {
        result(err, null);
        return;
      }

      const lignes = {};

      rows.forEach(row => {
        if (!lignes[row.id]) {
          // Clone tout l'objet point_chargement tel quel
          lignes[row.id] = {
            ...row,
            packages: []
          };
          // Supprime les champs du package de la racine (optionnel mais propre)
          delete lignes[row.id].package_id;
          delete lignes[row.id].barcode;
          delete lignes[row.id].departure;
          delete lignes[row.id].arrival;
          delete lignes[row.id].fk_id_ligne;
          delete lignes[row.id].package_created_at;
        }

        if (row.package_id) {
          lignes[row.id].packages.push({
            id: row.package_id,
            barcode: row.barcode,
            departure: row.departure,
            arrival: row.arrival,
            fk_id_ligne: row.fk_id_ligne,
            created_at: row.package_created_at
          });
        }
      });

      result(null, Object.values(lignes));
    });
  });
};



Ptchargement.getLigneByDestinationAndUserLiv = (id_user, result) => {
  // Récupérer les identifiants des destinations pour l'utilisateur donné
  sql.query("SELECT id_destination FROM user_destination WHERE id_user = ?", [id_user], (err, destinations) => {
    if (err) {
      result(err, null);
      return;
    }

    // Convertir les résultats en tableau d'identifiants de destination
    const destinationIds = destinations.map(destination => destination.id_destination);

    // Vérifier si destinationIds est vide
    if (destinationIds.length === 0) {
      result(null, []); // Pas de destinations trouvées
      return;
    }

    // Récupérer les lignes de point_chargement correspondant aux destinations récupérées
    sql.query(
      `SELECT p.*, CONCAT(c.nom, ' ', c.prenom) AS nom_conducteur, c.mobile
       FROM point_chargement p
       JOIN conducteur c ON p.id_conducteur = c.id
       WHERE p.status IN ('Valide', 'Réservé','Expédier') AND p.to_destination IN (?)`,
      [destinationIds],
      (err, res) => {
        if (err) {
          result(err, null);
          return;
        }
        result(null, res);
      }
    );
    
    
  });
};

Ptchargement.findAllRetourAndInter = result => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  sql.query("SELECT * FROM point_chargement WHERE type_ligne IN ('Transfert PF inter magasin', 'Retour articles') AND status IN ('Valide', 'Réservé')", (err, res) => {
    if (err) {
      console.error("Error: ", err);
      result(err, null);
      return;
    }

    //console.log("Ptchargement: ", res);
    result(null, res);
  });
};

Ptchargement.updateVolumeXls = (data, callback) => {
  // Counter to track completion of all updates
  let completed = 0;
  let hasError = false;

  for (let i = 0; i < data.length; i++) {
    const { id, volume } = data[i];

    // Update the point_chargement if status is not 'Facturé', 'Ajusté', or 'à Facturé'
    sql.query(
      "UPDATE point_chargement SET volume = ?, pdf_update = TRUE WHERE id = ? AND status NOT IN ('Facturé', 'Ajusté', 'à Facturé')",
      [volume, id],
      (updateErr, updateRes) => {
        if (updateErr) {
          if (!hasError) {
            hasError = true;
            callback(updateErr, null); // Call callback immediately on error
          }
          return;
        }

        completed++;

        // Check if all updates are completed
        if (completed === data.length && !hasError) {
          callback(null, "Mise à jour du volume réussie pour toutes les lignes.");
        }
      }
    );
  }
};
Ptchargement.pdfNotUpdated = result => {
  if (typeof result !== 'function') {
    console.error("Result is not a function");
    return;
  }

  // Requête SQL ajustée
  sql.query(
    "SELECT * FROM point_chargement WHERE pdf_update = false AND volume = 0 AND type_ligne IN ('Livraison PF (magasin)', 'Retour articles', 'Transfert PF inter magasin') AND status NOT IN ('annulé', 'en attente de confirmation', 'Valide','à facturer','facturé') AND nom_arrivee != 'Dépôt Aramex'",
    (err, res) => {
      if (err) {
        console.error("Error: ", err);
        result(err, null);
        return;
      }

      //console.log("Ptchargement: ", res);
      result(null, res);
    }
  );
};

// Ptchargement.updateExpedition = (id, kilometrage, volume, type_ligne, quantite, result) => {
//   //console.log("///////////////////", id); // Affiche l'ID dans la console pour le suivi

//   const sqlJoin = `
//     SELECT p.id_demandeur, u.type_utilisateur 
//     FROM point_chargement p 
//     JOIN utilisateurs u ON p.id_demandeur = u.id 
//     WHERE p.id = ?
//   `;
//   sql.query(sqlJoin, [id], (selectErr, selectResult) => {
//     if (selectErr) {
//       //console.log("Erreur lors de la sélection depuis la table point_chargement :", selectErr);
//       result(selectErr, null);
//       return;
//     }
//     if (selectResult.length === 0) {
//       result({ kind: "not_found" }, null);
//       return;
//     }

//     const firstResult = selectResult[0];
//     const typeUtilisateur = firstResult.type_utilisateur;
//     //console.log("*************------------------", typeUtilisateur);
//     if (typeUtilisateur === 'GE') {
//       type_ligne = "Transfert PF inter magasin";
//     }

//     sql.query("SELECT * FROM prices WHERE type = ?", [type_ligne], (selectErr, selectRes) => {
//       if (selectErr) {
//         //console.log("Erreur lors de la sélection depuis la table prices :", selectErr);
//         result(selectErr, null);
//         return;
//       }
//       const priceRow = selectRes[0];
//      // const { PU, PrixTot } = calculatePrice(type_ligne, kilometrage, volume, quantite, priceRow);

//       sql.query(
//         "UPDATE point_chargement SET status = 'Expédier', date_expedition = NOW() WHERE id = ?",
//         [PU, PrixTot, id],
//         (updateErr, updateRes) => {
//           if (updateErr) {
//             //console.log("Erreur lors de la mise à jour du point de chargement :", updateErr);
//             result(updateErr, null);
//             return;
//           }
//           if (updateRes.affectedRows === 0) {
//             result({ kind: "not_found" }, null);
//             return;
//           }
//           //console.log("Point de chargement mis à jour :", { id, date_expedition: new Date(), priceRow });
//           result(null, { id, date_expedition: new Date(), priceRow });
//         }
//       );
//     });
//   });
// };

// Ptchargement.updateStatusLivred = (id, result) => {
//   const sqlJoin = `
//     SELECT p.id_demandeur, u.type_utilisateur, p.kilometrage, p.volume, p.type_ligne, p.quantite 
//     FROM point_chargement p 
//     JOIN utilisateurs u ON p.id_demandeur = u.id 
//     WHERE p.id = ?
//   `;
//   sql.query(sqlJoin, [id], (selectErr, selectResult) => {
//     if (selectErr) {
//       //console.log("Erreur lors de la sélection depuis la table point_chargement :", selectErr);
//       result(selectErr, null);
//       return;
//     }
//     if (selectResult.length === 0) {
//       result({ kind: "not_found" }, null);
//       return;
//     }

//     const firstResult = selectResult[0];
//     const { type_ligne, kilometrage, volume, quantite } = firstResult;

//     sql.query("SELECT * FROM prices WHERE type = ?", [type_ligne], (selectErr, selectRes) => {
//       if (selectErr) {
//         //console.log("Erreur lors de la sélection depuis la table prices :", selectErr);
//         result(selectErr, null);
//         return;
//       }
//       const priceRow = selectRes[0];
//       const { PU, PrixTot } = calculatePrice(type_ligne, kilometrage, volume, quantite, priceRow);

//       sql.query(
//         "UPDATE point_chargement SET status = 'Livré', date_livraison = NOW() WHERE id = ?",
//         [PU, PrixTot, id],
//         (updateErr, updateRes) => {
//           if (updateErr) {
//             //console.log("Erreur lors de la mise à jour du point de chargement :", updateErr);
//             result(updateErr, null);
//             return;
//           }
//           if (updateRes.affectedRows === 0) {
//             result({ kind: "not_found" }, null);
//             return;
//           }
//           //console.log("Point de chargement mis à jour :", { id, status: 'Livré', date_livraison: new Date() });
//           result(null, { id, status: 'Livré', date_livraison: new Date() });
//         }
//       );
//     });
//   });
// };


Ptchargement.updateExpedition = (id) => {
  return new Promise((resolve, reject) => {
    if (!id) {
      reject(new Error("ID manquant")); // Vérification de l'ID
      return;
    }

    sql.query(
      "UPDATE point_chargement SET status = 'Expédier', date_expedition = NOW() WHERE id = ?",
      [id],
      (updateErr, updateRes) => {
        if (updateErr) {
          console.error("Erreur SQL :", updateErr);  // Affichage détaillé des erreurs SQL
          reject(new Error("Erreur lors de la mise à jour du point de chargement"));
          return;
        }
        if (updateRes.affectedRows === 0) {
          reject({ kind: "not_found", message: "Aucun enregistrement trouvé avec cet ID" });
          return;
        }
        console.log("Mise à jour réussie :", { id, date_expedition: new Date() });
        resolve({ id, date_expedition: new Date() });  // Retourne les détails mis à jour
      }
    );
  });
};



Ptchargement.updateStatusLivred = (id) => {
  return new Promise((resolve, reject) => {
    sql.query(
      "UPDATE point_chargement SET status = 'Livré', date_livraison = NOW() WHERE id = ?",
      [id],
      (updateErr, updateRes) => {
        if (updateErr) {
          console.log("Erreur lors de la mise à jour du point de chargement :", updateErr);
          reject(updateErr);
          return;
        }

        if (updateRes.affectedRows === 0) {
          reject({ kind: "not_found" });
          return;
        }

        // Vous pouvez récupérer la date_livraison de la réponse de la requête
        console.log("Point de chargement mis à jour :", { id, date_livraison: new Date() });
        
        // Résoudre avec la réponse complète
        resolve({ id, date_livraison: new Date() });
      }
    );
  });
};

Ptchargement.findLignesByIdVoyage = (idVoyage, result) => {
  // Vérifier que `result` est une fonction
  if (typeof result !== 'function') {
    console.error("Le paramètre 'result' doit être une fonction.");
    return;
  }

  const query = `
    SELECT 
      pc.id,
      pc.date_voyage,
      pc.date_livraison,
      pc.date_expedition,
      pc.nom_depart,
      pc.nom_arrivee,
      pc.type_ligne,
      pc.kilometrage,
      pc.prix_tot
    FROM 
      point_chargement AS pc
    WHERE 
      pc.id_voyage = ?
  `;

  sql.query(query, [idVoyage], (err, res) => {
    if (err) {
      console.error("Erreur lors de l'exécution de la requête SQL :", err);
      result(null, err); // Retourner l'erreur
      return;
    }

    if (res.length === 0) {
      console.log("Aucune ligne trouvée pour l'id du voyage :", idVoyage);
      result(null, []); 
      return;
    }

    // Retourner les résultats
    console.log("Lignes trouvées :", res);
    result(null, res);
  });
};





const executeTask = () => {
  console.log("Running cron job to check point_chargement");

  // Requête SQL pour récupérer les commandes manquantes
  sql.query(
    "SELECT * FROM point_chargement WHERE pdf_update = false AND volume = 0 AND type_ligne IN ('Livraison PF (magasin)', 'Retour articles', 'Transfert PF inter magasin') AND status NOT IN ('annulé', 'en attente de confirmation', 'Valide','à facturer','facturé') AND nom_arrivee != 'Dépôt Aramex'",
    (err, res) => {
      if (err) {
        console.error("Error fetching point_chargement: ", err);
        return;
      }
      if (res.length > 0) {
        console.log("Found rows with missing volume, notifying users...");

        sql.query("SELECT id FROM utilisateurs WHERE type_utilisateur IN ('Administrateur', 'SuperAdmin', 'Depot')", (err, users) => {
          if (err) {
            console.error("Error fetching users: ", err);
            return;
          }

          users.forEach(user => {
            const message = "Consulter la liste des commandes sans liste de colisage";
            notifyUser(user.id,"pages/pdfNotUploaded", message);
          });
        });
      } else {
        console.log("No rows found with missing volume.");
      }
    }
  );
};


Ptchargement.updateStatusReady = (id, status, result) => {
  console.log(status)
  sql.query(
    "UPDATE point_chargement SET ready = ? WHERE id = ?", 
    [status, id], 
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      if (res.affectedRows === 0) {
        result({ kind: "not_found" }, null);
        return;
      }

      result(null, { id, status, date_validation: new Date() });
    }
  );
};
Ptchargement.findByDestinationsAndDateVoyage = (data, result) => {
  const sqlQuery = `
    SELECT * 
    FROM point_chargement 
    WHERE (type_ligne = 'livraison PF (magasin)' OR type_ligne = 'Retour articles')
      AND DATE(date_voyage) = ?
      AND LOWER(nom_depart) LIKE LOWER(?)
      AND LOWER(nom_arrivee) LIKE LOWER(?)
      AND pdf_update = FALSE
  `;

  const formattedDate = data.date; 
  const depotEmetteur = data.depotEmetteur;
  const depotRecepteur = data.depotRecepteur;
  sql.query(sqlQuery, [formattedDate, `%${depotEmetteur}%`, `%${depotRecepteur}%`], (err, res) => {
    if (err) {
      console.error("SQL Error:", err);
      result(err, null);
      return;
    }

    console.log("Query Results:", res);
    result(null, res);
  });
};

Ptchargement.updateVolumeColisage = (data, callback) => {
  // Parcourir la liste data et mettre à jour chaque point de chargement
  for (let i = 0; i < data.length; i++) {
    const { id, volume } = data[i];

    // Sélectionner les données du point de chargement pour vérifier son existence et son statut
    sql.query("SELECT * FROM point_chargement WHERE id = ? AND status NOT IN ('Facturé', 'Ajusté','à facturer','Valid')", [id], (pointChargementErr, pointChargementRes) => {
      if (pointChargementErr) {
        console.log("Erreur lors de la sélection du point de chargement avec l'ID", id, ":", pointChargementErr);
        callback(pointChargementErr, null);
        return;
      }

      if (pointChargementRes[0] === undefined) {
        console.log("Point de chargement non trouvé ou déjà facturé pour l'ID", id);
        return;
      }

      // Mise à jour uniquement du volume
      sql.query("UPDATE point_chargement SET volume = ?, pdf_update = TRUE WHERE id = ?", [volume, id], (updateErr, updateRes) => {
        if (updateErr) {
          console.log("Erreur lors de la mise à jour du volume pour l'ID", id, ":", updateErr);
          if (i === data.length - 1) {
            callback(updateErr, null); // Appeler le callback avec l'erreur si c'est la dernière itération
          }
          return;
        }

        if (updateRes.affectedRows === 0) {
          console.log("Aucune ligne point_chargement trouvée avec l'ID", id);
        } else {
          console.log("Volume mis à jour avec succès pour l'ID", id);
        }

        // Si c'est la dernière itération, appeler le callback avec succès
        if (i === data.length - 1) {
          callback(null, "Mise à jour du volume réussie"); 
        }
      });
    });
  }
};



Ptchargement.getLigneWithPackageById = (id_ligne, result) => {
  const query = `
    SELECT 
      pc.*,
      p.id AS package_id,
      p.barcode,
      p.departure,
      p.arrival,
      p.created_at AS package_created_at,
      p.fk_id_ligne
    FROM point_chargement pc
    LEFT JOIN package p ON pc.id = p.fk_id_ligne
    WHERE pc.id = ?
  `;

  sql.query(query, [id_ligne], (err, rows) => {
    if (err) {
      result(err, null);
      return;
    }

    if (rows.length === 0) {
      result(null, null);
      return;
    }

    const ligne = {
      id: rows[0].id,
      from_destination: rows[0].from_destination,
      to_destination: rows[0].to_destination,
      status: rows[0].status,
      created_at: rows[0].created_at,
      // Ajoute ici d'autres champs de `point_chargement` si besoin
      packages: []
    };

    rows.forEach(row => {
      if (row.package_id) {
        ligne.packages.push({
          id: row.package_id,
          barcode: row.barcode,
          departure: row.departure,
          arrival: row.arrival,
          fk_id_ligne: row.fk_id_ligne,
          created_at: row.package_created_at
        });
      }
    });

    result(null, ligne);
  });
};

Ptchargement.updateVoyageToAdjust = (id_voyage, result) => {
  sql.query(
    "SELECT * FROM point_chargement WHERE id_voyage = ?",
    [id_voyage],
    (err, rows) => {
      if (err) {
        return result({ code: 500, message: "Erreur serveur lors du SELECT." }, null);
      }

      const hasFacture = rows.some(row => row.status === 'facturé');

      if (hasFacture) {
        return result({ code: 400, message: "Certaines lignes sont déjà facturées. Aucune mise à jour effectuée." }, null);
      }

      sql.query(
        "UPDATE point_chargement SET status = 'Ajusté' WHERE id_voyage = ? AND (status = 'Ajusté' OR status = 'À facturer')",
        [id_voyage],
        (updateErr, updateRes) => {
          if (updateErr) {
            return result({ code: 500, message: "Erreur serveur lors de l'UPDATE." }, null);
          }

          result(null, { message: "Mise à jour réussie", affectedRows: updateRes.affectedRows });
        }
      );
    }
  );
};




// const CancelReservationAuto = (callback) => {
//   const querry = `
//       UPDATE point_chargement 
//       SET status = 'Valide' 
//       WHERE id NOT IN (
//           SELECT id FROM (
//               SELECT id FROM point_chargement 
//               WHERE NOW() > date_reservation + INTERVAL 4 HOUR 
//               AND status = 'Réservé' 
//               AND driver_confirm = FALSE
//               ORDER BY date_reservation ASC 
//               LIMIT 1
//           ) AS subquery
//       )
//       AND NOW() > date_reservation + INTERVAL 4 HOUR 
//       AND status = 'Réservé' 
//       AND driver_confirm = FALSE;
//   `;

//   sql.query(querry, (error, result) => {
//       if (error) {
//           console.error("❌ Erreur lors de la mise à jour :", error);
//           return callback(error, null);
//       }

//       console.log(`✅ Mise à jour réussie : ${result.affectedRows} lignes modifiées.`);

//       if (result.affectedRows > 0) {
//           Notification.createNotificationForAdmins(
//               "Des commandes ont été annulées automatiquement suite à l'indisponibilité du conducteur. Merci de les re-réserver.",
//               "pages/listcommande"
//           );
//           console.log("📢 Notification envoyée aux administrateurs.");
//       } else {
//           console.log("ℹ️ Aucune ligne mise à jour, pas de notification envoyée.");
//       }

//   });
// };




//cron.schedule('0 9-18 * * *', CancelReservationAuto);

// Planifier le cron aux heures spécifiques : 7:45, 9:00, 12:00, 14:00, et 16:00
cron.schedule('45 7 * * *', executeTask);
cron.schedule('45 9 * * *', executeTask);
cron.schedule('10 12 * * *', executeTask);
cron.schedule('15 14 * * *', executeTask);
cron.schedule('20 16 * * *', executeTask);






module.exports = Ptchargement;