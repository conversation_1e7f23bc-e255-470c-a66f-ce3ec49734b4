const mailModel = require("../models/mail.model.js");
const ligne = require("../controllers/ptchargement.controllers.js");
const axios = require("axios");
const nodemailer = require('nodemailer');
const https = require('https'); // Ajoutez cette ligne
const cron = require('node-cron');
const JsBarcode = require("jsbarcode");
const { createCanvas } = require("canvas");
const QRCode = require("qrcode");

const sendEmail = async (emailData) => {
  try {
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: process.env.SMTP_SECURE,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
      tls: {
        rejectUnauthorized: false, 
      },
    });

    // Envoyez l'email
    let info = await transporter.sendMail({
      from: `${process.env.NAME} <${process.env.EMAIL}>`,
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.htmlContent,
    });

    console.log('Email sent: %s', info.messageId);
    return { message: "Email sent successfully", messageId: info.messageId };

  } catch (error) {
    console.error('Error sending email: ', error);
    throw new Error(error.message || "Email sending failed");
  }
};

// const sendEmail = async (emailData) => {
//   try {
//     const transporter = nodemailer.createTransport({
//       host: "*************",
//       port: 7525,
//       secure: false,

//       tls: {
//         rejectUnauthorized: false, 
//       },
//     });

//     // Envoyez l'email
//     let info = await transporter.sendMail({
//       from: `${process.env.NAME} <${process.env.EMAIL}>`,
//       to: emailData.to,
//       subject: emailData.subject,
//       html: emailData.htmlContent,
//     });

//     console.log('Email sent: %s', info.messageId);
//     return { message: "Email sent successfully", messageId: info.messageId };

//   } catch (error) {
//     console.error('Error sending email: ', error);
//     throw new Error(error.message || "Email sending failed");
//   }
// };


// const sendEmail = async (emailData) => {
//   console.log("Sending email with data:", emailData);
//   try {
//     const response = await axios.post('https://api.sendinblue.com/v3/smtp/email', {
//       sender: {
//         name: process.env.NAME,
//         email: process.env.EMAIL,
//       },
//       to: [{
//         email: emailData.to,
//         name: emailData.toName || '', // Optional: If you have a name for the recipient
//       }],
//       subject: emailData.subject,
//       htmlContent: emailData.htmlContent,
//     }, {
//       headers: {
//         'Content-Type': 'application/json',
//         'api-key': process.env.SENDINBLUE_API_KEY, // Your Sendinblue API key
//       },
//     });

//     console.log('Email sent: %s', response.data.messageId);
//     return { message: "Email sent successfully", messageId: response.data.messageId };

//   } catch (error) {
//     console.error('Error sending email: ', error.response ? error.response.data : error.message);
//     throw new Error(error.response ? error.response.data.message : "Email sending failed");
//   }
// };



exports.mailExpedition = async (req, res) => {
  try {
      // Extract data from the request body
      const { nom_locale, Destination, Date_voyage, Types_ligne, Volumes, qte, heure_debut, heure_fin, conducteur, phone } = req.body;

      const travelDate = new Date(Date_voyage);
      travelDate.setUTCHours(0, 0, 1, 0); // Set UTC hours, minutes, seconds, and milliseconds to 0
      const today = new Date();
      today.setUTCHours(0, 0, 1, 0);

      if (travelDate >= today) {
          // Prepare the table HTML content
          let tableHtml = `
              <table>
                  <tr>
                      <th>Type Livraison</th>
                      <th>Volume (m³)</th>
                      <th>Quantité</th>
                  </tr>
          `;
          
          Types_ligne.forEach((type, index) => {
              tableHtml += `
                  <tr>
                      <td>${type}</td>
                      <td>${Volumes[index]}</td>
                      <td>${qte[index]}</td>
                  </tr>
              `;
          });

          tableHtml += `</table>`;

          // Prepare email data
          const emailData = {
              to: Destination, // Recipient email
              subject: "Livraison", // Email subject
              htmlContent: `
              <!DOCTYPE html>
              <html lang="fr">
              <head>
                  <meta charset="UTF-8">
                  <meta http-equiv="X-UA-Compatible" content="IE=edge">
                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                  <title>Notification de livraison</title>
                  <style>
                      table {
                          border-collapse: collapse;
                          width: 100%;
                      }
                      th, td {
                          border: 1px solid black;
                          padding: 8px;
                          text-align: left;
                      }
                  </style>
              </head>
              <body>
                  <p>Bonjour ${nom_locale},</p>
                  <p>Vous avez une livraison prévue pour le ${Date_voyage} affecter à Mr. ${conducteur} avec le numéro de téléphone : ${phone}.</p>
                  <p>Les détails de la livraison sont les suivants :</p>
                  ${tableHtml}
                  <p>Bonne réception.</p>
                  <p>Cordialement,</p>
              </body>
              </html>
              `,
          };

          // Call the generic sendEmail function
          const response = await sendEmail(emailData);

          // Send response to the client
          res.send(response);
      } else {
          res.status(400).send({ message: "Travel date is in the past. No email sent." });
      }

  } catch (error) {
      console.error("Error while sending email: ", error);
      res.status(500).send({ message: error.message || "Internal Server Error" });
  }
};

exports.mailLivraison = async (req, res) => {
  try {
      // Extraction des données de la requête
      const { nom_locale, Destination, Date_voyage, Types_ligne, Volumes, qte } = req.body;

      // Préparation du tableau en HTML
      let tableHtml = `
          <table>
              <tr>
                  <th>Type Livraison</th>
                  <th>Volume (m³)</th>
                  <th>Quantité</th>
              </tr>
      `;
      Types_ligne.forEach((type, index) => {
          tableHtml += `
              <tr>
                  <td>${type}</td>
                  <td>${Volumes[index]}</td>
                  <td>${qte[index]}</td>
              </tr>
          `;
      });
      tableHtml += `</table>`;

      // Données de l'email à envoyer
      const emailData = {
          to: Destination, // Email du destinataire
          subject: "Livraison",
          htmlContent: `
              <!DOCTYPE html>
              <html lang="fr">
              <head>
                  <meta charset="UTF-8">
                  <meta http-equiv="X-UA-Compatible" content="IE=edge">
                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                  <title>Notification de livraison</title>
                  <style>
                      table {
                          border-collapse: collapse;
                          width: 100%;
                      }
                      th, td {
                          border: 1px solid black;
                          padding: 8px;
                          text-align: left;
                      }
                  </style>
              </head>
              <body>
                  <p>Bonjour ${nom_locale},</p>
                  <p>Vous avez reçu une livraison le ${Date_voyage}.</p>
                  <p>Les détails de la livraison sont les suivants :</p>
                  ${tableHtml}
                  <p>Bonne Réception</p>
                  <p>Cordialement,</p>
              </body>
              </html>
          `,
      };

      // Utilisation de la fonction `sendEmail` pour envoyer l'email
      const result = await sendEmail(emailData);
      res.send(result);

  } catch (error) {
      console.error("Erreur lors de l'envoi de l'email: ", error);
      res.status(500).send({ message: error.message || "Erreur interne du serveur" });
  }
};


exports.mailAnnulation = async (req, res) => {
  try {
      // Extraction des données de la requête
      const { nom_locale, mail } = req.body;

      // Préparation des données de l'email
      const emailData = {
          to: mail, // L'adresse e-mail du destinataire
          subject: "Retard de livraison",
          htmlContent: `
              <p>Bonjour ${nom_locale},</p>
              <p>Votre livraison connaît un retard imprévu indépendant de notre volonté. Nous nous excusons pour la gêne occasionnée.</p>
              <p>Votre livraison sera replanifiée dans les plus brefs délais.</p>
              <p>Cordialement,</p>
          `
      };

      // Utilisation de la fonction sendEmail pour envoyer l'email
      const response = await sendEmail(emailData);

      // Si l'email est envoyé avec succès
      res.status(200).send(response);
  } catch (error) {
      // Gestion des erreurs
      console.error("Erreur lors de l'envoi de l'e-mail :", error.message);
      res.status(500).send({ message: "Erreur lors de l'envoi de l'e-mail." });
  }
};

exports.mailReservation = async (req, res) => {
  try {
    const ids = req.body; // Liste des identifiants
    const results = await mailModel.getDetailsByIds(ids);
    if (!results) {
      return res.status(500).send("Erreur lors de la récupération des détails.");
    }

    const messagesMap = createMessagesMap(results);
    sendSMSReservationToday(ids);
    
    const errorMessages = [];
   await sendEmails(messagesMap.from, 'Prélèvement prévu', 'detailsPrelevement', errorMessages, messagesMap.quantitiesByDestinationAndType);
    await sendEmailsGroupedByDestination(results, 'Livraison prévue', 'detailsLivraison', errorMessages, messagesMap.quantitiesByDestinationAndType);

    if (errorMessages.length > 0) {
      return res.status(500).send({ message: "E-mails envoyés avec quelques erreurs.", errors: errorMessages });
    } else {
      return res.status(200).send({ message:"E-mails envoyés avec succès !"});
    }

  } catch (error) {
    console.error("Erreur lors de l'envoi de l'e-mail :", error); // Log full error
    res.status(500).send({ message: "Erreur lors de l'envoi de l'e-mail.", error: error.message });
  }
};


const sendEmailsGroupedByDestination = async (results, subject, emailTemplate, errorMessages) => {
  try {
    let conducteur_nom_complet =results[0].conducteur_nom_complet
    const data = Array.isArray(results) ? results : [];
    if (data.length === 0) {
      console.log("Aucune donnée pour envoyer les e-mails.");
      return;
    }

    // Grouper les données par `to_nom` et associer l'e-mail
    const groupedByDestination = await data.reduce(async (accPromise, row) => {
      const acc = await accPromise;
      const destination = row.to_nom;

      if (!acc[destination]) {
        acc[destination] = {
          email: row.to_email,
          to_nom: row.to_nom,
          date_voyage: row.date_voyage,
          H_arrivee: row.H_arrivee,
          tolerance: row.tolerance,
          details: [],
        };
      }

      const barcode = await generateQRCode(String(row.id)); // Générer le QR code
      acc[destination].details.push({
        id: row.id,
        barcode, // Ajouter l'image en base64
        type_ligne: row.type_ligne,
        quantite: row.quantite,
        volume: row.volume || row.estimation || "N/A",
      });

      return acc;
    }, Promise.resolve({}));

    // Envoyer les e-mails pour chaque destination
    for (const [destination, { email, to_nom, date_voyage, H_arrivee, tolerance, details }] of Object.entries(groupedByDestination)) {
      // Construire le contenu HTML de l'e-mail
      const introduction = `
        <p>Bonjour ${to_nom},</p>
        <p>Voici les détails de la livraison prévue le <strong>${date_voyage}</strong> à <strong>${H_arrivee}</strong> avec une tolérance de <strong>${tolerance * 60} minutes</strong> :</p>
        <p><strong>Conducteur:</strong>${conducteur_nom_complet}</p>
      `;

      const formattedDetails = `
        <table border="1" style="border-collapse: collapse; width: 80%; margin: 0 auto;">
          <thead>
            <tr>
              <th style="text-align: center;">ID</th>
              <th style="text-align: center;">QR Code</th>
              <th style="text-align: center;">Type Ligne</th>
              <th style="text-align: center;">Quantité</th>
              <th style="text-align: center;">Volume</th>
            </tr>
          </thead>
          <tbody>
            ${details
              .map(
                (detail) => `
                <tr>
                   <td style="text-align: center;">${detail.id}</td>
                  <td style="text-align: center;"><img src="${detail.barcode}" alt="QR Code" style="width: 150px; height: auto;" /></td>
                  <td style="text-align: center;">${detail.type_ligne}</td>
                  <td style="text-align: center;">${detail.quantite}</td>
                  <td style="text-align: center;">${detail.volume}</td>
                </tr>`
              )
              .join("")}
          </tbody>
        </table>
      `;

      try {
        await sendEmail({
          to: email, // Adresse e-mail extraite de `groupedByDestination`
          subject: subject,
          template: emailTemplate, // Nom du template d'e-mail
          htmlContent: introduction + formattedDetails, // Introduction + tableau HTML
        });
      } catch (error) {
        console.error(`Erreur lors de l'envoi à ${email}:`, error.message);
        errorMessages.push({ destination, error: error.message });
      }
    }
  } catch (error) {
    console.error("Erreur dans sendEmailsGroupedByDestination:", error.message);
    throw error;
  }
};






const generateQRCode = async (text) => {
  try {
    const qrCodeDataURL = await QRCode.toDataURL(text, {
      errorCorrectionLevel: "H",
      width: 150, // Taille adaptée pour l'affichage dans l'email
    });
    return qrCodeDataURL; // Retourne l'image sous forme de base64
  } catch (error) {
    console.error("Erreur lors de la génération du QR code :", error);
    throw error;
  }
};

// Create messages map from results
const createMessagesMap = (results) => {
  const messagesMap = {
    from: {},
    to: {},
    quantitiesByDestinationAndType: {}, // Storing quantities by destination and line type
  };

  for (const row of results) {
    const [startHour, startMinute] = row.H_depart.split(':').map(Number);
    const endTotalMinutes = row.H_arrivee ? row.H_arrivee.split(':').map(Number).reduce((a, b) => a * 60 + b) : 0;
    const startTotalMinutes = startHour * 60 + startMinute;
    let dateVoyagePrelevement = new Date(row.date_voyage);
    
    if (startTotalMinutes > endTotalMinutes) {
      dateVoyagePrelevement.setDate(dateVoyagePrelevement.getDate() - 1); // Adjust date
    }

    const typeDetailPrelevement = {
      type_ligne: row.type_ligne,
      date_voyage: dateVoyagePrelevement.toISOString().split('T')[0],
      h_depart: row.H_depart,
      tolerance: row.tolerance,
    };

    const typeDetailLivraison = {
      type_ligne: row.type_ligne,
      date_voyage: row.date_voyage,
      h_arrivee: row.H_arrivee,
      tolerance: row.tolerance,
    };

    const fromKey = `${row.from_email}-${row.from_nom}`;
    const toKey = `${row.to_email}-${row.to_nom}`;

    // Group quantities for prélèvement
    const quantityKeyFrom = `${fromKey}-${row.type_ligne}`;
    const quantityKeyTo = `${toKey}-${row.type_ligne}`;
    messagesMap.quantitiesByDestinationAndType[quantityKeyFrom] = (messagesMap.quantitiesByDestinationAndType[quantityKeyFrom] || 0) + row.quantite;
    messagesMap.quantitiesByDestinationAndType[quantityKeyTo] = (messagesMap.quantitiesByDestinationAndType[quantityKeyTo] || 0) + row.quantite;

    // Group prélèvement email details
    if (!messagesMap.from[fromKey]) {
      messagesMap.from[fromKey] = {
        email: row.from_email,
        nom: row.from_nom,
        details: new Set(),
      };
    }
    messagesMap.from[fromKey].details.add(JSON.stringify(typeDetailPrelevement));

    // Group livraison email details
    if (!messagesMap.to[toKey]) {
      messagesMap.to[toKey] = {
        email: row.to_email,
        nom: row.to_nom,
        details: new Set(),
      };
    }
    messagesMap.to[toKey].details.add(JSON.stringify(typeDetailLivraison));
  }

  return messagesMap;
};

// Send emails based on messages map
const sendEmails = async (messages, subject, detailType, errorMessages, quantitiesByDestinationAndType) => {
  for (const key in messages) {
    const group = messages[key];
    const detailsHtml = Array.from(group.details).map((detail) => {
      const parsedDetail = JSON.parse(detail);
      const totalQuantity = quantitiesByDestinationAndType[`${key}-${parsedDetail.type_ligne}`] || 0; // Handle potential undefined
      return `
        Type de ${detailType} : ${parsedDetail.type_ligne} <br>
        Date de ${detailType} : ${parsedDetail.date_voyage} à ${parsedDetail.h_depart || parsedDetail.h_arrivee} avec ± ${parsedDetail.tolerance * 60} min <br>
        Quantité totale : ${totalQuantity} unités <br>
      `;
    }).join("");

    const emailData = {
      to: group.email,
      subject: subject,
      htmlContent: `
        <p>Bonjour ${group.nom},</p>
        Voici les détails de votre ${detailType} :<br>
        ${detailsHtml}
        Cordialement,
      `,
    };

    try {
      await sendEmail(emailData); // This is the function call
      console.log(`Email sent to ${group.email} successfully.`);
    } catch (error) {
      console.error(`Error sending email to ${group.email}:`, error);
      errorMessages.push(`Error sending email to ${group.email}: ${error.message}`);
    }
  }
};

exports.mailUpdateReservationDepart = async (req, res) => {
  try {
    const ids = req.body;

    // Remplacer par votre méthode pour obtenir les détails nécessaires
    mailModel.getDetailsByIds(ids, async (err, results) => {
      if (err) {
        console.error("Erreur lors de la récupération des réservations :", err);
        res.status(500).send("Erreur lors de la récupération des réservations.");
        return;
      }

      const messagesMap = {
        from: {},
      };

      results.forEach((row) => {
        const reservationDetail = {
          date_reservation: new Date(row.date_reservation).toISOString().split('T')[0],
          type_reservation: row.type_reservation,
          quantite: row.quantite,
          client: row.client_nom,
        };

        const fromKey = `${row.from_email}-${row.from_nom}`;

        if (!messagesMap.from[fromKey]) {
          messagesMap.from[fromKey] = {
            email: row.from_email,
            nom: row.from_nom,
            details: new Set(),
          };
        }
        messagesMap.from[fromKey].details.add(JSON.stringify(reservationDetail));
      });

      const messages = [];

      for (const email in messagesMap.from) {
        const group = messagesMap.from[email];
        const detailsHtml = Array.from(group.details).map((detail) => {
          const parsedDetail = JSON.parse(detail);

          return `
            Type de réservation : ${parsedDetail.type_reservation} <br>
            Date de réservation : ${parsedDetail.date_reservation} <br>
            Quantité : ${parsedDetail.quantite} <br>
            Client : ${parsedDetail.client} <br>
          `;
        }).join("");

        const subject = "Nouvelle réservation confirmée";

        messages.push({
          to: group.email,
          subject: subject,
          htmlContent: `
            <p>Bonjour ${group.nom},</p>
            Voici les détails de votre réservation : <br>
            ${detailsHtml}
            Cordialement,
          `,
        });
      }

      // Envoyer les emails en utilisant la fonction sendEmail
      for (const message of messages) {
        try {
          await sendEmail(message);
        } catch (error) {
          console.error("Erreur lors de l'envoi de l'e-mail :", error.message);
          // Vous pouvez choisir de gérer les erreurs d'envoi individuellement
        }
      }

      res.status(200).send("E-mails envoyés avec succès !");
    });
  } catch (error) {
    console.error("Erreur lors de l'envoi de l'e-mail :", error.message);
    res.status(500).send("Erreur lors de l'envoi de l'e-mail.");
  }
};

exports.mailUpdateReservationArrivee = async (req, res) => {
  try {
    const ids = req.body;

    // Remplacez par votre méthode pour obtenir les détails nécessaires
    mailModel.getDetailsByIds(ids, async (err, results) => {
      if (err) {
        console.error("Erreur lors de la récupération des détails :", err);
        res.status(500).send("Erreur lors de la récupération des détails.");
        return;
      }

      if (results[0].id_camion == 14 && results[0].id_conducteur == 61) {
        CancelReservation(ids);
      } else {
        sendSMSUpdateReservation(ids);
      }

      const messagesMap = {
        to: {},
      };

      const totalQuantitiesByDestination = {};

      results.forEach((row) => {
        const toKey = `${row.to_email}-${row.to_nom}`;
        if (row.to_email) {
          if (!messagesMap.to[toKey]) {
            messagesMap.to[toKey] = {
              email: row.to_email,
              nom: row.to_nom,
              details: new Set(),
            };
          }

          const typeDetail = {
            type_ligne: row.type_ligne,
            date_voyage: row.date_voyage,
            h_arrivee: row.H_arrivee,
            tolerance: row.tolerance,
            id_camion: row.id_camion,
            id_conducteur: row.id_conducteur
          };

          messagesMap.to[toKey].details.add(JSON.stringify(typeDetail));

          if (!totalQuantitiesByDestination[toKey]) {
            totalQuantitiesByDestination[toKey] = {};
          }

          if (!totalQuantitiesByDestination[toKey][row.type_ligne]) {
            totalQuantitiesByDestination[toKey][row.type_ligne] = 0;
          }

          totalQuantitiesByDestination[toKey][row.type_ligne] += row.quantite;
        }
      });

      const messages = [];

      for (const email in messagesMap.to) {
        const group = messagesMap.to[email];
        let subject = `Livraison prévue`; // Default subject

        const detailsHtml = Array.from(group.details).map((detail) => {
          const parsedDetail = JSON.parse(detail);
          const totalQuantity = totalQuantitiesByDestination[email][parsedDetail.type_ligne];

          let messagePart = `
            Type de livraison : ${parsedDetail.type_ligne} <br>
            Date de livraison : ${parsedDetail.date_voyage} à ${parsedDetail.h_arrivee.replace(/:00$/, '')} avec ± ${parsedDetail.tolerance * 60} min <br>
            Quantité totale : ${totalQuantity} unités <br>
          `;

          if (parsedDetail.id_camion == 14 && parsedDetail.id_conducteur == 61) {
            messagePart = `
              <b>Nous vous informons que votre livraison a été reportée en raison de circonstances imprévues.</b><br>
              <b>Veuillez nous excuser pour ce désagrément. Nous vous tiendrons informé de la nouvelle date de livraison.</b><br>
            `;
            subject = `Annulation de Livraison`;
          }

          return messagePart;
        }).join("");

        messages.push({
          to: group.email,
          subject: subject,
          htmlContent: `
            <p>Bonjour ${group.nom},</p>
            Voici les détails de votre livraison :<br>
            ${detailsHtml}
            Cordialement,
          `,
        });
      }

      // Envoyer les e-mails en utilisant la fonction sendEmail
      for (const message of messages) {
        try {
          await sendEmail(message);
        } catch (error) {
          console.error("Erreur lors de l'envoi de l'e-mail :", error.message);
          // Vous pouvez choisir de gérer les erreurs d'envoi individuellement
        }
      }

      res.status(200).send("E-mails envoyés avec succès !");
    });
  } catch (error) {
    console.error("Erreur lors de l'envoi de l'e-mail :", error.message);
    res.status(500).send("Erreur lors de l'envoi de l'e-mail.");
  }
};
 
async function sendSMSReservationToday(ids) {
  console.log("--------------------");

  try {
    // Récupérer les détails par identifiants
    const results = await mailModel.getDetailsExcludingReservationDate(ids);

    const destinations = {};

    // Obtenir la date du jour (en ignorant l'heure)
    const today = new Date().toISOString().split('T')[0];

    results.forEach((row) => {


      // Comparer la date de voyage avec la date du jour
      const dateVoyage = new Date(row.date_voyage).toISOString().split('T')[0];
      if (dateVoyage !== today) {
        console.log(`La date de voyage (${dateVoyage}) ne correspond pas à la date du jour (${today}) pour l'ID ${row.id}`);
        return;
      }

      // Si la destination n'existe pas déjà, l'initialiser
      if (!destinations[row.to_nom]) {
        destinations[row.to_nom] = {
          phone: row.phone,
          totalQuantity: 0,
          dateVoyage: row.date_voyage,
          H_arrivee: row.H_arrivee,
          tolerance: row.tolerance,
          conducteur: row.conducteur_nom,
          conducteurPhone: row.mobile,
        };
      }

      // Ajouter les quantités
      destinations[row.to_nom].totalQuantity += row.quantite;
    });


    // Créer des messages pour chaque destination
    const messageTextParts = [];

    for (const [destination, data] of Object.entries(destinations)) {
      const messageText = `Livraison à ${destination} le ${data.dateVoyage} avec ${data.totalQuantity} unités à ${data.H_arrivee.split(':')[0]}:${data.H_arrivee.split(':')[1]}H ± ${(data.tolerance) * 60} min. Conducteur: ${data.conducteur} (Tél: ${data.conducteurPhone})`;
      messageTextParts.push({ phone: data.phone, message: messageText });
    }


    // Utiliser la fonction sendSMS pour envoyer les messages
    if (process.env.SMS_RESERVATION === "true") {
      for (const messagePart of messageTextParts) {
        try {
          await mailModel.sendingSMS(messagePart.phone, messagePart.message);
          console.log(`SMS envoyé à ${messagePart.phone} : ${messagePart.message}`);
        } catch (error) {
          console.error(`Erreur lors de l'envoi du SMS à ${messagePart.phone} :`, error.message);
        }
      }
    }

  } catch (error) {
    console.error("Erreur lors de l'envoi des SMS :", error.message);
  }
}

function sendSMSUpdateReservation(ids) {
  if(process.env.SMS_RESERVATION =="true"){
    try {
      // Récupérer les détails par identifiants
      mailModel.getDetailsByIds(ids, async (err, results) => {
          if (err) {
              console.error("Erreur lors de la récupération des détails :", err);
              return;
          }

          const destinations = {};

          results.forEach((row) => {
              // Vérifier si to_destination et to_phone sont définis
              if (!row.to_nom || !row.to_phone || row.type_ligne !== 'livraison PF (magasin)') {
                  console.warn(`Données manquantes pour l'ID ${row.id}`);
                  return; // Passer à l'entrée suivante si des données sont manquantes
              }

              // Créer une entrée pour chaque destination
              if (!destinations[row.to_nom]) {
                  destinations[row.to_nom] = {
                      phone: row.to_phone,
                      totalQuantity: 0,
                      dateVoyage: row.date_voyage,
                      H_arrivee: row.H_arrivee,
                      tolerance: row.tolerance
                  };
              }
              // Additionner les quantités
              destinations[row.to_nom].totalQuantity += row.quantite;
          });

          // Créer des messages pour chaque destination
          const messageTextParts = [];

          for (const [destination, data] of Object.entries(destinations)) {
              const messageText = `Modification Livraison de ${destination} sera le ${data.dateVoyage} avec ${data.totalQuantity} unités à ${data.H_arrivee.split(':')[0]}:${data.H_arrivee.split(':')[1]}H ± ${(data.tolerance) * 60} min.`;
              messageTextParts.push({ phone: data.phone, text: messageText });
          }

          

          // Envoyer les messages pour chaque destination
          for (const messagePart of messageTextParts) {
            try {
              await mailModel.sendingSMS(messagePart.phone, messagePart.message);
              console.log(`SMS envoyé à ${messagePart.phone} : ${messagePart.message}`);
            } catch (error) {
              console.error(`Erreur lors de l'envoi du SMS à ${messagePart.phone} :`, error.message);
            }
          }
      });
  } catch (error) {
      console.error("Erreur lors de l'envoi des SMS :", error.message);
  }
  }

}
function CancelReservation(ids) {

  if (process.env.SMS_RESERVATION === "true") {
    try {
      // Récupérer les détails par identifiants
      mailModel.getDetailsByIds(ids, async (err, results) => {
        if (err) {
          console.error("Erreur lors de la récupération des détails :", err);
          return;
        }

        if (!results || !Array.isArray(results)) {
          console.error("Les résultats récupérés ne sont pas un tableau valide.");
          return;
        }

        const uniqueDestinations = new Set();

        results.forEach((row) => {
          if (row.to_phone && row.type_ligne === 'livraison PF (magasin)') {
            uniqueDestinations.add(row.to_phone);
          }
        });

        // Message d'annulation court
        const cancelMessage = "Votre livraison a été annulée. Nous vous tiendrons informé pour la nouvelle date.";

     

        for (const messagePart of uniqueDestinations) {
          try {
            await mailModel.sendingSMS(messagePart,cancelMessage);
            console.log(`SMS envoyé à ${messagePart} : ${cancelMessage}`);
          } catch (error) {
            console.error(`Erreur lors de l'envoi du SMS à ${messagePart} :`, error.message);
          }
        }

      });
    } catch (error) {
      console.error("Erreur lors de l'envoi des SMS :", error.message);
    }
  }
}

async function processData(results) {
  const destinations = {};

  results.forEach((row) => {
   
      if (!row.nom_locale || !row.phone || row.type_ligne !== 'livraison PF (magasin)') {
          console.warn(`Données manquantes ou incorrectes pour l'ID ${row.id}`);
          return;
      }

      if (!destinations[row.nom_locale]) {
          destinations[row.nom_locale] = {
              mobile_consucteur: row.mobile,
              phone_dest: row.phone,
              totalQuantity: 0,
              dateVoyage: row.date_voyage,
              H_arrivee: row.H_arrivee,
              tolerance: row.tolerance,
              nom: row.nom,
              prenom: row.prenom,
          };
      }
      destinations[row.nom_locale].totalQuantity += row.quantite;
  });

  const messageTextParts = [];

  for (const [destination, data] of Object.entries(destinations)) {
      const heureArrivee = `${data.H_arrivee.split(':')[0]}:${data.H_arrivee.split(':')[1]}H`;
      const toleranceMinutes = data.tolerance * 60;
      const messageText = `Livraison à ${destination} par ${data.prenom} ${data.nom} (${data.mobile_consucteur}) le ${data.dateVoyage} avec ${data.totalQuantity} unités à ${heureArrivee} ± ${toleranceMinutes} min.`;
      messageTextParts.push({ phone: data.phone_dest, text: messageText });
  }

  return messageTextParts;
}
async function sendSMS(messageTextParts) {
  for (const { phone, text } of messageTextParts) {
      if (phone && text) {
          try {
              await mailModel.sendingSMS(phone, text);
              console.log(`SMS envoyé avec succès à ${phone} !`);
          } catch (error) {
              console.error(`Erreur définitive lors de l'envoi du SMS à ${phone} :`, error.message);
          }
      } else {
          console.warn("Aucun numéro de téléphone valide ou message vide pour l'envoi du SMS.");
      }
  }
}






// Fonction principale qui combine le tout
async function sendSMSReservation() {
  try {
      const results = await mailModel.getReservationData();
      console.log(`Données récupérées : ${results.length} lignes`);

      const messageTextParts = await processData(results);
      if (process.env.SMS_RESERVATION === "true") {
        await mailModel.sendingSMS(messageTextParts[0].phone, messageTextParts[0].text);
      }
  } catch (error) {
      console.error("Erreur lors de l'envoi des SMS :", error.message);
  }
}

exports.sendMailsFromFront = async (req, res) => {
  const emailData = req.body;
  try {
    // Call sendEmail and await its result
    const info = await sendEmail(emailData);

    // Send the success response with messageId
    res.status(200).json({
      message: "Email sent successfully",
      messageId: info.messageId,
    });

  } catch (error) {
    console.error('Error sending email: ', error);

    // Return an error response
    res.status(500).json({
      message: error.message || "Email sending failed",
    });
  }
};




// Planifier l'envoi de SMS toutes les 4 minutes
cron.schedule('0 20 * * *', () => {
  //cron.schedule('*/50 * * * * *', () => {

  console.log('Exécution de l\'envoi des SMS de réservation...');
  sendSMSReservation();
});

  
  
  
  