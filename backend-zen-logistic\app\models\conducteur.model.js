const sql = require("./db.js");

//constructeur  

const Conducteur = function(conducteur) {
    //this.id=conducteur.id ;
    this.nom = conducteur.nom;
    this.prenom = conducteur.prenom;
    this.nom_utilisateur = conducteur.nom_utilisateur;
    this.mot_de_passe= conducteur.mot_de_passe;
    this.mobile = conducteur.mobile;
    this.cin = conducteur.cin;
    this.image_cin = conducteur.image_cin;
    this.ajoutee_par = conducteur.ajoutee_par;
  } ;

  // Fuction create conducteur 

Conducteur.create = (newConducteur, result) => {
    sql.query("INSERT INTO conducteur SET ?", newConducteur, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
     // //console.log("created conducteur: ", { id: res.insertId, ...newConducteur });
      result(null, { id: res.insertId, ...newConducteur });
    });
  };

  //Function get all conducteurs 
  Conducteur.getAll = (result) => {
    const query = `
      SELECT 
        C.id, 
        C.nom, 
        C.prenom, 
        C.nom_utilisateur, 
        C.mobile, 
        C.cin, 
        C.image_cin, 
        C.ajoutee_par, 
        CONCAT(U.prenom, ' ', U.nom) AS transporteur 
      FROM 
        conducteur C 
      INNER JOIN 
        utilisateurs U 
      ON 
        C.ajoutee_par = U.id 
      WHERE 
        C.bloqued = false 
      ORDER BY 
        C.ajoutee_par
    `;
  
    sql.query(query, (err, res) => {
      if (err) {
        console.error("Erreur lors de la récupération des conducteurs : ", err);
        result(null, err); // Retourne l'erreur au callback
        return;
      }
  
      // Retourne les résultats
      result(null, res);
    });
  };
  

  //Function delate conducteur with ID
  Conducteur.conducteurBloqued = (id, result) => {
    sql.query("UPDATE conducteur SET bloqued = true WHERE id = ?", id, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }
  
      if (res.affectedRows == 0) {
        // Conducteur avec l'ID spécifié introuvable
        result({ kind: "not_found" }, null);
        return;
      }
  
      console.log("Updated conducteur with id: ", id);
      result(null, res);
    });
  };
  

// function find a conducteur by ID 
Conducteur.findById = (conducteurId, result) => {
    sql.query(`SELECT * FROM conducteur WHERE id = ${conducteurId}`, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      if (res.length) {
        //console.log("found conducteur: ", res[0]);
        result(null, res[0]);
        return;
      }
  
      // not found conducteur with the id
      result({ kind: "not_found" }, null);
    });
  };

  
//Function get all Conducteur   
Conducteur.findConducteur =(idUser , result )=> {
  sql.query(`SELECT C.id,C.nom,C.prenom,C.nom_utilisateur,C.mobile,C.cin,C.image_cin,C.ajoutee_par,CONCAT(U.prenom,U.nom) as transporteur FROM conducteur C INNER JOIN utilisateurs U on C.ajoutee_par= U.id WHERE C.ajoutee_par = ${idUser}  ORDER  By C.ajoutee_par `, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result( err) ,null;
      return;
    }

    if (res.length) {
      //console.log("found Conducteur: ");
      result(null, res);
      return;
    }

    // not found Conducteur with the iduser 
    result({ kind: "not_found" }, null);
  });
};
Conducteur.findAuth = (conducteurNu , conducteurMdp , result) => {
  // //console.log(`SELECT * FROM utilisateurs WHERE nom_utilisateur = "${conducteurNu}"  AND  mot_de_passe="${conducteurMdp}" `)
   sql.query(`SELECT * FROM conducteur WHERE nom_utilisateur="${conducteurNu}"  AND  mot_de_passe="${conducteurMdp}" `
   , (err, res) => {  
      //console.log(`SELECT * FROM conducteur WHERE nom_utilisateur="${conducteurNu}"  AND  mot_de_passe="${conducteurMdp}" `);
 
     if (err) {
       console.log("error:", err);
       result(err, null);
       return;
     } 
 
     if (res.length) {
       //console.log("found conducteur: ", res[0]);
       result(null, res[0]);
       return;
     } 
 
     // not found conducteur with user name and password 
     result({ kind: "not_found" }, null);  
 
   });    
 };    
  // Function


  module.exports = Conducteur ; 

    