const sql = require("./db.js");

//constructeur  

const NotUser = function(notUser) {
   // this.id=camion.id ;
    this.id_user = notUser.id_user;
    this.onesignal=notUser.onesignal ;
     
  } ;



  // Fuction create camion 

  NotUser.create = (newNot, result) => {
    sql.query("INSERT INTO user_onesignal SET ?", newNot, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      ////console.log("created camion: ", { type_camion: res.inserttype_camion, ...newCamion });
      result(null, { newNot });
    });
  };


/*
  //Function get all camion 
  Facture.getAll = result => {
    sql.query("SELECT * FROM facture", (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }
  
      //console.log("Camion: ", res);
      result(null, res);
    });
  } 

  //Function delate Camion with ID
 

  // function find a camion by ID 
  Facture.findById = (factureId, result) => {
  sql.query(`SELECT F.raison_sociale, F.id_facture,C.id as id_commande,C.code,CO.immatriculation,C.ville_depart,C.ville_arrivee,C.prix FROM facture F INNER JOIN facture_commande FC ON F.id_facture=FC.id_facture INNER JOIN commande C ON C.id=FC.id_commande INNER JOIN camion CO ON C.id_camion=CO.id where F.id_facture=${factureId}`, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.length) {
    
    
    
   
    
    
    
     
      result(null, res );
      return;
    }

    // not found camion with the id
    result({ kind: "not_found" }, null);
  });
};
 
Facture.getAllE = (expediteur,result) => {
  sql.query("SELECT * FROM facture F INNER JOIN facture_expediteur FE ON F.id_facture=FE.id_facture where FE.id_expediteur="+expediteur, (err, res) => {
    if (err) {
      //console.log("error: ", err);
      result(null, err);
      return;
    }

    ////console.log("Camion: ", res);
    result(null, res);
  });
} 
*/
  module.exports = NotUser ; 