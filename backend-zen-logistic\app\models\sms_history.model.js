const sql = require("./db.js");
const cron = require("node-cron");
const https = require("https");
// Define the SMSHistory model
const SMSHistory = {};

// Create a new SMS record
SMSHistory.create = (smsData, result) => {
  console.log("**************",smsData)
  const { ref, content, mobile, status,code } = smsData;

  const query = `
    INSERT INTO sms_history (ref, content, number, status,code)
    VALUES (?, ?, ?, ?,?)
  `;

  sql.query(query, [ref, content, mobile, status,code], (err, res) => {
    if (err) {
      console.error("Error inserting SMS into database: ", err);
      result(err, null);
      return;
    }

    console.log("SMS successfully inserted: ", { id: res.insertId, ...smsData });
    result(null, { id: res.insertId, ...smsData });
  });
};





async function fetchSMSStatus(ref) {
  const url = `https://41.226.169.210/API/verifsms.php?SPID=62&LOGIN=Zen%20&PASS=Zen%212019%24&TYPE=sms&REF=${ref}`;

  return new Promise((resolve, reject) => {
    const options = {
      rejectUnauthorized: false, // Ignore la vérification SSL
    };

    https.get(url, options, (response) => {
      let responseData = "";

      response.on("data", (chunk) => {
        responseData += chunk;
      });

      response.on("end", () => {
        try {
          const result = JSON.parse(responseData);
          resolve(result);
        } catch (error) {
          reject(new Error("Erreur lors de l'analyse de la réponse de l'API."));
        }
      });
    }).on("error", (error) => {
      reject(new Error("Erreur lors de la requête à l'API : " + error.message));
    });
  });
}


// Fonction principale pour traiter les données de sms_history
async function processSMSHistory() {
  try {
    // Récupérer la date de début (une semaine avant aujourd'hui)
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    const formattedDate = oneWeekAgo.toISOString().split("T")[0];

    // Préparer la requête SQL
    const smsHistoryQuery = `
      SELECT id, ref, status
      FROM sms_history
      WHERE created_at >= ? AND code = 200
    `;

    // Exécuter la requête SQL avec un callback
    sql.query(smsHistoryQuery, [formattedDate], async (err, smsList) => {
      if (err) {
        console.error("Erreur lors de l'exécution de la requête SQL : ", err.message);
        return;
      }

      if (!smsList || smsList.length === 0) {
        console.log("Aucune donnée trouvée dans la table sms_history pour la période sélectionnée.");
        return;
      }

      console.log(`Nombre de lignes récupérées : ${smsList.length}`);

      for (const ligne of smsList) {
        try {
          const currentStatus = await fetchSMSStatus(ligne.ref);

          // Vérifier si `data.status` est différent de `ligne.status`
          const apiStatus = currentStatus && currentStatus.data && currentStatus.data[0] && currentStatus.data[0].status;
          if (!apiStatus) {
            console.warn(`Statut API manquant pour REF ${ligne.ref}`);
            continue;
          }

          if (apiStatus !== ligne.status) {
            console.log(
              `Mise à jour nécessaire : REF ${ligne.ref}, Ancien statut : ${ligne.status}, Nouveau statut : ${apiStatus}`
            );

            // Mettre à jour la table sms_history
            const updateQuery = `
              UPDATE sms_history
              SET status = ?
              WHERE id = ?
            `;

            sql.query(updateQuery, [apiStatus, ligne.id], (updateErr) => {
              if (updateErr) {
                console.error(`Erreur lors de la mise à jour de REF ${ligne.ref} :`, updateErr.message);
                return;
              }
              console.log(`Mise à jour réussie pour REF ${ligne.ref}`);
            });
          } else {
            console.log(`Pas de mise à jour nécessaire pour REF ${ligne.ref}`);
          }
        } catch (error) {
          console.error(`Erreur lors du traitement de REF ${ligne.ref} :`, error.message);
        }
      }
    });
  } catch (error) {
    console.error("Erreur lors de la récupération des données :", error.message);
  }
}



cron.schedule("0 0,12 * * *", () => {
  //cron.schedule("*/30 * * * * *", () => {

  console.log("Exécution de la tâche planifiée processSMSHistory...");
  processSMSHistory();
});

module.exports = SMSHistory;
