/**
 * @swagger
 * tags:
 *   name: Fournisseur
 *   description: Opérations sur les fournisseurs
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Fournisseur:
 *       type: object
 *       required:
 *         - nom_fournisseur
 *         - mat_fisc
 *         - sous_traitant
 *         - adresse
 *         - mail
 *         - flux
 *         - entreposage
 *         - code_cegid
 *         - code_sageX3
 *       properties:
 *         id:
 *           type: integer
 *           description: ID unique du fournisseur
 *         nom_fournisseur:
 *           type: string
 *           description: Nom du fournisseur
 *         mat_fisc:
 *           type: string
 *           description: Matricule fiscal du fournisseur
 *         sous_traitant:
 *           type: boolean
 *           description: Indique si le fournisseur est un sous-traitant
 *         adresse:
 *           type: string
 *           description: Adresse du fournisseur
 *         mail:
 *           type: string
 *           format: email
 *           description: Email du fournisseur
 *         flux:
 *           type: boolean
 *           description: Indique si le fournisseur est associé à un flux
 *         entreposage:
 *           type: boolean
 *           description: Indique si le fournisseur est associé à l'entreposage
 *         code_cegid:
 *           type: string
 *           description: Code Cegid du fournisseur
 *         code_sageX3:
 *           type: string
 *           description: Code Sage X3 du fournisseur
 *
 * /api/fournisseur:
 *   post:
 *     tags: [Fournisseur]
 *     summary: Ajouter un nouveau fournisseur
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Fournisseur'
 *     responses:
 *       201:
 *         description: Fournisseur créé avec succès
 *       400:
 *         description: Erreur de validation des données
 *       500:
 *         description: Erreur lors de la création du fournisseur
 *
 *   get:
 *     tags: [Fournisseur]
 *     summary: Obtenir tous les fournisseurs
 *     responses:
 *       200:
 *         description: Liste des fournisseurs
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Fournisseur'
 *       500:
 *         description: Erreur lors de la récupération des fournisseurs
 *
 * /api/fournisseur/{id}:
 *   get:
 *     tags: [Fournisseur]
 *     summary: Obtenir un fournisseur par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du fournisseur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Fournisseur trouvé
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Fournisseur'
 *       404:
 *         description: Fournisseur non trouvé
 *       500:
 *         description: Erreur lors de la récupération du fournisseur
 *
 *   put:
 *     tags: [Fournisseur]
 *     summary: Mettre à jour un fournisseur par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du fournisseur à mettre à jour
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Fournisseur'
 *     responses:
 *       200:
 *         description: Fournisseur mis à jour avec succès
 *       404:
 *         description: Fournisseur non trouvé
 *       500:
 *         description: Erreur lors de la mise à jour du fournisseur
 *
 *   delete:
 *     tags: [Fournisseur]
 *     summary: Supprimer un fournisseur par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du fournisseur à supprimer
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Fournisseur supprimé avec succès
 *       404:
 *         description: Fournisseur non trouvé
 *       500:
 *         description: Erreur lors de la suppression du fournisseur
 *
 * /api/findFournisseurWhithFLux:
 *   get:
 *     tags: [Fournisseur]
 *     summary: Obtenir tous les fournisseurs avec flux
 *     responses:
 *       200:
 *         description: Liste des fournisseurs avec flux
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Fournisseur'
 *       500:
 *         description: Erreur lors de la récupération des fournisseurs avec flux
 *
 * /api/findFournisseurWhithEntreposage:
 *   get:
 *     tags: [Fournisseur]
 *     summary: Obtenir tous les fournisseurs avec entreposage
 *     responses:
 *       200:
 *         description: Liste des fournisseurs avec entreposage
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Fournisseur'
 *       500:
 *         description: Erreur lors de la récupération des fournisseurs avec entreposage
 *
 * /api/findClientToInvoice:
 *   get:
 *     tags: [Fournisseur]
 *     summary: Obtenir tous les clients à facturer
 *     responses:
 *       200:
 *         description: Liste des clients à facturer
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Fournisseur'
 *       500:
 *         description: Erreur lors de la récupération des clients à facturer
 *
 * /api/findClientInvoiced:
 *   get:
 *     tags: [Fournisseur]
 *     summary: Obtenir tous les clients facturés
 *     responses:
 *       200:
 *         description: Liste des clients facturés
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Fournisseur'
 *       500:
 *         description: Erreur lors de la récupération des clients facturés
 *
 * /api/findClientFromSageByMat/{mat}:
 *   get:
 *     tags: [Fournisseur]
 *     summary: Obtenir un client à partir de Sage par matricule
 *     parameters:
 *       - in: path
 *         name: mat
 *         required: true
 *         description: Matricule du client
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Client trouvé
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       404:
 *         description: Client non trouvé
 *       500:
 *         description: Erreur lors de la récupération du client
 *
 * /api/fournisseur/{mat_fisc}:
 *   get:
 *     tags: [Fournisseur]
 *     summary: Obtenir un fournisseur par matricule fiscal
 *     parameters:
 *       - in: path
 *         name: mat_fisc
 *         required: true
 *         description: Matricule fiscal du fournisseur
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Fournisseur trouvé
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Fournisseur'
 *       204:
 *         description: Aucun fournisseur trouvé
 *       500:
 *         description: Erreur lors de la récupération du fournisseur
 *
 * /api/getFou/{type}:
 *   get:
 *     tags: [Fournisseur]
 *     summary: Obtenir un fournisseur par type
 *     parameters:
 *       - in: path
 *         name: type
 *         required: true
 *         description: Type du fournisseur
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Fournisseur trouvé
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Fournisseur'
 *       204:
 *         description: Aucun fournisseur trouvé
 *       500:
 *         description: Erreur lors de la récupération du fournisseur
 */
