const Facture = require("../models/facture.model.js");
bodyParser = require("body-parser");
const pdf = require("html-pdf");
const fs = require("fs");
const mailModel = require("../models/mail.model.js");

const axios = require("axios");

// Create a Camion
exports.create = async (req, res) => {
  try {
    if (!req.body) {
      res.status(400).send({
        message: "Content can not be empty",
      });
      return;
    }

    const facture = new Facture({
      id_client: req.body.id_client,
      created_at: req.body.created_at,
      tot_facture: req.body.tot_facture,
      tot_factureTTC: req.body.tot_factureTTC,
      created_by: req.body.created_by,
      ligne_ids: req.body.ligne_ids,
      adresse: req.body.adresse,
      tva:req.body.tva,
      timbre:req.body.timbre,
      type:req.body.type,
      invoice_date:req.body.invoice_date

    });

    Facture.create(facture, (err, data) => {
      if (err) {
        res.status(500).send({
          message:
            err.message || "Some error occurred while creating the facture.",
        });
        return;
      }
      res.send(data);
    });
  } catch (error) {
    console.error(error);
    res.status(500).send({
      message: "Internal server error",
    });
  }
};

exports.findLigneFac = (req, res) => {
  const ids = req.body.ids;

  Facture.findLigneFac(ids, (err, data) => {
    if (err) {
      if (err.kind === "NoResults") {
        // Aucun résultat trouvé, retournez une réponse appropriée
        return res.status(200).json({ message: err.message });
      } else {
        // Autre type d'erreur, gestion standard
        return res
          .status(500)
          .json({
            message: "Erreur lors de la recherche des lignes de facture.",
          });
      }
    }

    // Traitement des données normalement si tout est OK
    return res.status(200).json(data);
  });
};


exports.findOne = (req, res) => {
  Facture.findById(req.params.factureId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found facture with id ${req.params.factureId}.`,
        });
      } else {
        res.status(500).send({
          message: "Error retrieving facture with id " + req.params.factureId,
        });
      }
    } else res.send(data);
  });
};

exports.findAllFacture = (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.pageSize) || 10;
  const idClient = req.query.id_client || null;
  const invoiceDate = req.query.invoice_date || null; // Format attendu : YYYY-MM

  Facture.getAll(page, pageSize, idClient, invoiceDate, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({ message: `Not found Facture` });
      } else {
        res.status(500).send({ message: "Error retrieving Facture" });
      }
    } else {
      res.send(data);
    }
  });
};


//  Retrieve all Camion from the database
exports.findAllFactureE = (req, res) => {
  //console.log('rrrr')
  Facture.getAllE(req.params.expediteur, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Facture `,
        });
      } else {
        res.status(500).send({
          message: "Error retrieving Facture ",
        });
      }
    } else res.send(data);
  });
};

function encryptId(id) {
  const key = 63950543490678405;
  const idString = id.toString();
  let encryptedId = '';
  for (let i = 0; i < idString.length; i++) {
      encryptedId += String.fromCharCode(idString.charCodeAt(i) ^ key);
  }

  return encryptedId;
}

// Fonction de décryptage
function decryptId(encryptedId) {
  const key = 63950543490678405;

  let decryptedId = '';
  for (let i = 0; i < encryptedId.length; i++) {
      decryptedId += String.fromCharCode(encryptedId.charCodeAt(i) ^ key);
  }

  return parseInt(decryptedId);
}


exports.sendEmailFacture = async (req, res) => {
  const factureId = req.body.id;
  const idCrypt = encryptId(factureId);

  try {
    if (!req.body.mail || !req.body.client) {
      return res.status(400).send({ message: "L'adresse email ou le nom du client est manquant." });
    }
    let attachment = [];
      const pdfPath = `factures/facture_${factureId}.pdf`;
      if (fs.existsSync(pdfPath)) { 
        attachment.push({
          filename: `facture_${factureId}.pdf`, 
          path: pdfPath,
        });
      } else {
        console.error(`Le fichier PDF n'existe pas à l'emplacement : ${pdfPath}`);
      }

    const emailData = {
      to: req.body.mail,  
      subject: "Facture",
      htmlContent: `
        <!DOCTYPE html>
        <html lang="fr">
        <head>
          <meta charset="UTF-8">
          <meta http-equiv="X-UA-Compatible" content="IE=edge">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Facture</title>
        </head>
        <body>
          <p>Bonjour ${req.body.client},</p>
          <p>Nous avons le plaisir de vous transmettre votre facture en pièce jointe.</p>
          <p>Nous vous prions de bien vouloir accorder votre attention à cette facture dans les 48 heures. Si elle n'est pas validée dans ce délai, elle sera automatiquement validée.</p>
          <p>Pour procéder à la validation, veuillez cliquer sur l'un des boutons suivants :</p>

          <table cellpadding="10" cellspacing="0" style="margin: 0 auto;">
            <tr>
              <td align="center" style="background-color: #4CAF50; border-radius: 5px;">
                <a href="http://localhost:3000/api/facture/updateTrue/${idCrypt}" style="text-decoration: none; color: white; padding: 10px; display: inline-block;">Valider</a>
              </td>
              <td style="width: 20px;"></td> <!-- Espacement entre les boutons -->
              <td align="center" style="background-color: #f44336; border-radius: 5px;">
                <a href="http://localhost:3000/api/facture/updateFalse/${idCrypt}" style="text-decoration: none; color: white; padding: 10px; display: inline-block;">Non Valider</a>
              </td>
            </tr>
          </table>

          <p>Cordialement,</p>
        </body>
        </html>
      `,
      attachments: attachment, 
    };
    const emailResponse = await mailModel.sendEmail(emailData);
    console.log("Email sent successfully. Returned data: ", emailResponse);
    res.send({ message: "Email sent" });

  } catch (error) {
    console.error("Error while sending email: ", error);
    res.status(500).send({ message: error.message || "Internal Server Error" });
  }
};



exports.sendFac = (req, res) => {
  // Récupérez les données envoyées depuis le frontend
  const {
    ligneFacture,
    lettre,
    TVA,
    TTC,
    HT,
    timbre,
    adresse,
    mat_fisc,
    client,
    mail,
  } = req.body;



  // Assurez-vous que toutes les données nécessaires sont présentes
  if (
    !ligneFacture ||
    !lettre ||
    !TVA ||
    !TTC ||
    !HT ||
    !timbre ||
    !adresse ||
    !mat_fisc ||
    !client ||
    !mail
  ) {
    return res.status(400).json({ message: "Missing data in request body" });
  }

  const htmlContent = generateHTML(req.body);

  // Options pour la génération du PDF
  const options = {
    format: "Letter",
    border: {
      top: "0.5in",
      right: "0.5in",
      bottom: "0.5in",
      left: "0.5in",
    },
  };

  pdf
    .create(htmlContent, options)
    .toFile(`factures/facture_${req.body.id}.pdf`, async (err, response) => {
      if (err) {
        console.error("Erreur lors de la génération du PDF : ", err);
        return res.status(500).json({ message: "Error generating PDF" });
      }

      console.log("PDF généré avec succès : ", response.filename);

      try {
        // Envoyer l'e-mail avec la facture attachée
        await exports.sendEmailFacture(req, res);
      } catch (error) {
        console.error("Error sending email: ", error);
        res.status(500).json({ message: "Error sending email" });
      }
    });
};

function generateHTML(data) {
  const currentDate = new Date();
  const jour = currentDate.getDate().toString().padStart(2, "0"); // Obtenez le jour avec padding
  const mois = (currentDate.getMonth() + 1).toString().padStart(2, "0"); // Obtenez le mois avec padding (les mois sont indexés à partir de 0)
  const annee = currentDate.getFullYear(); // Obtenez l'année
  const formattedTVA = parseFloat(data.TVA).toFixed(3).replace('.', ',');
  const formattedTimbre = parseFloat(data.timbre).toFixed(3).replace('.', ',');
  const formattedHT = parseFloat(data.HT).toFixed(3).replace('.', ',');
  const formattedTTC = parseFloat(data.TTC).toFixed(3).replace('.', ',');

  const formattedDate = `${jour}/${mois}/${annee}`;  // Formatage des données en HTML
  const tableRowsHTML = data.ligneFacture
    .map(
      (item) => `
    <tr style="border: 1px solid black;">
      <td style="border: 1px solid black;">${item.designation}</td>
      <td style="border: 1px solid black;">${item.unite}</td>
      <td style="border: 1px solid black;">${item.volume}</td>
      <td style="border: 1px solid black;">${item.tva}%</td>
      <td style="border: 1px solid black;">${parseFloat(item.prix).toFixed(3).replace('.', ',')}</td>
    </tr>`
    )
    .join("");

  // Contenu HTML de la facture
  const htmlContent = `
  <html>
  <head>
    <style>
      table {
        border-collapse: collapse;
        width: 95%;
        margin: auto;
      }
      th, td {
        padding: 8px;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <table style="width: 100%; border-collapse: collapse;">
      <tr>
        <td style="width: 50%; vertical-align: top;">
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
             
              <td style="vertical-align: top;">
                <h3 style="text-align: left !important;"><b>STE ZEN LOGISTIC</b></h3>
                <p style="text-align: left !important;"><b>MF:</b> 1557718/G/A/M/000</p>
                <p style="text-align: left !important;"><b>ADRESSE :</b> Route Gremda km2,5 SFAX</p>
                <p style="text-align: left !important;"><b>TELEPHONE :</b> 70 147 680</p>
                <p style="text-align: left !important;"><b>FAX :</b> 74 870 248</p>
              </td>
            </tr>
          </table>
        </td>
        <td style="width: 50%; vertical-align: top;">
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="text-align: center;">
                <h3 style="font-weight: bold;">Pré-FACTURE</h3>
              </td>
            </tr>
            <tr>
              <td style="border: solid; padding: 10px;">
                <p><b>Client :</b> ${data.client}</p>
                <p><b>Adresse:</b> ${data.adresse}</p>
                <p><b>M.F :</b> ${data.mat_fisc}</p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>

    <div ><h4 style="text-align: center !important;"><b>Pré-Facture N° ${
      data.code
    }</b></h4></div>
    <div style="display: flex; justify-content: center;text-align: center !important;"><h5><b> DATE :</b> ${formattedDate} <br> </h5></div>

    <table>
      <thead>
        <tr>
          <th style="border: 1px solid black;">Désignation</th>
          <th style="border: 1px solid black;">Unité</th>
          <th style="border: 1px solid black;">Quantité</th>
          <th style="border: 1px solid black;">TVA</th>
          <th style="border: 1px solid black;">Total HT</th>
        </tr>
      </thead>
      <tbody>
        ${tableRowsHTML}
      </tbody>
    </table>
     <div class="table-container" style="margin-top: 30px;margin-right: 15px width: 100%; text-align: right;">
     <table style="margin-top: 30px; margin-right: 15px; width: 300px;">
    <tr style="border: 1px solid black;">
        <th style="border: 1px solid black;">Total HT :</th>
        <td style="border: 1px solid black;">${formattedHT}</td>
    </tr>
    <tr style="border: 1px solid black;">
        <th style="border: 1px solid black;">Total TVA :</th>
        <td style="border: 1px solid black;">${formattedTVA}</td>
    </tr>
    <tr style="border: 1px solid black;">
        <th style="border: 1px solid black;">Timbre :</th>
        <td style="border: 1px solid black;">${formattedTimbre}</td>
    </tr>
    <tr style="border: 1px solid black;">
        <th style="border: 1px solid black;">Total TTC :</th>
        <td style="border: 1px solid black;">${formattedTTC}</td>
    </tr>
</table>

    </div>
    <div style="display: flex; justify-content: center; margin-bottom: 30px;">
      <h5><b>Arrêtée la présente Pré-Facture à la somme De :</b></h5><br>
      <p>${data.lettre}</p>
    </div>
  </body>
</html>
  `;

  return htmlContent;
}

exports.updateTrue = (req, result) => {

  const query = { id: decryptId(req.params.factureId) };

  Facture.updateTrue(query.id, (err, data) => {
  });
};

exports.updateFalse = (req, result) => {

  const query = { id: decryptId(req.params.factureId) };

  Facture.updateFalse(query.id, (err, data) => {
    // if (err) {
    //   result(err, null);
    //   return;
    // }

    // if (data) {
    //   result(null, data);
    //   return;
    // }

   // result({ kind: "not_found" }, null);
  });
};




exports.createFactureFlux = async (req, res) => {

  try {
    if (!req.body) {
      res.status(400).send({
        message: "Content can not be empty",
      });
      return;
    }

    const facture = new Facture({
      id_client: req.body.id_client,
      created_at: new Date(),
      tot_facture: req.body.tot_facture,
      tot_factureTTC: req.body.tot_factureTTC,
      created_by: req.body.created_by,
      ligne_ids: req.body.ligne_ids,
      adresse: req.body.adresse,
      tva:req.body.tva,
      timbre:req.body.timbre,
      type:'flux',
      invoice_date:req.body.invoice_date
    });

    Facture.createFactureFlux(facture, (err, data) => {
      if (err) {
        res.status(500).send({
          message:
            err.message || "Some error occurred while creating the facture.",
        });
        return;
      }
      res.send(data);
    });
  } catch (error) {
    console.error(error);
    res.status(500).send({
      message: "Internal server error",
    });
  }
};


exports.createFactureEntreposage = async (req, res) => {

  try {
    if (!req.body) {
      res.status(400).send({
        message: "Content can not be empty",
      });
      return;
    }

    const facture = new Facture({
      id_client: req.body.id_client,
      created_at: new Date(),
      tot_facture: req.body.tot_facture,
      tot_factureTTC: req.body.tot_factureTTC,
      created_by: req.body.created_by,
      ligne_ids: req.body.ligne_ids,
      adresse: req.body.adresse,
      tva:req.body.tva,
      timbre:req.body.timbre,
      type:'Entreposage',
      invoice_date:req.body.invoice_date
    });

    Facture.createFactureEntreposage(facture, (err, data) => {
      if (err) {
        res.status(500).send({
          message:
            err.message || "Some error occurred while creating the facture.",
        });
        return;
      }
      res.send(data);
    });
  } catch (error) {
    console.error(error);
    res.status(500).send({
      message: "Internal server error",
    });
  }
};



exports.sendToSage = (req, res) => {
  // Extraire les données du corps de la requête
  const factureData = req.body;

  // Appeler la méthode du modèle pour traiter la facture
  Facture.sendToSage(factureData, (err, data) => {
      if (err) {
          // Envoyer la réponse d'erreur au client
          return res.status(500).json({
              message: "Erreur lors de l'envoi à Sage",
              error: err
          });
      }

      // Si des données sont retournées, cela signifie que tout s'est bien passé
      if (data) {
          return res.status(200).json({
              message: "Facture envoyée avec succès",
              data: data
          });
      }

      // Si aucune donnée n'est trouvée ou mise à jour
      return res.status(404).json({
          message: "Facture non trouvée"
      });
  });
};



exports.findInvoiceByDate = (req, res) => {
  if (!req.body.date) {
    return res.status(400).send({
      message: "Date is required!",
    });
  }

  Facture.findInvoiceByDate(req.body.date, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: "No invoices found for the given date.",
        });
      } else {
        res.status(500).send({
          message: "Error retrieving invoices.",
        });
      }
    } else {
      res.send(data);
    }
  });
};


exports.findFactureByClientAndDate = (req, res) => {
  const { id_client, invoiced_date } = req.query;
  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.pageSize) || 10;
  const offset = (page - 1) * pageSize;

  if (!id_client || !invoiced_date) {
    return res.status(400).send({
      message: "id_client et invoiced_date sont requis",
    });
  }

  Facture.findFactureByClientAndDate(id_client, invoiced_date, page, pageSize, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({ message: "Aucune facture trouvée" });
      } else {
        res.status(500).send({ message: "Erreur lors de la récupération des factures" });
      }
    } else {
      res.send(data);
    }
  });
};