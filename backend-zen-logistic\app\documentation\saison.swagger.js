/**
 * @swagger
 * tags:
 *   name: Saisons
 *   description: Opérations liées à la gestion des saisons
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Saison:
 *       type: object
 *       required:
 *         - nom
 *         - value
 *       properties:
 *         id:
 *           type: integer
 *           description: Identifiant unique de la saison
 *         nom:
 *           type: string
 *           description: Nom de la saison
 *         value:
 *           type: string
 *           description: Valeur associée à la saison
 */

/**
 * @swagger
 * /api/saison:
 *   get:
 *     tags: [Saisons]
 *     summary: Récupérer toutes les saisons
 *     responses:
 *       200:
 *         description: Liste de toutes les saisons récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur
 */

/**
 * @swagger
 * /api/saison/{id}/{qte}:
 *   put:
 *     tags: [Saisons]
 *     summary: Mettre à jour la quantité d'une saison
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID de la saison à mettre à jour
 *         schema:
 *           type: integer
 *       - in: path
 *         name: qte
 *         required: true
 *         description: Nouvelle quantité à définir pour la saison
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Saison mise à jour avec succès
 *       404:
 *         description: Saison non trouvée pour cet identifiant
 *       500:
 *         description: Erreur interne du serveur
 */
