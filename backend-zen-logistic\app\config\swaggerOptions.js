// swaggerOptions.js
const path = require('path');

const options = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'Mon API Express',
        version: '1.0.0',
        description: 'Documentation Swagger de l\'API',
      },
      servers: [
        {
          url: 'http://localhost:3000', // adapte si besoin
        },
      ],
    },
    apis: [path.join(__dirname, '../documentation/*.swagger.js')],
     // chemins vers tes fichiers de routes contenant les commentaires Swagger
  };
  
  module.exports = options;
  