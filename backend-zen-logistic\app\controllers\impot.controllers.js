const Impot = require ("../models/impot.model.js") ; 


exports.getAll = (req, res) => {
    Impot.getAll((err, data) => {
      if (err)
        res.status(500).send({
          message:
            err.message || "Some error occurred while retrieving Impot."
        });
      else res.send(data);
    });
   
  };

 
  exports.updateById = (req, res) => {
    const id = req.params.id;
    const updatedFields = req.body; // Récupérer les champs mis à jour depuis le corps de la requête
  
    // Vérifiez si des champs sont vides ou manquants avant la mise à jour
    if (!updatedFields || Object.keys(updatedFields).length === 0) {
      return res.status(400).send({
        message: "Le contenu ne peut pas être vide"
      });
    }
  
    Impot.updateById(id, updatedFields, (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          return res.status(404).send({
            message: `Pas d'enregistrement trouvé avec l'ID ${id}.`
          });
        }
        res.status(500).send({
          message: err.message || "Une erreur est survenue lors de la mise à jour de l'enregistrement."
        });
      } else {
        res.send({
          message: "Mise à jour réussie.",
          data
        });
      }
    });
  };
  