import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class BillingTypeService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Get all billing types
   * @returns Observable with billing types list
   * @originalName getAllTypeFacturation
   */
  getAllBillingTypes(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiURL}typeFacturation`, httpOptions);
  }
}
