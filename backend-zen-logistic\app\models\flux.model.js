
const sql = require("./db.js");
const Flux = function(flux) {
    this.id= flux.id ;
    this.id_ste = flux.id_ste ;
    this.nom_societe = flux.nom_societe ;
    this.nom_depot = flux.nom_depot ;
    this.date = flux.date ;
    this.qte = flux.qte ;
    this.id_facture = flux.id_facture ;

    
  } ;



  Flux.getAll = result => {
    sql.query("SELECT * FROM flux", (err, res) => {
      if (err) {
        console.log("SQL error: ", err);
        result(err, null);
        return;
      }
  
      console.log("flux: ", res);
      result(null, res);
    });
  };


  Flux.AllBrand = result => {
    sql.query("SELECT * FROM entreprise", (err, res) => {
      if (err) {
        console.log("SQL error: ", err);
        result(err, null);
        return;
      }
  
      console.log("flux: ", res);
      result(null, res);
    });
  };


  Flux.AddFormula = (data, result) => {
    // Requête d'insertion dans la table formula
    const query = `
      INSERT INTO formula (
        invoiced_for,
  invoiced_name,
  nature,
  id_em_brand,
  name_em_brand,
  type_em,
  id_em_wharehouse,
  name_em_wharehouse,
  id_rec_brand,
  name_rec_brand,
  type_rec,
  id_rec_wharehouse,
  name_rec_wharehouse

      ) VALUES (
        ?, ?, ?, 
        ?, ?, ?, 
        ?, ?, ?, 
        ?, ?, ?,?
      )`;
  console.log(query)
    // Exécution de la requête avec les données passées en paramètre
    sql.query(query, [
      data.invoiced_for,
      data.invoiced_name,
      data.nature,
      data.id_em_brand,
      data.name_em_brand,
      data.type_em,
      data.id_em_wharehouse,
      data.name_em_wharehouse,
      data.id_rec_brand,
      data.name_rec_brand,
      data.type_rec,
      data.id_rec_wharehouse,
      data.name_rec_wharehouse,
    ], (err, res) => {
      if (err) {
        console.log("SQL error: ", err);
        result(err, null);
        return;
      }
  
      console.log("Formula added: ", res);
      result(null, res);
    });
  };
  



  Flux.getAllFormula = result => {
    sql.query("SELECT * FROM formula WHERE enabled=true", (err, res) => {
      if (err) {
        console.log("SQL error: ", err);
        result(err, null);
        return;
      }
  
      console.log("flux: ", res);
      result(null, res);
    });
  };

  

  Flux.disableFormula = (id, result) => {
    sql.query("UPDATE formula SET enabled = false WHERE id = ?", [id], (err, res) => {
        if (err) {
            console.log("SQL Error: ", err);
            result(err, null);
            return;
        }

        if (res.affectedRows === 0) {
            // Aucun enregistrement trouvé avec l'ID spécifié
            result({ kind: "not_found" }, null);
            return;
        }

        result(null, { message: "Formula disabled successfully" });
    });
};


  

  Flux.getByClientAndDate = (id_ste, datedebut, datefin, result) => {
    const query = `
      SELECT * FROM flux 
      WHERE id_ste = ? 
      AND id_facture IS NULL
      AND date BETWEEN ? AND ? AND prix_tot>0;
    `;
  
    sql.query(query, [id_ste, datedebut, datefin], (err, res) => {
      if (err) {
        console.log("SQL error: ", err);
        result(err, null);
        return;
      }
  
      console.log("flux: ", res);
      result(null, res);
    });
  };
  
  Flux.getFluxByidFacture = (id_facture, result) => {
    const query = `
      SELECT * FROM flux 
      WHERE id_facture = ? 
    `;
  
    sql.query(query, [id_facture], (err, res) => {
      if (err) {
        console.log("SQL error: ", err);
        result(err, null);
        return;
      }
  
      console.log("flux: ", res);
      result(null, res);
    });
  };



  
  module.exports = Flux ; 
