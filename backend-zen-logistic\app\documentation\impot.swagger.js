/**
 * @swagger
 * tags:
 *   name: Impot
 *   description: Opérations sur les impôts
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Impot:
 *       type: object
 *       required:
 *         - timbre
 *         - tva_tr
 *         - tva_entreposage
 *         - tva_flux
 *         - prix_flux
 *         - prix_flux_mp
 *         - prix_entreposage_mp
 *         - prix_entreposage
 *       properties:
 *         id:
 *           type: integer
 *           description: ID unique de l'impôt
 *         timbre:
 *           type: number
 *           description: Montant du timbre
 *         tva_tr:
 *           type: number
 *           description: TVA sur les transactions
 *         tva_entreposage:
 *           type: number
 *           description: TVA sur l'entreposage
 *         tva_flux:
 *           type: number
 *           description: TVA sur le flux
 *         prix_flux:
 *           type: number
 *           description: Prix du flux
 *         prix_flux_mp:
 *           type: number
 *           description: Prix du flux pour les matières premières
 *         prix_entreposage_mp:
 *           type: number
 *           description: Prix de l'entreposage pour les matières premières
 *         prix_entreposage:
 *           type: number
 *           description: Prix de l'entreposage
 */

/**
 * @swagger
 * /api/impot:
 *   get:
 *     tags: [Impot]
 *     summary: Récupérer tous les impôts
 *     responses:
 *       200:
 *         description: Liste des impôts récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Impot'
 *       500:
 *         description: Erreur lors de la récupération des impôts

 *   put:
 *     tags: [Impot]
 *     summary: Mettre à jour un impôt par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID de l'impôt à mettre à jour
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Impot'
 *     responses:
 *       200:
 *         description: Mise à jour réussie
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Impot'
 *       400:
 *         description: Le contenu ne peut pas être vide
 *       404:
 *         description: Pas d'enregistrement trouvé avec l'ID spécifié
 *       500:
 *         description: Erreur lors de la mise à jour de l'enregistrement
 */
