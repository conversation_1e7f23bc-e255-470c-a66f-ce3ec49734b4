{"version": 3, "sources": ["../../../../../../node_modules/@angular/core/fesm2022/rxjs-interop.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.3\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { assertInInjectionContext, inject, DestroyRef, RuntimeError, Injector, assertNotInReactiveContext, signal, PendingTasks } from './root_effect_scheduler-DCy1y1b8.mjs';\nimport { getOutputDestroyRef, effect, untracked, computed, resource, encapsulateResourceError } from './resource-BarKSp_3.mjs';\nimport './primitives/di.mjs';\nimport './signal-nCiHhWf6.mjs';\nimport '@angular/core/primitives/di';\nimport '@angular/core/primitives/signals';\nimport './untracked-DmD_2MlC.mjs';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @publicApi 19.0\n */\nfunction takeUntilDestroyed(destroyRef) {\n    if (!destroyRef) {\n        ngDevMode && assertInInjectionContext(takeUntilDestroyed);\n        destroyRef = inject(DestroyRef);\n    }\n    const destroyed$ = new Observable((subscriber) => {\n        if (destroyRef.destroyed) {\n            subscriber.next();\n            return;\n        }\n        const unregisterFn = destroyRef.onDestroy(subscriber.next.bind(subscriber));\n        return unregisterFn;\n    });\n    return (source) => {\n        return source.pipe(takeUntil(destroyed$));\n    };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n    source;\n    destroyed = false;\n    destroyRef = inject(DestroyRef);\n    constructor(source) {\n        this.source = source;\n        this.destroyRef.onDestroy(() => {\n            this.destroyed = true;\n        });\n    }\n    subscribe(callbackFn) {\n        if (this.destroyed) {\n            throw new RuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected subscription to destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.');\n        }\n        // Stop yielding more values when the directive/component is already destroyed.\n        const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n            next: (value) => callbackFn(value),\n        });\n        return {\n            unsubscribe: () => subscription.unsubscribe(),\n        };\n    }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @publicApi 19.0\n */\nfunction outputFromObservable(observable, opts) {\n    ngDevMode && assertInInjectionContext(outputFromObservable);\n    return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @publicApi 19.0\n */\nfunction outputToObservable(ref) {\n    const destroyRef = getOutputDestroyRef(ref);\n    return new Observable((observer) => {\n        // Complete the observable upon directive/component destroy.\n        // Note: May be `undefined` if an `EventEmitter` is declared outside\n        // of an injection context.\n        const unregisterOnDestroy = destroyRef?.onDestroy(() => observer.complete());\n        const subscription = ref.subscribe((v) => observer.next(v));\n        return () => {\n            subscription.unsubscribe();\n            unregisterOnDestroy?.();\n        };\n    });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @publicApi 20.0\n */\nfunction toObservable(source, options) {\n    if (ngDevMode && !options?.injector) {\n        assertInInjectionContext(toObservable);\n    }\n    const injector = options?.injector ?? inject(Injector);\n    const subject = new ReplaySubject(1);\n    const watcher = effect(() => {\n        let value;\n        try {\n            value = source();\n        }\n        catch (err) {\n            untracked(() => subject.error(err));\n            return;\n        }\n        untracked(() => subject.next(value));\n    }, { injector, manualCleanup: true });\n    injector.get(DestroyRef).onDestroy(() => {\n        watcher.destroy();\n        subject.complete();\n    });\n    return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n */\nfunction toSignal(source, options) {\n    typeof ngDevMode !== 'undefined' &&\n        ngDevMode &&\n        assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' +\n            'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n    const requiresCleanup = !options?.manualCleanup;\n    if (ngDevMode && requiresCleanup && !options?.injector) {\n        assertInInjectionContext(toSignal);\n    }\n    const cleanupRef = requiresCleanup\n        ? (options?.injector?.get(DestroyRef) ?? inject(DestroyRef))\n        : null;\n    const equal = makeToSignalEqual(options?.equal);\n    // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n    // the same - the returned signal gives values of type `T`.\n    let state;\n    if (options?.requireSync) {\n        // Initially the signal is in a `NoValue` state.\n        state = signal({ kind: 0 /* StateKind.NoValue */ }, { equal });\n    }\n    else {\n        // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n        state = signal({ kind: 1 /* StateKind.Value */, value: options?.initialValue }, { equal });\n    }\n    let destroyUnregisterFn;\n    // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n    // this, we would subscribe to the observable outside of the current reactive context, avoiding\n    // that side-effect signal reads/writes are attribute to the current consumer. The current\n    // consumer only needs to be notified when the `state` signal changes through the observable\n    // subscription. Additional context (related to async pipe):\n    // https://github.com/angular/angular/pull/50522.\n    const sub = source.subscribe({\n        next: (value) => state.set({ kind: 1 /* StateKind.Value */, value }),\n        error: (error) => {\n            state.set({ kind: 2 /* StateKind.Error */, error });\n            destroyUnregisterFn?.();\n        },\n        complete: () => {\n            destroyUnregisterFn?.();\n        },\n        // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n        // \"complete\".\n    });\n    if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n        throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n    // Unsubscribe when the current context is destroyed, if requested.\n    destroyUnregisterFn = cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n    // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n    // to either values or errors.\n    return computed(() => {\n        const current = state();\n        switch (current.kind) {\n            case 1 /* StateKind.Value */:\n                return current.value;\n            case 2 /* StateKind.Error */:\n                throw current.error;\n            case 0 /* StateKind.NoValue */:\n                // This shouldn't really happen because the error is thrown on creation.\n                throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n        }\n    }, { equal: options?.equal });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n    return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Operator which makes the application unstable until the observable emits, completes, errors, or is unsubscribed.\n *\n * Use this operator in observables whose subscriptions are important for rendering and should be included in SSR serialization.\n *\n * @param injector The `Injector` to use during creation. If this is not provided, the current injection context will be used instead (via `inject`).\n *\n * @developerPreview 20.0\n */\nfunction pendingUntilEvent(injector) {\n    if (injector === undefined) {\n        ngDevMode && assertInInjectionContext(pendingUntilEvent);\n        injector = inject(Injector);\n    }\n    const taskService = injector.get(PendingTasks);\n    return (sourceObservable) => {\n        return new Observable((originalSubscriber) => {\n            // create a new task on subscription\n            const removeTask = taskService.add();\n            let cleanedUp = false;\n            function cleanupTask() {\n                if (cleanedUp) {\n                    return;\n                }\n                removeTask();\n                cleanedUp = true;\n            }\n            const innerSubscription = sourceObservable.subscribe({\n                next: (v) => {\n                    originalSubscriber.next(v);\n                    cleanupTask();\n                },\n                complete: () => {\n                    originalSubscriber.complete();\n                    cleanupTask();\n                },\n                error: (e) => {\n                    originalSubscriber.error(e);\n                    cleanupTask();\n                },\n            });\n            innerSubscription.add(() => {\n                originalSubscriber.unsubscribe();\n                cleanupTask();\n            });\n            return innerSubscription;\n        });\n    };\n}\n\nfunction rxResource(opts) {\n    if (ngDevMode && !opts?.injector) {\n        assertInInjectionContext(rxResource);\n    }\n    return resource({\n        ...opts,\n        loader: undefined,\n        stream: (params) => {\n            let sub;\n            // Track the abort listener so it can be removed if the Observable completes (as a memory\n            // optimization).\n            const onAbort = () => sub.unsubscribe();\n            params.abortSignal.addEventListener('abort', onAbort);\n            // Start off stream as undefined.\n            const stream = signal({ value: undefined });\n            let resolve;\n            const promise = new Promise((r) => (resolve = r));\n            function send(value) {\n                stream.set(value);\n                resolve?.(stream);\n                resolve = undefined;\n            }\n            // TODO(alxhub): remove after g3 updated to rename loader -> stream\n            const streamFn = opts.stream ?? opts.loader;\n            if (streamFn === undefined) {\n                throw new RuntimeError(990 /* ɵRuntimeErrorCode.MUST_PROVIDE_STREAM_OPTION */, ngDevMode && `Must provide \\`stream\\` option.`);\n            }\n            sub = streamFn(params).subscribe({\n                next: (value) => send({ value }),\n                error: (error) => {\n                    send({ error: encapsulateResourceError(error) });\n                    params.abortSignal.removeEventListener('abort', onAbort);\n                },\n                complete: () => {\n                    if (resolve) {\n                        send({\n                            error: new RuntimeError(991 /* ɵRuntimeErrorCode.RESOURCE_COMPLETED_BEFORE_PRODUCING_VALUE */, ngDevMode && 'Resource completed before producing a value'),\n                        });\n                    }\n                    params.abortSignal.removeEventListener('abort', onAbort);\n                },\n            });\n            return promise;\n        },\n    });\n}\n\nexport { outputFromObservable, outputToObservable, pendingUntilEvent, rxResource, takeUntilDestroyed, toObservable, toSignal };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,SAAS,mBAAmB,YAAY;AACpC,MAAI,CAAC,YAAY;AACb,iBAAa,yBAAyB,kBAAkB;AACxD,iBAAa,OAAO,UAAU;AAAA,EAClC;AACA,QAAM,aAAa,IAAI,WAAW,CAAC,eAAe;AAC9C,QAAI,WAAW,WAAW;AACtB,iBAAW,KAAK;AAChB;AAAA,IACJ;AACA,UAAM,eAAe,WAAW,UAAU,WAAW,KAAK,KAAK,UAAU,CAAC;AAC1E,WAAO;AAAA,EACX,CAAC;AACD,SAAO,CAAC,WAAW;AACf,WAAO,OAAO,KAAK,UAAU,UAAU,CAAC;AAAA,EAC5C;AACJ;AAQA,IAAM,0BAAN,MAA8B;AAAA,EAC1B;AAAA,EACA,YAAY;AAAA,EACZ,aAAa,OAAO,UAAU;AAAA,EAC9B,YAAY,QAAQ;AAChB,SAAK,SAAS;AACd,SAAK,WAAW,UAAU,MAAM;AAC5B,WAAK,YAAY;AAAA,IACrB,CAAC;AAAA,EACL;AAAA,EACA,UAAU,YAAY;AAClB,QAAI,KAAK,WAAW;AAChB,YAAM,IAAI,aAAa,KAAkD,aACrE,gGACkD;AAAA,IAC1D;AAEA,UAAM,eAAe,KAAK,OAAO,KAAK,mBAAmB,KAAK,UAAU,CAAC,EAAE,UAAU;AAAA,MACjF,MAAM,CAAC,UAAU,WAAW,KAAK;AAAA,IACrC,CAAC;AACD,WAAO;AAAA,MACH,aAAa,MAAM,aAAa,YAAY;AAAA,IAChD;AAAA,EACJ;AACJ;AAyBA,SAAS,qBAAqB,YAAY,MAAM;AAC5C,eAAa,yBAAyB,oBAAoB;AAC1D,SAAO,IAAI,wBAAwB,UAAU;AACjD;AAUA,SAAS,mBAAmB,KAAK;AAC7B,QAAM,aAAa,oBAAoB,GAAG;AAC1C,SAAO,IAAI,WAAW,CAAC,aAAa;AAIhC,UAAM,sBAAsB,YAAY,UAAU,MAAM,SAAS,SAAS,CAAC;AAC3E,UAAM,eAAe,IAAI,UAAU,CAAC,MAAM,SAAS,KAAK,CAAC,CAAC;AAC1D,WAAO,MAAM;AACT,mBAAa,YAAY;AACzB,4BAAsB;AAAA,IAC1B;AAAA,EACJ,CAAC;AACL;AAWA,SAAS,aAAa,QAAQ,SAAS;AACnC,MAAI,aAAa,CAAC,SAAS,UAAU;AACjC,6BAAyB,YAAY;AAAA,EACzC;AACA,QAAM,WAAW,SAAS,YAAY,OAAO,QAAQ;AACrD,QAAM,UAAU,IAAI,cAAc,CAAC;AACnC,QAAM,UAAU,OAAO,MAAM;AACzB,QAAI;AACJ,QAAI;AACA,cAAQ,OAAO;AAAA,IACnB,SACO,KAAK;AACR,gBAAU,MAAM,QAAQ,MAAM,GAAG,CAAC;AAClC;AAAA,IACJ;AACA,cAAU,MAAM,QAAQ,KAAK,KAAK,CAAC;AAAA,EACvC,GAAG,EAAE,UAAU,eAAe,KAAK,CAAC;AACpC,WAAS,IAAI,UAAU,EAAE,UAAU,MAAM;AACrC,YAAQ,QAAQ;AAChB,YAAQ,SAAS;AAAA,EACrB,CAAC;AACD,SAAO,QAAQ,aAAa;AAChC;AAwBA,SAAS,SAAS,QAAQ,SAAS;AAC/B,SAAO,cAAc,eACjB,aACA,2BAA2B,UAAU,6JACmE;AAC5G,QAAM,kBAAkB,CAAC,SAAS;AAClC,MAAI,aAAa,mBAAmB,CAAC,SAAS,UAAU;AACpD,6BAAyB,QAAQ;AAAA,EACrC;AACA,QAAM,aAAa,kBACZ,SAAS,UAAU,IAAI,UAAU,KAAK,OAAO,UAAU,IACxD;AACN,QAAM,QAAQ,kBAAkB,SAAS,KAAK;AAG9C,MAAI;AACJ,MAAI,SAAS,aAAa;AAEtB,YAAQ,OAAO;AAAA,MAAE,MAAM;AAAA;AAAA,IAA0B,GAAG,EAAE,MAAM,CAAC;AAAA,EACjE,OACK;AAED,YAAQ,OAAO,EAAE,MAAM,GAAyB,OAAO,SAAS,aAAa,GAAG,EAAE,MAAM,CAAC;AAAA,EAC7F;AACA,MAAI;AAOJ,QAAM,MAAM,OAAO,UAAU;AAAA,IACzB,MAAM,CAAC,UAAU,MAAM,IAAI,EAAE,MAAM,GAAyB,MAAM,CAAC;AAAA,IACnE,OAAO,CAAC,UAAU;AACd,YAAM,IAAI,EAAE,MAAM,GAAyB,MAAM,CAAC;AAClD,4BAAsB;AAAA,IAC1B;AAAA,IACA,UAAU,MAAM;AACZ,4BAAsB;AAAA,IAC1B;AAAA;AAAA;AAAA,EAGJ,CAAC;AACD,MAAI,SAAS,eAAe,MAAM,EAAE,SAAS,GAA2B;AACpE,UAAM,IAAI,aAAa,MAA6D,OAAO,cAAc,eAAe,cACpH,qFAAqF;AAAA,EAC7F;AAEA,wBAAsB,YAAY,UAAU,IAAI,YAAY,KAAK,GAAG,CAAC;AAGrE,SAAO,SAAS,MAAM;AAClB,UAAM,UAAU,MAAM;AACtB,YAAQ,QAAQ,MAAM;AAAA,MAClB,KAAK;AACD,eAAO,QAAQ;AAAA,MACnB,KAAK;AACD,cAAM,QAAQ;AAAA,MAClB,KAAK;AAED,cAAM,IAAI,aAAa,MAA6D,OAAO,cAAc,eAAe,cACpH,qFAAqF;AAAA,IACjG;AAAA,EACJ,GAAG,EAAE,OAAO,SAAS,MAAM,CAAC;AAChC;AACA,SAAS,kBAAkB,eAAe,OAAO,IAAI;AACjD,SAAO,CAAC,GAAG,MAAM,EAAE,SAAS,KAA2B,EAAE,SAAS,KAA2B,aAAa,EAAE,OAAO,EAAE,KAAK;AAC9H;AAWA,SAAS,kBAAkB,UAAU;AACjC,MAAI,aAAa,QAAW;AACxB,iBAAa,yBAAyB,iBAAiB;AACvD,eAAW,OAAO,QAAQ;AAAA,EAC9B;AACA,QAAM,cAAc,SAAS,IAAI,YAAY;AAC7C,SAAO,CAAC,qBAAqB;AACzB,WAAO,IAAI,WAAW,CAAC,uBAAuB;AAE1C,YAAM,aAAa,YAAY,IAAI;AACnC,UAAI,YAAY;AAChB,eAAS,cAAc;AACnB,YAAI,WAAW;AACX;AAAA,QACJ;AACA,mBAAW;AACX,oBAAY;AAAA,MAChB;AACA,YAAM,oBAAoB,iBAAiB,UAAU;AAAA,QACjD,MAAM,CAAC,MAAM;AACT,6BAAmB,KAAK,CAAC;AACzB,sBAAY;AAAA,QAChB;AAAA,QACA,UAAU,MAAM;AACZ,6BAAmB,SAAS;AAC5B,sBAAY;AAAA,QAChB;AAAA,QACA,OAAO,CAAC,MAAM;AACV,6BAAmB,MAAM,CAAC;AAC1B,sBAAY;AAAA,QAChB;AAAA,MACJ,CAAC;AACD,wBAAkB,IAAI,MAAM;AACxB,2BAAmB,YAAY;AAC/B,oBAAY;AAAA,MAChB,CAAC;AACD,aAAO;AAAA,IACX,CAAC;AAAA,EACL;AACJ;AAEA,SAAS,WAAW,MAAM;AACtB,MAAI,aAAa,CAAC,MAAM,UAAU;AAC9B,6BAAyB,UAAU;AAAA,EACvC;AACA,SAAO,SAAS,iCACT,OADS;AAAA,IAEZ,QAAQ;AAAA,IACR,QAAQ,CAAC,WAAW;AAChB,UAAI;AAGJ,YAAM,UAAU,MAAM,IAAI,YAAY;AACtC,aAAO,YAAY,iBAAiB,SAAS,OAAO;AAEpD,YAAM,SAAS,OAAO,EAAE,OAAO,OAAU,CAAC;AAC1C,UAAI;AACJ,YAAM,UAAU,IAAI,QAAQ,CAAC,MAAO,UAAU,CAAE;AAChD,eAAS,KAAK,OAAO;AACjB,eAAO,IAAI,KAAK;AAChB,kBAAU,MAAM;AAChB,kBAAU;AAAA,MACd;AAEA,YAAM,WAAW,KAAK,UAAU,KAAK;AACrC,UAAI,aAAa,QAAW;AACxB,cAAM,IAAI,aAAa,KAAwD,aAAa,iCAAiC;AAAA,MACjI;AACA,YAAM,SAAS,MAAM,EAAE,UAAU;AAAA,QAC7B,MAAM,CAAC,UAAU,KAAK,EAAE,MAAM,CAAC;AAAA,QAC/B,OAAO,CAAC,UAAU;AACd,eAAK,EAAE,OAAO,yBAAyB,KAAK,EAAE,CAAC;AAC/C,iBAAO,YAAY,oBAAoB,SAAS,OAAO;AAAA,QAC3D;AAAA,QACA,UAAU,MAAM;AACZ,cAAI,SAAS;AACT,iBAAK;AAAA,cACD,OAAO,IAAI,aAAa,KAAuE,aAAa,6CAA6C;AAAA,YAC7J,CAAC;AAAA,UACL;AACA,iBAAO,YAAY,oBAAoB,SAAS,OAAO;AAAA,QAC3D;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AAAA,EACJ,EAAC;AACL;", "names": []}