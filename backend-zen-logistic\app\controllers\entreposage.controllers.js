const Entreposage = require ("../models/entreposage.model.js") ; 


exports.getAll = (req, res) => {
    Entreposage.getAll((err, data) => {
      if (err)
        res.status(500).send({
          message:
            err.message || "Some error occurred while retrieving Impot."
        });
      else res.send(data);
    });
   
  };


  exports.getByClientAndDate = (req, res) => {
    const { id_ste, datedebut, datefin } = req.body;
    if (!id_ste || !datedebut || !datefin) {
      return res.status(400).send({
        message: "id_societe, datedebut, and datefin sont obligatoire."
      });
    }
    if (new Date(datedebut) >= new Date(datefin)) {
      return res.status(400).send({
        message: "La date de début doit être inférieure à la date de fin."
      });
    }
    Entreposage.getByClientAndDate(id_ste, datedebut, datefin, (err, data) => {
      if (err) {
        res.status(500).send({
          message: err.message || "Some error occurred while retrieving data."
        });
      } else {
        res.send(data);
      }
    });
  };

 
  exports.getEntreposageByIdfacture = (req, res) => {
    const id_facture = req.params.id_facture; 

    Entreposage.getEntreposageByIdfacture(id_facture, (err, data) => {
        if (err) {
            res.status(500).send({
                message: err.message || "Une erreur s'est produite lors de la récupération des flux."
            });
        } else {
            res.send(data); 
        }
    });
};