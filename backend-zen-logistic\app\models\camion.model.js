const sql = require("./db.js");

//constructeur  

const Camion = function(camion) {
   // this.id=camion.id ;
    this.type_camion = camion.type_camion;
    this.immatriculation=camion.immatriculation ;
    this.immat_rs_tn = camion.immat_rs_tn;
    this.date_circulation = camion.date_circulation;
    this.poids = camion.poids;
    this.unite_poids= camion.unite_poids;
    this.volume = camion.volume;
    this.nombre_palettes = camion.nombre_palettes;
    this.palette_met_eur = camion.palette_met_eur;
    this.longeur = camion.longeur;
    this.largeur = camion.largeur;
    this.hauteur = camion.hauteur;
    this.image_carte_grise = camion.image_carte_grise;
    this.ajoutee_par = camion.ajoutee_par ; 

  } ;



  // Fuction create camion 

  Camion.create = (newCamion, result) => {
    sql.query("INSERT INTO camion SET ?", newCamion, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      //console.log("created camion: ", { type_camion: res.inserttype_camion, ...newCamion });
      result(null, { type_camion: res.inserttype_camion, ...newCamion });
    });
  };



  //Function get all camion 
  Camion.getAll = result => {
    sql.query(`SELECT 
    C.id,
    C.type_camion,
    C.immatriculation,
    C.immat_rs_tn,
    C.date_circulation,
    C.poids,
    C.unite_poids,
    C.volume,
    C.nombre_palettes,
    C.palette_met_eur,
    C.longeur,
    C.largeur,
    C.hauteur,
    C.image_carte_grise,
    C.ajoutee_par
FROM 
    camion C  
ORDER BY 
    C.ajoutee_par
`, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }
  
      ////console.log("Camion: ", res);
      result(null, res);
    });
  }

  //Function delate Camion with ID
  Camion.remove = (id, result) => {
    sql.query("DELETE FROM camion WHERE id = ?", id, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }
  
      if (res.affectedRows == 0) {
        // not found camion with the id
        result({ kind: "not_found" }, null);
        return;
      }
  
      //console.log("deleted camion with id: ", id);
      result(null, res);
    });
  };


  // function find a camion by ID 
  Camion.findById = (camionId, result) => {
  sql.query(`SELECT * FROM camion WHERE id = ${camionId}`, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.length) {
    //  //console.log("found camion: ", res[0]);
      result(null, res[0]);
      return;
    }

    // not found camion with the id
    result({ kind: "not_found" }, null);
  });
};

//Function get all Camion   
Camion.findCamion =(idUser , result )=> {
  sql.query(`SELECT C.id,C.type_camion,C.immatriculation,C.immat_rs_tn,C.date_circulation,C.poids,C.unite_poids, C.volume,C.nombre_palettes,C.palette_met_eur,C.longeur,C.largeur,C.hauteur,C.image_carte_grise ,C.ajoutee_par,CONCAT(U.prenom,U.nom) as transporteur FROM camion C INNER JOIN utilisateurs U on C.ajoutee_par= U.id WHERE C.ajoutee_par = ${idUser}  ORDER  By C.ajoutee_par `, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result( err) ,null;
      return;
    }

    if (res.length) {
    //  //console.log("found Camion: ");
      result(null, res);
      return;
    }

    // not found Camion with the iduser 
    result({ kind: "not_found" }, null);
  });
};




  module.exports = Camion ; 
