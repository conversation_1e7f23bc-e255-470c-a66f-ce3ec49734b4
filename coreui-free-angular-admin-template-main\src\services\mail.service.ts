import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class MailService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Send expedition mail
   * @param data - Mail data
   * @returns Promise with sending result
   */
  sendExpeditionMail(data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.http.post<any>(`${this.apiURL}mailExpedition`, data, httpOptions)
        .toPromise()
        .then(response => resolve(response))
        .catch(error => reject(error));
    });
  }

  /**
   * Send delivery mail
   * @param data - Mail data
   * @returns Promise with sending result
   */
  sendDeliveryMail(data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.http.post<any>(`${this.apiURL}mailLivraison`, data, httpOptions)
        .toPromise()
        .then(response => resolve(response))
        .catch(error => reject(error));
    });
  }

  /**
   * Send reservation mail
   * @param data - Mail data
   * @returns Promise with sending result
   */
  sendReservationMail(data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.http.post<any>(`${this.apiURL}mailReservation`, data, httpOptions)
        .toPromise()
        .then(response => resolve(response))
        .catch(error => reject(error));
    });
  }

  /**
   * Send message
   * @param data - Message data
   * @returns Promise with sending result
   */
  sendMessage(data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.http.post<any>(`${this.apiURL}messages`, data, httpOptions)
        .toPromise()
        .then(response => resolve(response))
        .catch(error => reject(error));
    });
  }

  /**
   * Send cancellation mail
   * @param data - Cancellation data
   * @returns Promise with sending result
   */
  sendCancellationMail(data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.http.post<any>(`${this.apiURL}mailAnnulation`, data, httpOptions)
        .toPromise()
        .then(response => resolve(response))
        .catch(error => reject(error));
    });
  }

  /**
   * Send reservation departure update mail
   * @param data - Update data
   * @returns Promise with sending result
   */
  sendReservationDepartureUpdateMail(data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.http.post<any>(`${this.apiURL}mailUpdateReservationDepart`, data, httpOptions)
        .toPromise()
        .then(response => resolve(response))
        .catch(error => reject(error));
    });
  }

  /**
   * Send reservation arrival update mail
   * @param data - Update data
   * @returns Promise with sending result
   */
  sendReservationArrivalUpdateMail(data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.http.post<any>(`${this.apiURL}mailUpdateReservationArrivee`, data, httpOptions)
        .toPromise()
        .then(response => resolve(response))
        .catch(error => reject(error));
    });
  }

  /**
   * Send mails from frontend
   * @param data - Mail data
   * @returns Promise with sending result
   */
  sendMailsFromFrontend(data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.http.post<any>(`${this.apiURL}sendMailsFromFront`, data, httpOptions)
        .toPromise()
        .then(response => resolve(response))
        .catch(error => reject(error));
    });
  }
}
