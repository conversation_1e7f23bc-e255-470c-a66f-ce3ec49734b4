const ConditionModel = require("../models/condition.model.js");

exports.create = (req, res) => {
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty"
    });
    return;
  }

  const condition = new ConditionModel({
    nom_condition: req.body.nom_condition,
  });

  ConditionModel.create(condition, (err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Some error occurred while creating the condition."
      });
    } else {
      res.send(data);
    }
  });
};

exports.findAll = (req, res) => {
    ConditionModel.findAll((err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Some error occurred while retrieving condition."
      });
    } else {
      res.send(data);
    }
  });
};

exports.deleteCondition = (req, res) => {
  const conditionId = req.params.id; // Make sure this line is correct and provides a value

  ConditionModel.delete(conditionId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Condition with id ${conditionId} not found.`
        });
      } else {
        res.status(500).send({
          message: err.message || `Some error occurred while deleting the condition with id ${conditionId}.`
        });
      }
    } else {
      res.send({
        message: 'Condition deleted successfully.'
      });
    }
  });
};
