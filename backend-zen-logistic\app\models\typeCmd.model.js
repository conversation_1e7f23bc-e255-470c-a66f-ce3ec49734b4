const sql = require("./db.js");

// Constructor
const TypeCmdModel = function(typeCmd) {
    //console.log("rrrrrrrrr",typeCmd)
  this.nom_type = typeCmd.nom_type;
};

TypeCmdModel.create = (newtypeCmd, result) => {
  sql.query("INSERT INTO typecommande SET ?", newtypeCmd, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    //console.log("created customer: ", { id: res.insertId, ...newtypeCmd });
    result(null, { id: res.insertId, ...newtypeCmd });
  });
};

TypeCmdModel.findAll = (result) => {
    sql.query("SELECT * FROM typecommande", (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(err, null);
            return;
        }

        //console.log("typeCmd: ", res);
        result(null, res);
    });
};

TypeCmdModel.delete = (typeCmdId, result) => {
  sql.query("DELETE FROM typecommande WHERE id = ?", typeCmdId, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      result({ kind: "not_found" }, null);
      return;
    }

    //console.log("Deleted typeCmd with id: ", typeCmdId);
    result(null, res);
  });
};

module.exports = TypeCmdModel;
