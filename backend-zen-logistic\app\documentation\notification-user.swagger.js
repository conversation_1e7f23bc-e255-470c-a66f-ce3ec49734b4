/**
 * @swagger
 * tags:
 *   name: NotificationUser
 *   description: Opérations liées à la gestion des notifications utilisateur
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     NotificationUser:
 *       type: object
 *       required:
 *         - id_user
 *         - onesignal
 *       properties:
 *         id:
 *           type: integer
 *           description: Identifiant unique de la notification
 *         id_user:
 *           type: integer
 *           description: Identifiant de l'utilisateur
 *         onesignal:
 *           type: string
 *           description: Identifiant OneSignal pour les notifications

 */

/**
 * @swagger
 * /api/addUserNot:
 *   post:
 *     tags: [NotificationUser]
 *     summary: Créer une nouvelle notification utilisateur
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/NotificationUser'
 *     responses:
 *       201:
 *         description: Notification utilisateur créée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/NotificationUser'
 *       400:
 *         description: Contenu vide ou données invalides
 *       500:
 *         description: Erreur interne du serveur
 */
