const ErpApi = require("../models/erpApi.model.js");

const checkForDuplicateShipments = (shipments) => {
  let duplicates = [];
  let seenCodes = new Set();  // Utiliser un Set pour suivre les codes déjà vus

  shipments.forEach((shipment, index) => {
    const key = `${shipment.departureCode}-${shipment.arrivalCode}`;
    if (seenCodes.has(key)) {
      duplicates.push(index);  // Ajouter l'index des doublons
    } else {
      seenCodes.add(key);  // Ajouter la clé au Set
    }
  });

  return duplicates;
};

// Valider si tous les champs nécessaires sont présents et valides
const validateShipmentData = (shipment) => {
  if (!shipment.departureCode || !shipment.arrivalCode || !shipment.quantity) {
    return { valid: false, message: "departureCode, arrivalCode et quantity sont requis." };
  }
  // Ajoutez d'autres validations de champs si nécessaire
  return { valid: true };
};

const updateShipment = async (id, shipmentData) => {
  try {
    const updatedShipment = await ErpApi.updateShipment(id, shipmentData, { new: true });

    if (!updatedShipment) {
      throw new Error(`Expédition avec l'ID ${id} non trouvée`);
    }

    return { success: true, updatedShipment };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

exports.createShipments = async (req, res) => {
  try {
    if (!Array.isArray(req.body)) {
      return res.status(400).json({ message: "Invalid input: expected an array of shipments" });
    }

    const shipmentsData = req.body;

    // Vérification des doublons dans les expéditions
    const duplicateIndexes = checkForDuplicateShipments(shipmentsData);
    if (duplicateIndexes.length > 0) {
      return res.status(400).json({
        message: "Doublons détectés dans les expéditions",
        details: `Doublons aux indices: ${duplicateIndexes.join(', ')}`
      });
    }

    const promises = shipmentsData.map(async (shipment) => {
      // Valider les données avant de procéder
      

      if (shipment.id) {
        // Mise à jour de l'expédition avec l'ID
        const updateResult = await updateShipment(shipment.id, shipment);
        return {
          shipmentId: shipment.id,
          message: updateResult.success ? "Expédition mise à jour" : updateResult.error,
        };
      } else {
        const validation = validateShipmentData(shipment);
        if (!validation.valid) {
          return { error: validation.message, shipmentId: shipment.id || null };
        }        return new Promise((resolve, reject) => {
          ErpApi.createShipments([shipment], (err, result) => {
            if (err) {
              resolve({ shipment, error: err });
            } else {
              resolve({ shipment, result });
            }
          });
        });
      }
    });

    const results = await Promise.all(promises);
    
    const errors = results.filter(r => r.error);
    if (errors.length > 0) {
      return res.status(400).json({
        message: "Certaines expéditions n'ont pas pu être traitées",
        details: errors.map(e => e.error),
      });
    }
    
    const processedShipments = results.flatMap(r => {
      // Cas 1 : création avec createdShipments
      if (r.result && Array.isArray(r.result.createdShipments)) {
        return r.result.createdShipments.map(s => ({
          shipmentId: s.shipmentId,
          message: r.result.message || "Expédition traitée avec succès"
        }));
      }
    
      // Cas 2 : mise à jour simple
      if (r.shipmentId) {
        return [{
          shipmentId: r.shipmentId,
          message: r.message || "Expédition traitée avec succès"
        }];
      }
    
      // Aucun résultat exploitable
      return [];
    });
    
    res.status(201).json({
      message: "Les expéditions ont été traitées avec succès",
      processedShipments,
    });
    
  } catch (error) {
    console.error("Error creating or updating shipments:", error);
    res.status(500).json({ message: "Erreur interne du serveur" });
  }
};
