/**
 * @swagger
 * tags:
 *   name: Circuit
 *   description: Gestion des circuits de transport
 */

/**
 * @swagger
 * /api/circuit:
 *   post:
 *     summary: Crée un nouveau circuit
 *     tags: [Circuit]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Circuit'
 *     responses:
 *       200:
 *         description: Circuit créé avec succès
 *       500:
 *         description: Erreur serveur
 *
 *   get:
 *     summary: Récupère tous les circuits
 *     tags: [Circuit]
 *     responses:
 *       200:
 *         description: Liste des circuits récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Circuit'
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/circuit/{id}:
 *   delete:
 *     summary: Supprime un circuit par son ID
 *     tags: [Circuit]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID du circuit à supprimer
 *     responses:
 *       200:
 *         description: Circuit supprimé avec succès
 *       404:
 *         description: Circuit non trouvé
 *       500:
 *         description: Erreur serveur
 *
 *   put:
 *     summary: Met à jour un circuit par son ID
 *     tags: [Circuit]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID du circuit à mettre à jour
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Circuit'
 *     responses:
 *       200:
 *         description: Circuit mis à jour avec succès
 *       404:
 *         description: Circuit non trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/circuit/adjustVoyage:
 *   post:
 *     summary: Ajuste un voyage dans un circuit
 *     tags: [Circuit]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               circuitId:
 *                 type: string
 *               voyageData:
 *                 type: object
 *             example:
 *               circuitId: "12345"
 *               voyageData: { données du voyage  }
 *     responses:
 *       200:
 *         description: Voyage ajusté avec succès
 *       400:
 *         description: Données invalides
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Circuit:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: ID auto-généré du circuit
 *         name:
 *           type: string
 *           description: Nom du circuit
 *         description:
 *           type: string
 *           description: Description du circuit
 *       example:
 *         id: d5fE_asz
 *         name: "Circuit Nord"
 *         description: "Circuit principal pour la région nord"
 */