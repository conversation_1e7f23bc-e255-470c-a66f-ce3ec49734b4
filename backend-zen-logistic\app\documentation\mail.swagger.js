/**
 * @swagger
 * tags:
 *   name: Mail
 *   description: Opérations liées à l'envoi d'emails
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     MailRequest:
 *       type: object
 *       required:
 *         - nom_locale
 *         - Destination
 *         - Date_voyage
 *         - Types_ligne
 *         - Volumes
 *         - qte
 *       properties:
 *         nom_locale:
 *           type: string
 *           description: Nom de l'expéditeur
 *         Destination:
 *           type: string
 *           description: Adresse email du destinataire
 *         Date_voyage:
 *           type: string
 *           format: date
 *           description: Date du voyage
 *         Types_ligne:
 *           type: array
 *           items:
 *             type: string
 *           description: Types de lignes pour la livraison
 *         Volumes:
 *           type: array
 *           items:
 *             type: number
 *           description: Volumes associés aux types de lignes
 *         qte:
 *           type: array
 *           items:
 *             type: integer
 *           description: Quantités associées aux types de lignes
 *         conducteur:
 *           type: string
 *           description: Nom du conducteur
 *         phone:
 *           type: string
 *           description: Numéro de téléphone du conducteur
 */

/**
 * @swagger
 * /api/mailExpedition:
 *   post:
 *     tags: [Mail]
 *     summary: Envoyer un email d'expédition
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MailRequest'
 *     responses:
 *       200:
 *         description: Email d'expédition envoyé avec succès
 *       400:
 *         description: Erreur dans les données fournies
 *       500:
 *         description: Erreur interne du serveur

 * /api/mailLivraison:
 *   post:
 *     tags: [Mail]
 *     summary: Envoyer un email de livraison
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MailRequest'
 *     responses:
 *       200:
 *         description: Email de livraison envoyé avec succès
 *       400:
 *         description: Erreur dans les données fournies
 *       500:
 *         description: Erreur interne du serveur

 * /api/mailAnnulation:
 *   post:
 *     tags: [Mail]
 *     summary: Envoyer un email d'annulation
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - nom_locale
 *               - mail
 *             properties:
 *               nom_locale:
 *                 type: string
 *                 description: Nom de l'expéditeur
 *               mail:
 *                 type: string
 *                 description: Adresse email du destinataire
 *     responses:
 *       200:
 *         description: Email d'annulation envoyé avec succès
 *       400:
 *         description: Erreur dans les données fournies
 *       500:
 *         description: Erreur interne du serveur

 * /api/mailReservation:
 *   post:
 *     tags: [Mail]
 *     summary: Envoyer un email de réservation
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: string
 *     responses:
 *       200:
 *         description: Email de réservation envoyé avec succès
 *       400:
 *         description: Erreur dans les données fournies
 *       500:
 *         description: Erreur interne du serveur

 * /api/sendMailsFromFront:
 *   post:
 *     tags: [Mail]
 *     summary: Envoyer un email depuis le front
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - to
 *               - subject
 *               - htmlContent
 *             properties:
 *               to:
 *                 type: string
 *                 description: Adresse email du destinataire
 *               subject:
 *                 type: string
 *                 description: Sujet de l'email
 *               htmlContent:
 *                 type: string
 *                 description: Contenu HTML de l'email
 *     responses:
 *       200:
 *         description: Email envoyé avec succès
 *       400:
 *         description: Erreur dans les données fournies
 *       500:
 *         description: Erreur interne du serveur

 * /api/mailUpdateReservationDepart:
 *   post:
 *     tags: [Mail]
 *     summary: Envoyer un email de mise à jour de réservation (départ)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: string
 *     responses:
 *       200:
 *         description: Email de mise à jour de réservation envoyé avec succès
 *       400:
 *         description: Erreur dans les données fournies
 *       500:
 *         description: Erreur interne du serveur

 * /api/mailUpdateReservationArrivee:
 *   post:
 *     tags: [Mail]
 *     summary: Envoyer un email de mise à jour de réservation (arrivée)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: string
 *     responses:
 *       200:
 *         description: Email de mise à jour de réservation envoyé avec succès
 *       400:
 *         description: Erreur dans les données fournies
 *       500:
 *         description: Erreur interne du serveur
 */
