const MarchandiseModel = require("../models/marchandise.model.js");

exports.create = (req, res) => {
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty"
    });
    return;
  }

  const marchandise = new MarchandiseModel({
    nom_marchandise: req.body.nom_marchandise,
  });

  MarchandiseModel.create(marchandise, (err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Some error occurred while creating the marchandise."
      });
    } else {
      res.send(data);
    }
  });
};

exports.findAll = (req, res) => {
  MarchandiseModel.findAll((err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Some error occurred while retrieving marchandise."
      });
    } else {
      res.send(data);
    }
  });
};

exports.deleteMarchandise = (req, res) => {
  const marchandiseId = req.params.id; // Make sure this line is correct and provides a value

  MarchandiseModel.delete(marchandiseId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Marchandise with id ${marchandiseId} not found.`
        });
      } else {
        res.status(500).send({
          message: err.message || `Some error occurred while deleting the marchandise with id ${marchandiseId}.`
        });
      }
    } else {
      res.send({
        message: 'Marchandise deleted successfully.'
      });
    }
  });
};
