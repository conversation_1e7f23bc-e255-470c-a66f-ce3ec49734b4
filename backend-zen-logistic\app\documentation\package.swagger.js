/**
 * @swagger
 * tags:
 *   - name: Package
 *     description: Opérations liées à la gestion des colis
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Package:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         barcode:
 *           type: string
 *         departure:
 *           type: string
 *         code_departure:
 *           type: string
 *         arrival:
 *           type: string
 *         code_arrival:
 *           type: string
 *         created_at:
 *           type: string
 *           format: date-time
 *         status:
 *           type: string
 *           enum: [en attente, en transit, livré, partiellement livré]
 *         last_reception:
 *           type: string
 *           format: date-time
 *         name_receptor:
 *           type: string
 *         id_user:
 *           type: integer
 *         code_receiver:
 *           type: string
 * 
 *     PackageStatus:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         receptor_name:
 *           type: string
 *         status:
 *           type: string
 *         created_date:
 *           type: string
 *           format: date-time
 *         id_package:
 *           type: integer
 * 
 *     Barcode:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         base64:
 *           type: string
 * 
 *     PackageStats:
 *       type: object
 *       properties:
 *         jour:
 *           type: string
 *           format: date
 *         statuts:
 *           type: object
 *           properties:
 *             "en attente":
 *               type: integer
 *             "en transit":
 *               type: integer
 *             "livré":
 *               type: integer
 *             "partiellement livré":
 *               type: integer
 */

/**
 * @swagger
 * /api/findAllPackage:
 *   get:
 *     summary: Récupère tous les colis
 *     tags: [Package]
 *     responses:
 *       200:
 *         description: Liste de tous les colis
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Package'
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/transferPackages:
 *   get:
 *     summary: Transfère les colis de Cegid vers la base locale (production seulement)
 *     tags: [Package]
 *     responses:
 *       200:
 *         description: Transfert réussi
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 insertedBarcodes:
 *                   type: array
 *                   items:
 *                     type: string
 *       403:
 *         description: Accès refusé (non production)
 *       404:
 *         description: Aucune donnée trouvée dans Cegid
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/receptionPackage:
 *   post:
 *     summary: Réceptionne des colis
 *     tags: [Package]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - packages
 *               - userId
 *               - userName
 *             properties:
 *               packages:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     barcode:
 *                       type: string
 *               userId:
 *                 type: integer
 *               userName:
 *                 type: string
 *     responses:
 *       200:
 *         description: Réception réussie
 *       400:
 *         description: Paramètres manquants
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/receptionPackageMagasin:
 *   post:
 *     summary: Réceptionne des colis pour un magasin spécifique
 *     tags: [Package]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - packages
 *               - userId
 *               - userName
 *             properties:
 *               packages:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     barcode:
 *                       type: string
 *               userId:
 *                 type: integer
 *               userName:
 *                 type: string
 *     responses:
 *       200:
 *         description: Réception réussie
 *       400:
 *         description: Paramètres manquants
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/search:
 *   post:
 *     summary: Recherche des colis avec filtres
 *     tags: [Package]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               barcode:
 *                 type: string
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [en attente, en transit, livré, partiellement livré]
 *     responses:
 *       200:
 *         description: Résultats de la recherche
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Package'
 *       400:
 *         description: Paramètres invalides
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/getStatusByPackage/{id}:
 *   get:
 *     summary: Récupère l'historique des statuts d'un colis
 *     tags: [Package]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Historique des statuts
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/PackageStatus'
 *       404:
 *         description: Colis non trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/getBarcodes:
 *   post:
 *     summary: Génère des codes-barres pour des IDs donnés
 *     tags: [Package]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Codes-barres générés
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Barcode'
 *       400:
 *         description: IDs invalides
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/generateCode:
 *   post:
 *     summary: Génère de nouveaux codes-barres pour un point de chargement
 *     tags: [Package]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *               - count
 *             properties:
 *               id:
 *                 type: integer
 *               count:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Codes-barres générés
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 barcodes:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Package'
 *       400:
 *         description: Paramètres invalides
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/getHistoricalStatusStats:
 *   get:
 *     summary: Récupère les statistiques historiques des statuts
 *     tags: [Package]
 *     responses:
 *       200:
 *         description: Statistiques historiques
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/PackageStats'
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/getStatisticPackage:
 *   get:
 *     summary: Récupère les statistiques des colis
 *     tags: [Package]
 *     responses:
 *       200:
 *         description: Statistiques des colis
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   status:
 *                     type: string
 *                   total:
 *                     type: integer
 *                   moyenne_minutes:
 *                     type: integer
 *                   moyenne_formatee:
 *                     type: string
 *                   date_min_status:
 *                     type: string
 *                     format: date-time
 *                   date_max_status:
 *                     type: string
 *                     format: date-time
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/getPackageByStatusAndDate:
 *   post:
 *     summary: Récupère les colis par statut et date
 *     tags: [Package]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - date
 *               - status
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [en attente, en transit, livré, partiellement livré]
 *     responses:
 *       200:
 *         description: Liste des colis
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Package'
 *       400:
 *         description: Paramètres manquants
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/findPackageDepartByUser/{id}:
 *   get:
 *     summary: Récupère les colis au départ pour un utilisateur
 *     tags: [Package]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des colis
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Package'
 *       404:
 *         description: Aucun colis trouvé
 *       500:
 *         description: Erreur serveur
 */

/**
 * @swagger
 * /api/findPackageArrivalByUser/{id}:
 *   get:
 *     summary: Récupère les colis à l'arrivée pour un utilisateur
 *     tags: [Package]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des colis
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Package'
 *       404:
 *         description: Aucun colis trouvé
 *       500:
 *         description: Erreur serveur
 */