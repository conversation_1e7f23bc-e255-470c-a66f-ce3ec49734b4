const Ville = require ("../models/ville.model.js") ; 

// create a ville 
exports.create = (req, res)=>
{ 
  //console.log('Marchandise')
  if(!req.body){
    res.status(400).send({
      message: "Content can not be empty"
    })
  }
 
 
 const ville = new Ville ({
   id:req.body.id ,
   nom_ville: req.body.nom_ville,
  

 });

 /*//console.log('ttt')
//console.log(conducteur)  */

 // Save ville in the database
 Ville.create(ville, (err, data) => {
  // //console.log('test');
   if (err)
     res.status(500).send({
       message:
         err.message || "Some error occurred while creating the ville ."
     });
   else res.send(data);
 });
}

// Retrieve all Ville from the database.
exports.findAll = (req, res) => {
  Ville.getAll((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving Ville."
      });
    else res.send(data);
  });
 
};



