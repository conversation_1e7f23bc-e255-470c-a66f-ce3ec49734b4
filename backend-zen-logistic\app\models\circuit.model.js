const sql = require("./db.js");
const cron = require('node-cron');


const getCircuitById = async (circuitId) => {  
  return new Promise((resolve, reject) => {  
    sql.query("SELECT * FROM circuit WHERE id = ?", [circuitId], (err, res) => {  
      if (err) {  
        console.log("Erreur lors de la récupération du circuit : ", err);  
        return reject(err);  
      }  
      if (res.length === 0) {  
        return reject({ kind: "not_found" }); // Not found  
      }  
      resolve(res[0]); // Return the first circuit found  
    });  
  });  
};  


const getLignesByIds = async (ligneIds) => {
 // console.log("ligneIds passed to getLignesByIds:", ligneIds);

  return new Promise((resolve, reject) => {
    const query = `
      SELECT 
        pt.*, 
        u.type_utilisateur 
      FROM 
        point_chargement AS pt
      LEFT JOIN 
        utilisateurs AS u
      ON 
        u.id = pt.id_demandeur
      WHERE 
        pt.id IN (?);
    `;

    sql.query(query, [ligneIds], (err, res) => {
      if (err) {
        console.log("Erreur lors de la récupération des lignes : ", err);
        reject(err);
        return;
      }
   //   console.log("Lignes récupérées :", res); // Vérifiez ici les données retournées
      resolve(res);
    });
  });
};




const getImpot = async () => {  
  return new Promise((resolve, reject) => {  
    sql.query("SELECT * FROM impot WHERE id = 1", (err, res) => {  
      if (err) {  
        console.log("Erreur lors de la récupération des informations de l'impot : ", err);  
        return reject(err); // Reject the promise with the error  
      }  
      if (res.length === 0) {  
        return reject({ kind: "not_found" }); // Reject if no record found  
      }  
      console.log("Impot récupérées: ", res);  // Log the retrieved data  
      resolve(res[0]); // Resolve the promise with the first result found  
    });  
  });  
};


const updateLignePricesColis = async(lignes, price_colis, impot) => {
  return new Promise((resolve, reject) => {
    let lignesNonMisesAJour = [];
    
    let updatePromises = lignes.map((ligne, index) => {
      if (ligne.type_ligne === "Livraison fourniture" || 
          ligne.type_ligne === "Transfert administratif technique et matériel informatique") {
                ligne.prix_unit = price_colis;
        ligne.prix_tot = ligne.quantite * price_colis;

        return new Promise((resolve, reject) => {
          sql.query(
            "UPDATE point_chargement SET prix_unit = ?, prix_tot = ? WHERE id = ?",
            [ligne.prix_unit, ligne.prix_tot, ligne.id],
            (err, res) => {
              if (err) {
                reject(err);
                return;
              }
              resolve(res);  // Résoudre la promesse de mise à jour
            }
          );
        });
      }

      // Condition supplémentaire pour "Transfert administratif ZEN Group-Magasin ou dépôt"
      if (ligne.type_ligne === "Transfert administratif ZEN Group-Magasin ou dépôt" || ligne.type_ligne == "Transfert Administratif inter magasin" ) {
        console.log(ligne.id,price_colis)
        ligne.prix_unit = price_colis;
        ligne.prix_tot = price_colis;

        return new Promise((resolve, reject) => {
          sql.query(
            "UPDATE point_chargement SET prix_unit = ?, prix_tot = ? WHERE id = ?",
            [ligne.prix_unit, ligne.prix_tot, ligne.id],
            (err, res) => {
              if (err) {
                reject(err);
                return;
              }
              resolve(res);  // Résoudre la promesse de mise à jour
            }
          );
        });
      }
      
      // Si aucune condition n'est remplie, ajouter la ligne à la liste des non mises à jour
      lignesNonMisesAJour.push(ligne);
      
      // Retourner une promesse vide pour les lignes non mises à jour
      return Promise.resolve();
    });

    // Attendre que toutes les promesses soient terminées
    Promise.all(updatePromises)
      .then(() => resolve(lignesNonMisesAJour))  // Résoudre avec les lignes non mises à jour
      .catch(err => reject(err));  // Rejeter si une erreur se produit
  });
};



const updateLigneAramex = async (lignes, price_voyage) => {
  // Créer un tableau pour stocker toutes les promesses de mise à jour
  const updatePromises = lignes.map((ligne) => {
    // Calculer les valeurs pour prix_unit et prix_tot
    ligne.prix_unit = price_voyage / lignes.length;
    ligne.prix_tot = price_voyage / lignes.length;

    // Retourner la promesse de mise à jour pour chaque ligne
    return new Promise((resolve, reject) => {
      sql.query(
        "UPDATE point_chargement SET prix_unit = ?, prix_tot = ? WHERE id = ?",
        [ligne.prix_unit, ligne.prix_tot, ligne.id],
        (err, res) => {
          if (err) {
            reject(err);  // Rejeter la promesse en cas d'erreur
            return;
          }
          resolve(res);  // Résoudre la promesse en cas de succès
        }
      );
    });
  });

  // Attendre que toutes les promesses soient terminées
  try {
    const results = await Promise.all(updatePromises);
    console.log("Mise à jour terminée avec succès", results);
  } catch (err) {
    console.error("Erreur lors de la mise à jour des lignes", err);
  }
};




















































const calculatePrixLignesNonMisesAJour = async (lignesNonMisesAJour, circuit, impot) => {
  return new Promise(async (resolve, reject) => {
    if (!Array.isArray(lignesNonMisesAJour) || lignesNonMisesAJour.length === 0) {
      return reject(new Error("Aucune ligne fournie."));
    }

    if (lignesNonMisesAJour.length === 1) {
      return updateSingleLine(lignesNonMisesAJour[0], circuit, resolve, reject);
    }

    try {
      const groupedLines = await groupLinesByClient (lignesNonMisesAJour); // 1️⃣ Groupage d'abord

      console.log(groupedLines)
      const totalKilometrageVolume = calculateTotalKilometrageVolume(groupedLines); // 2️⃣ Calcul après groupage
      console.log(totalKilometrageVolume)

      const updatePromises = updateGroupedLines(groupedLines, totalKilometrageVolume, circuit, impot);

      Promise.all(updatePromises.flat())
        .then(() => resolve("Mises à jour terminées."))
        .catch(reject);
    } catch (error) {
      reject(error);
    }
  });
};

const updateSingleLine = (ligne, circuit, resolve, reject) => {
  const prixTot = circuit.price;
  const prixUnit = ligne.kilometrage * ligne.volume > 0
    ? (prixTot / (ligne.kilometrage * ligne.volume)).toFixed(3)
    : "0.000";

  sql.query(
    "UPDATE point_chargement SET prix_tot = ?, prix_unit = ? WHERE id = ?",
    [prixTot, prixUnit, ligne.id],
    (err, res) => {
      if (err) return reject(err);
      resolve(res);
    }
  );
};

const calculateTotalKilometrageVolume = (groupedLines) => {
  let total = 0;

  Object.values(groupedLines).forEach(group => {
    group.forEach(ligne => {
      if (
        typeof ligne.kilometrage !== "number" ||
        typeof ligne.volume !== "number" ||
        ligne.kilometrage <= 0 ||
        ligne.volume < 0
      ) {
        throw new Error(`Données invalides : ${JSON.stringify(ligne)}`);
      }
      total += ligne.kilometrage * ligne.volume;
    });
  });

  if (total === 0) throw new Error("Total kilometrage * volume ne peut pas être zéro.");
  return total;
};




const groupLinesByClient  = async (lignes) => {
  if (!Array.isArray(lignes) || lignes.length === 0) {
    console.log("Aucune ligne à traiter.");
    return {};
  }

  const groups = lignes.reduce((acc, ligne) => {
    const key = `${ligne.id_client}`;
    if (!acc[key]) acc[key] = [];
    acc[key].push(ligne);
    return acc;
  }, {});

  for (const key in groups) {
    let totalVolume = groups[key].reduce((sum, ligne) => sum + ligne.volume, 0);

    if (totalVolume < 1) {
      const id_client = groups[key][0].id_client;

      try {
        // Requête SQL convertie en Promise
        const rows = await new Promise((resolve, reject) => {
          sql.query("SELECT * FROM fournisseur WHERE id = ?", [id_client], (err, res) => {
            if (err) {
              console.error("Erreur SQL:", err);
              reject(err);
            } else {
              resolve(res);
            }
          });
        });

        if (rows.length > 0 && rows[0].sous_traitant) {
          totalVolume = 1;
          const volumeParLigne = 1 / groups[key].length;

          groups[key].forEach(ligne => {
            console.log(`Ligne ID: ${ligne.id} - Ancien volume: ${ligne.volume} - Nouveau volume: ${volumeParLigne}`);
            ligne.volume = volumeParLigne;
          });
        } else {
          console.log(`Aucun sous-traitant trouvé pour le client avec l'ID: ${id_client}`);
        }
      } catch (err) {
        console.error("Erreur lors de la récupération du fournisseur :", err);
      }
    }
  }

  return groups;
};










const updateGroupedLines = (groupedLines, totalKilometrageVolume, circuit, impot) => {
  return Object.keys(groupedLines).map(key => {
    const lignesGroup = groupedLines[key];
    const impotPartage = (impot.station / lignesGroup.length).toFixed(3);

    return lignesGroup.map(ligne => {
      ligne.volume = ligne.volume;
      const ligneKilometrageVolume = ligne.kilometrage * ligne.volume;
      let proportion = ligneKilometrageVolume / totalKilometrageVolume;
      const prixUnitaireAvecImpot = (circuit.price * proportion) + Number(impotPartage);
      const prixTot = prixUnitaireAvecImpot.toFixed(3);
      const prixUnit = ligneKilometrageVolume > 0 ? (prixUnitaireAvecImpot / ligneKilometrageVolume).toFixed(3) : "0.000";

      return new Promise((resolve, reject) => {
        sql.query(
          "UPDATE point_chargement SET station_price = ?, prix_tot = ?, prix_unit = ?,adapted_volume =? WHERE id = ?",
          [impotPartage, prixTot, prixUnit,ligne.volume, ligne.id],
          (err, res) => {
            if (err) return reject(err);
            resolve(res);
          }
        );
      });
    });
  });
};


async function processLignesAndCreateVoyage(lignes, circuitData) {
  try {
    // Récupérer les lignes en fonction de l'ID
    const ligneData = await getLignesByIds(lignes);

    // Calculer le prix total des lignes
    const totalSellPrice = ligneData.reduce(
      (sum, ligne) => sum + (ligne.prix_tot),
      0
    );

    // Créer les données du voyage
    const voyageData = {
      nom_voyage: circuitData.nom_circuit,
      sell_price: totalSellPrice,
      purchase_price: circuitData.price_without_margin,
      id_camion: (ligneData[0] && ligneData[0].id_camion) || null,
      id_conducteur: (ligneData[0] && ligneData[0].id_conducteur) || null,
      date_voyage: (ligneData[0] && ligneData[0].date_voyage) || (ligneData[0] && ligneData[0].date_voyage),
    };

    // Ajouter le voyage dans la base de données
    const voyageId = await addVoyageAsync(voyageData);

    // Mettre à jour les lignes avec l'ID du voyage
    await updateLignesWithVoyageIdAsync(ligneData, voyageId);

    // Retourner les détails du voyage créé (via la promesse)
    return { voyageId, totalSellPrice };
  } catch (err) {
    // Gérer l'erreur en la rejetant dans la promesse
    throw err;
  }
}



const addVoyageAsync = async (voyageData) => {  
  return new Promise((resolve, reject) => {  
    sql.query("INSERT INTO voyage SET ?", voyageData, (err, res) => {  
      if (err) {  
        console.log("Erreur lors de l'ajout du voyage : ", err);  
        return reject(err);  
      }  
      resolve(res.insertId); // Retourner l'ID du voyage créé  
    });  
  });  
};

const updateLignesWithVoyageIdAsync = async (lignes, voyageId) => {  
  const updatePromises = lignes.map(ligne => {  
    return new Promise((resolve, reject) => {  
      sql.query("UPDATE point_chargement SET status=?, id_voyage = ? WHERE id = ?", ["Ajusté",voyageId, ligne.id], (err, res) => {  
        if (err) {  
          console.log("Erreur lors de la mise à jour de la ligne : ", err);  
          reject(err);  
        } else {  
          resolve(res);  
        }  
      });  
    });  
  });  

  // Wait for all lines to be updated  
  await Promise.all(updatePromises);  
};


const CircuitModel = function(circuit) {
    //console.log("rrrrrrrrr",circuit)
  this.nom_circuit = circuit.nom_circuit;
  this.price = circuit.price;
  this.margin = circuit.margin;
  this.price_without_margin = circuit.price_without_margin;

};

CircuitModel.create = (newCircuit, result) => {
  sql.query("INSERT INTO `circuit` SET ?", newCircuit, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

   // //console.log("created circuit: ", { id: res.insertId, ...newCircuit });
    result(null, { id: res.insertId, ...newCircuit });
  });
};

CircuitModel.findAll = (result) => {
  sql.query("SELECT * FROM `circuit` ORDER BY `nom_circuit` ASC", (err, res) => {
      if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
      }

      // //console.log("circuits: ", res);
      result(null, res);
  });
};



CircuitModel.delete = (circuitId, result) => {
  sql.query("DELETE FROM `circuit` WHERE id = ?", circuitId, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      // No circuit found with the given id
      result({ kind: "not_found" }, null);
      return;
    }

   // //console.log("Deleted circuit with id: ", circuitId);
    result(null, res);
  });
};



CircuitModel.adjustVoyage = async (id_circuit, lignes, result) => {  
  try {  
    const circuitData = await getCircuitById(id_circuit);  
    const ligneData = await getLignesByIds(lignes);  
    const impotData = await getImpot(); 
    
    const isCircuitAramex = circuitData.nom_circuit.toLowerCase().includes('aramex');
    let lignesNonMisesAJour = [];

    if (!isCircuitAramex && ligneData.length > 1) {
      lignesNonMisesAJour = await updateLignePricesColis(ligneData, impotData.price_colis, impotData); 
    } else {
      lignesNonMisesAJour = ligneData;
    }

    if (!isCircuitAramex && lignesNonMisesAJour.length > 0) {
      await calculatePrixLignesNonMisesAJour(lignesNonMisesAJour, circuitData, impotData);  
    }

    if (isCircuitAramex) {
      await updateLigneAramex(ligneData, circuitData.price);
    }

    // Traitement des lignes et création du voyage
    const { voyageId, totalSellPrice } = await processLignesAndCreateVoyage(lignes, circuitData);  
    
    // Retourner les résultats
    result(null, {  
      circuit: { id: circuitData.id, V: circuitData.price },  
      lignes: ligneData,  
      lignesNonMisesAJour: lignesNonMisesAJour,  
      voyageId: voyageId,  
      totalSellPrice: totalSellPrice,  
    });  
  } catch (err) {  
    console.error("Error during the adjustment process:", err.stack);  
    result({ step: "adjustVoyageProcess", error: err.message, stack: err.stack }, null);  
  }  
};




CircuitModel.updateCircuitById = (id, updatedFields, result) => {
  // Validate input
  const hasValidValues = Object.values(updatedFields).every(
    (value) => value !== null && value !== '' && value !== undefined
  );

  if (!hasValidValues) {
    result({ kind: "invalid_input" }, null);
    return;
  }

  // Prepare query for dynamic field updates
  const fieldsToUpdate = Object.keys(updatedFields)
    .map((key) => `${key} = ?`)
    .join(", ");
  const values = Object.values(updatedFields);

  sql.query(
    `UPDATE circuit SET ${fieldsToUpdate} WHERE id = ?`,
    [...values, id],
    (err, res) => {
      if (err) {
        console.error("Erreur SQL : ", err);
        result(err, null);
        return;
      }

      if (res.affectedRows === 0) {
        // No rows updated
        result({ kind: "not_found" }, null);
        return;
      }

      console.log("Mise à jour réussie : ", { id, ...updatedFields });
      result(null, { id, ...updatedFields });
    }
  );
};










































































async function processLignesAndUpdate(lignes, voyageId) {
  try {
    // Récupérer les lignes en fonction de l'ID
    const ligneData = await getLignesByIdVoyage(voyageId);

    // Calculer le prix total des lignes
    const totalSellPrice = ligneData.reduce(
      (sum, ligne) => sum + (ligne.prix_tot || 0),  // Assurer qu'il n'y ait pas de valeurs nulles ou undefined
      0
    );

    // Préparer la requête pour mettre à jour le prix total du voyage
    const query = `UPDATE voyage SET sell_price = ? WHERE id = ?`;

    // Exécuter la requête SQL avec les paramètres nécessaires
    await new Promise((resolve, reject) => {
      sql.query(query, [totalSellPrice, voyageId], (err, res) => {
        if (err) {
          console.error("Erreur de mise à jour dans la base de données :", err);
          return reject(err);
        }
        console.log(`Voyage ID ${voyageId} mis à jour avec succès avec un prix total de ${totalSellPrice}.`);
        resolve(res);
      });
    });

    // Retourner les détails du voyage créé (via la promesse)
    return { voyageId, totalSellPrice };
  } catch (err) {
    // Gérer l'erreur en la rejetant dans la promesse
    console.error("Erreur lors du traitement des lignes et de la mise à jour :", err);
    throw err;
  }
}


const getLignesByIdVoyage = async (ligneIds) => {
  return new Promise((resolve, reject) => {
    const query = `
      SELECT 
        pt.*, 
        u.type_utilisateur 
      FROM 
        point_chargement AS pt
      LEFT JOIN 
        utilisateurs AS u
      ON 
        u.id = pt.id_demandeur
      WHERE 
        pt.id_voyage IN (?);
    `;
    sql.query(query, [ligneIds], (err, res) => {
      if (err) {
        console.log("Erreur lors de la récupération des lignes : ", err);
        reject(err);
        return;
      }
      resolve(res);
    });
  });
};


const getCircuitByName = async (circuitName) => {
  return new Promise((resolve, reject) => {
    sql.query("SELECT * FROM circuit WHERE nom_circuit = ?", [circuitName], (err, res) => {
      if (err) {
        console.log("Erreur lors de la récupération du circuit : ", err);
        return reject(err);
      }
      if (res.length === 0) {
        return reject({ kind: "not_found" }); // Not found
      }
      resolve(res[0]); // Return the first circuit found
    });
  });
};


const adjustVoyageCron = async (voyages, result) => {
  try {
    // Assurez-vous que voyages est un tableau
    if (!Array.isArray(voyages) || voyages.length === 0) {
      throw new Error('Aucune donnée de voyage fournie.');
    }

    // Traitement pour chaque voyage dans la liste
    for (const voyage of voyages) {
      try {
        // Récupérer les données du circuit et des lignes pour chaque voyage
        const circuitData = await getCircuitByName(voyage.nom_voyage);
        const ligneData = await getLignesByIdVoyage(voyage.id); // Vérifie que 'lignes' est bien un tableau d'IDs de lignes
        const impotData = await getImpot();

        const isCircuitAramex = circuitData.nom_circuit.toLowerCase().includes('aramex');
        let lignesNonMisesAJour = [];

        // Si ce n'est pas un circuit Aramex et qu'il y a plus d'une ligne, on met à jour les prix des lignes
        if (!isCircuitAramex && ligneData.length > 1) {
          lignesNonMisesAJour = await updateLignePricesColis(ligneData, impotData.price_colis, impotData);
        } else {
          lignesNonMisesAJour = ligneData;
        }

        // Si ce n'est pas un circuit Aramex et qu'il y a des lignes à mettre à jour, on calcule leur prix
        if (!isCircuitAramex && lignesNonMisesAJour.length > 0) {
          await calculatePrixLignesNonMisesAJour(lignesNonMisesAJour, circuitData, impotData);
        }

        // Si c'est un circuit Aramex, on met à jour les lignes spécifiques
        if (isCircuitAramex) {
          await updateLigneAramex(ligneData, circuitData.price);
        }

        // Traitement des lignes et création du voyage
        const { voyageId, totalSellPrice } = await processLignesAndUpdate(ligneData, voyage.id);

        console.log(`Voyage ID: ${voyageId}, Total Sell Price: ${totalSellPrice}`);

      } catch (err) {
        console.error(`Erreur lors du traitement du voyage avec ID ${voyage.id}:`, err.stack);
      }
    }

    // Si tout s'est bien passé, retournez une réponse générique
    //result(null, { message: 'Traitement terminé pour tous les voyages.' });

  } catch (err) {
    console.error("Erreur lors du traitement global des voyages:", err.stack);
    result({ step: "adjustVoyageProcess", error: err.message, stack: err.stack }, null);
  }
};





















const getVoyages = () => {
  return new Promise((resolve, reject) => {
    const query = 'SELECT * FROM voyage where id= 148 And cancelled = false'; // Sélectionner tous les voyages
    sql.query(query, (err, res) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(res); // Retourne tous les voyages
    });
  });
};

// Recalcul Voyage


// cron.schedule('*/30 * * * * *', async () => {  
//   try {
//     // Récupérer tous les voyages
//     const voyages = await getVoyages();

//     console.log("Voyages récupérés: ", voyages); // Vérifier si les voyages sont bien récupérés

//     if (voyages.length > 0) {
//       // Pour chaque voyage, invoquer la fonction adjustVoyageCron
//       for (let voyage of voyages) {
//         // Vous devrez définir les autres paramètres nécessaires pour adjustVoyageCron, comme 'lignes' et 'result'
//         const lignes = []; // Remplacer par les lignes appropriées si nécessaire
//         const result = (err, data) => {
//           if (err) {
//             console.error("Erreur lors de l'exécution de adjustVoyageCron", err);
//           } else {
//             console.log("Résultat de adjustVoyageCron:", data);
//           }
//         };

//         // Appeler la fonction adjustVoyageCron pour chaque voyage
//         await adjustVoyageCron([voyage], lignes, result);
//       }
//     } else {
//       console.log("Aucun voyage trouvé.");
//     }
//   } catch (err) {
//     console.error("Erreur lors de l'exécution de la tâche cron:", err);
//   }
// });

module.exports = CircuitModel;
;
