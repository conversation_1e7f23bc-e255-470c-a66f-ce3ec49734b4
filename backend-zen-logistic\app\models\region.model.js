const sql = require("./db.js");


//constructeur  

const Region = function(region) {
    this.id=region.id ;
    this.nom_region = region.nom_region;
    this.id_ville = region.id_ville ;
    this.idVille = region.idVille ;


  } ;

  // Fuction create region 

  Region.create = (newRegion, result) => {
    sql.query("INSERT INTO region SET ?", newRegion, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      //console.log("created region: ", { id: res.insertId, ...newRegion });
      result(null, { id: res.insertId, ...newRegion });
    });
  };





  // function get region by id_ville 

   Region.findById = (regionId_ville, result) => {
    sql.query(`SELECT * FROM region WHERE idVille = ${regionId_ville}`, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      if (res.length) {
        //console.log("found Region: ", res);
        result(null, res);
        return;
      }









  
      // not found Region with the ville_id
      result({ kind: "not_found" }, null);
    });
  }; 



  //Function get all Region 
  Region.getAll = result => {
    sql.query("SELECT * FROM region", (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }
  
      //console.log("Region: ", res);
      result(null, res);
    });
  }

  module.exports = Region ; 
