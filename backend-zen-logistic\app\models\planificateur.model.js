const sql = require("./db.js");

const Planificateur = {};

// Récupérer la liste des tables
Planificateur.findAllTables = (result) => {
  sql.query("SHOW TABLES", (err, res) => {
    if (err) {
      console.log("Error: ", err);
      result(err, null);
      return;
    }

    // Vérifiez si `res` est un tableau et extrayez les noms des tables
    if (!Array.isArray(res)) {
      console.log("Expected an array of tables, but got:", res);
      result("Error: The tables data is not an array", null);
      return;
    }

    // Retourner les noms des tables sous forme de tableau
    result(null, res);
  });
};

Planificateur.describeTables = (tables, result) => {
    const descriptions = [];
    let completed = 0;
  
    // Vérifier que `tables` est bien un tableau
    if (!Array.isArray(tables)) {
      return result("Error: `tables` parameter is not an array.", null);
    }
  
    // Pour chaque table, exécuter une requête DESCRIBE
    tables.forEach((table, index) => {
      sql.query(`DESCRIBE ${table}`, (err, res) => {
        if (err) {
          console.log(`Error describing table ${table}:`, err);
          descriptions.push({ table, error: err.message });
        } else {
          // Filtrer les colonnes pour n'inclure que celles qui ne contiennent pas "id"
          const filteredColumns = res
            .filter(col => !col.Field.includes("id") && !col.Field.includes("by") ) // Exclure les champs contenant "id"
            .map(col => ({
              field: col.Field, // Nom du champ
              type: col.Type   // Type du champ
            }));
  
          descriptions.push({ table, columns: filteredColumns });
        }
  
        completed++;
        // Si toutes les tables ont été traitées, renvoyer la réponse
        if (completed === tables.length) {
          result(null, descriptions);
        }
      });
    });
  };

module.exports = Planificateur;
