const Conducteur = require ("../models/conducteur.model.js") ; 

  exports.create = (req, res)=>
 { 
   if(!req.body){
     res.status(400).send({
       message: "Content can not be empty"
     })
   }
  
  
  const conducteur = new Conducteur({
   //id:req.body.id ,
   nom: req.body.nom,
   prenom: req.body.prenom,
   nom_utilisateur: req.body.nom_utilisateur,
   mot_de_passe	: req.body.mot_de_passe, 
   cin: req. body.cin,
   mobile : req.body.mobile,
   image_cin: req.body.image_cin,
   ajoutee_par : req.body.ajoutee_par,

  });


  // Save Conducteur in the database
  Conducteur.create(conducteur, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while creating the conducteur ."
      });
    else res.send(data);
  });
}

// Retrieve all Conducteur from the database.
exports.findAll = (req, res) => {
  Conducteur.getAll((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving conducteur."
      });
    else res.send(data);
  });
 
};

// Delete a Conducteur with the specified conducteurId in the request
exports.conducteurBloqued = (req, res) => {
  Conducteur.conducteurBloqued(req.params.conducteurId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Conducteur with id ${req.params.conducteurId}.`
        });
      } else {
        res.status(500).send({
          message: "Could not delete conducteur with id " + req.params.conducteurId
        });
      }
    } else res.send({ message: `conducteur was deleted successfully!` });
  });
};


// Find a single Conducteur with a conducteurId
exports.findOne = (req, res) => {

  Conducteur.findById(req.params.conducteurId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Conducteur with id ${req.params.conducteurId}.`
        });
      } else {
        res.status(500).send({
          message: "Error retrieving Conducteur with id " + req.params.conducteurId
        });
      }
    } else res.send(data);
  });
};

   // Retrieve all Conducteur from the database 
   exports.findAllConducteur= (req, res) => {
    Conducteur.findConducteur(req.params.idUser ,  (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Conducteur with idUser ${req.params.idUser} `
        });
      } else {
        res.status(500).send({
          message: "Error retrieving Conducteur with idUser " + req.params.idUser 
        });
      }
    } else res.send(data);
  });
  };
  exports.findByAuth = (req, res) => {
    Conducteur.findAuth (req.body.nom_utilisateur, req.body.mot_de_passe , (err, data) => {
    
      if (err) {
        console.log(err)
         if (err.kind === "not_found") {
           res.status(404).send({
             message: `Not found with user name ${req.body.nom_utilisateur} .`
          
            });
         } else {
           res.status(500).send({
             message: "Error retrieving Conducteur with user name" + req.params.conducteurNu 
           });
         }
       } else res.send(data) ; 
     });    
  
  };
