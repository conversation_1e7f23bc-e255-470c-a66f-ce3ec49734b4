import { NgModule } from '@angular/core';
import { CommonModule } from "@angular/common";
import { DatePipe } from "@angular/common";

import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { FullPagesRoutingModule } from "./full-pages-routing.module";
import { ChartistModule } from 'ng-chartist';
import { AgmCoreModule } from '@agm/core';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
// import { GalleryPageComponent } from "./gallery/gallery-page.component";

import { UserProfilePageComponent } from "./user-profile/user-profile-page.component";
import { SearchComponent } from './search/search.component';
// import { FaqComponent } from './faq/faq.component';
import { ListUserComponent } from '../list-user/list-user.component';
import { Ng2SmartTableModule } from 'ng2-smart-table';
import { HttpClientModule } from '@angular/common/http';
import { ProfiletransporteurComponent } from '../profiletransporteur/profiletransporteur.component';
import { ListConducteurComponent } from '../list-conducteur/list-conducteur.component';
import { WelcomConducteurComponent } from '../welcom-conducteur/welcom-conducteur.component';
import { WelcomeCamionComponent } from '../welcome-camion/welcome-camion.component';
import { ListCamionComponent } from '../list-camion/list-camion.component';
import { WelcomCommandeComponent } from '../welcom-commande/welcom-commande.component';
import { ListCommandeComponent } from '../list-commande/list-commande.component';
import { ListCommandeAdminComponent } from 'app/list-commande-admin/list-commande-admin.component';
import { ToastrModule } from 'ngx-toastr';
import { ListCommandeExpComponent } from '../list-commande-exp/list-commande-exp.component';
import { ListCommValidExpComponent } from '../list-comm-valid-exp/list-comm-valid-exp.component';
import { ListCommAValiderExpComponent } from '../list-comm-a-valider-exp/list-comm-a-valider-exp.component';
import { MesReservationsComponent } from '../mes-reservations/mes-reservations.component';
import { ListUserTrComponent } from '../list-user-tr/list-user-tr.component';
import { AjouCommComponent } from '../ajou-comm/ajou-comm.component';
import { MesCommResComponent } from '../mes-comm-res/mes-comm-res.component';
import { HomeComponent } from '../home/<USER>';
import { ConducteurComponent } from '../conducteur/conducteur.component';
import { CamionComponent } from '../camion/camion.component';
import { ListCamionAdminComponent } from '../list-camion-admin/list-camion-admin.component';
import { DeconnexionComponent } from '../deconnexion/deconnexion.component';
import { ListConducteurAdminComponent } from '../list-conducteur-admin/list-conducteur-admin.component';
import { ProfileexpediteurComponent } from '../profileexpediteur/profileexpediteur.component';
import { ActivationComponent } from '../content-pages/activation/activation.component';
import { NgxMaskModule } from 'ngx-mask';
import { FileUploadModule } from 'ng2-file-upload';
import { ReclamationComponent } from 'app/reclamation/reclamation.component';
import { GFactureComponent } from 'app/g-facture/g-facture.component';
import { ListFactureComponent } from '../list-facture/list-facture.component';
import { ParametrageComponent } from '../parametrage/parametrage.component';
import { SavedModeleComponent } from '../saved-modele/saved-modele.component';
import { CommALivreComponent } from '../comm-a-livre/comm-a-livre.component';
import { ExpeditionsComponent } from '../expeditions/expeditions.component';
import { LigneExpedierComponent } from '../ligne-expedier/ligne-expedier.component';
import { LineDelivredComponent } from '../line-delivred/line-delivred.component';
import { ModifyCmdComponent } from '../modify-cmd/modify-cmd.component';
import { GestionDestinationComponent } from '../gestion-destination/gestion-destination.component';
import { AddUserComponent } from '../add-user/add-user.component';
import { VerificationListComponent } from '../verification-list/verification-list.component';
import { AddInvoiceComponent } from '../add-invoice/add-invoice.component';
import { ToBeInvoicedComponent } from '../to-be-invoiced/to-be-invoiced.component';
import { InspectionComponent } from '../inspection/inspection.component';
import { ProfileChefComponent } from '../profile-chef/profile-chef.component';
import { ListChefCommandeComponent } from '../list-chef-commande/list-chef-commande.component';
import { HangarListComponent } from '../hangar-list/hangar-list.component';
import { GsListComponent } from '../gs-list/gs-list.component';
import { CommandeByDestinationComponent } from '../commande-by-destination/commande-by-destination.component';
import { PdfUploadComponent } from '../pdf-upload/pdf-upload.component';
import { ColisageListComponent } from '../colisage-list/colisage-list.component';
import { GFacturefluxComponent } from 'app/g-factureflux/g-factureflux.component';
import { VolumeNotUpdatedComponent } from '../volume-not-updated/volume-not-updated.component';
import { AdjustlinesComponent } from '../adjustlines/adjustlines.component';
import { BarcodeReaderComponent } from '../barcode-reader/barcode-reader.component';
import { ShipmentComponent } from '../shipment/shipment.component';
import { CalendarsModule } from 'app/calendar/calendar.module';
import { RecpetionComponent } from '../recpetion/recpetion.component';
import { ManagingComponent } from '../managing/managing.component';



@NgModule({
    imports: [
        FileUploadModule,
        CommonModule,
        FullPagesRoutingModule,
        FormsModule,
        ChartistModule,
        AgmCoreModule,
        Ng2SmartTableModule,
        NgbModule,
        HttpClientModule,
        ToastrModule.forRoot(),
        ReactiveFormsModule,
        NgxMaskModule.forRoot(),
        CalendarsModule





    ],
    declarations: [
        ListFactureComponent,
        GFactureComponent,
        UserProfilePageComponent,
        SearchComponent,
        // FaqComponent,
        ListUserComponent,
        ProfiletransporteurComponent,
        ListConducteurComponent,
        WelcomConducteurComponent,
        WelcomeCamionComponent,
        ListCamionComponent,
        WelcomCommandeComponent,
        ListCommandeComponent,
        ListCommandeAdminComponent,
        ListCommandeExpComponent,
        ListCommValidExpComponent,
        ListCommAValiderExpComponent,
        MesReservationsComponent,
        ListUserTrComponent,
        AjouCommComponent,
        MesCommResComponent,
        HomeComponent,
        ConducteurComponent,
        CamionComponent,
        ListCamionAdminComponent,
        DeconnexionComponent,
        ListConducteurAdminComponent,
        ListCamionAdminComponent,
        ProfileexpediteurComponent,
        ActivationComponent,
        ReclamationComponent,
        ParametrageComponent,
        SavedModeleComponent,
        CommALivreComponent,
        ExpeditionsComponent,
        LigneExpedierComponent,
        LineDelivredComponent,
        ModifyCmdComponent,
        GestionDestinationComponent,
        AddUserComponent,
        VerificationListComponent,
        AddInvoiceComponent,
        ToBeInvoicedComponent,
        InspectionComponent,
        ProfileChefComponent,
        ListChefCommandeComponent,
        HangarListComponent,
        GsListComponent,
        CommandeByDestinationComponent,
        PdfUploadComponent,
        ColisageListComponent,
        GFacturefluxComponent,
        VolumeNotUpdatedComponent,
        AdjustlinesComponent,
        BarcodeReaderComponent,
        ShipmentComponent,
        RecpetionComponent,
        ManagingComponent


    ],

    providers: [DatePipe]

})
export class FullPagesModule { }
