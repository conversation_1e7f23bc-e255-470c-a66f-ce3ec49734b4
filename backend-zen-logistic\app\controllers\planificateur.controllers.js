const Prices = require("../models/planificateur.model.js");

// Liste des tables à inclure
const includedTables = [
  "commentaire", 
  "voyage", 
  "package", 
  "facture", 
  "commentaire", 
  "colisage", 
  "verification_codes", 
  "sms_history", 
  "status_package"
];

exports.findAllTables = (req, res) => {
  // Récupérer toutes les tables
  Prices.findAllTables((err, data) => {
    if (err) {
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving tables."
      });
    } else {
      // Vérifiez si `data` est un tableau avant de continuer
      if (!Array.isArray(data)) {
        return res.status(500).send({ message: "The tables data is not an array." });
      }

      // Formater les données pour récupérer les noms des tables
      const tableNames = data.map(row => Object.values(row)[0]);

      // Filtrer les tables pour n'inclure que celles qui sont dans includedTables
      const filteredTableNames = tableNames.filter(table => includedTables.includes(table));

      // Pour chaque table dans filteredTableNames, exécuter un "DESCRIBE"
      Prices.describeTables(filteredTableNames, (err, result) => {
        if (err) {
          res.status(500).send({
            message: err.message || "Error occurred while describing the tables."
          });
        } else {
          res.send(result);
        }
      });
    }
  });
};
