import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { RoleService } from './role.service';
import { AuthService } from './auth.service';

export interface Permission {
  action: string;
  resource: string;
  roles: string[];
}

@Injectable({
  providedIn: 'root'
})
export class RbacService {

  constructor(
    private roleService: RoleService,
    private authService: AuthService
  ) { }

  /**
   * Check if user can access a resource with specific permission
   */
  canAccess(permission: string, requiredRole: string): boolean {
    return this.roleService.hasRole(requiredRole);
  }

  /**
   * Check if user can access with any of the required roles
   */
  canAccessWithAnyRole(permission: string, requiredRoles: string[]): boolean {
    return this.roleService.hasAnyRole(requiredRoles);
  }

  /**
   * Check if user can access as Observable
   */
  canAccess$(permission: string, requiredRole: string): Observable<boolean> {
    return this.roleService.hasRole$(requiredRole);
  }

  /**
   * Check if user can access with any of the required roles as Observable
   */
  canAccessWithAnyRole$(permission: string, requiredRoles: string[]): Observable<boolean> {
    return this.roleService.hasAnyRole$(requiredRoles);
  }

  /**
   * Check if user is authenticated and has required role
   */
  isAuthorized(requiredRole: string): boolean {
    const isAuthenticated = this.authService.isAuthenticated();
    const hasRole = this.roleService.hasRole(requiredRole);
    return isAuthenticated && hasRole;
  }

  /**
   * Check if user is authenticated and has any of the required roles
   */
  isAuthorizedWithAnyRole(requiredRoles: string[]): boolean {
    const isAuthenticated = this.authService.isAuthenticated();
    const hasAnyRole = this.roleService.hasAnyRole(requiredRoles);
    return isAuthenticated && hasAnyRole;
  }

  /**
   * Get user permissions based on role
   */
  getUserPermissions(): string[] {
    const userRole = this.roleService.getRole();
    if (!userRole) return [];

    // Define permissions based on roles
    const rolePermissions: { [key: string]: string[] } = {
      'SuperAdmin': ['*'], // All permissions
      'Administrateur': [
        'user.manage',
        'system.configure',
        'reports.view',
        'data.export'
      ],
      'Client': [
        'order.create',
        'order.view',
        'profile.edit'
      ],
      'Chef Departement': [
        'order.manage',
        'team.manage',
        'reports.view'
      ],
      'GS': [
        'inventory.manage',
        'order.process'
      ],
      'GE': [
        'inventory.view',
        'order.view'
      ],
      'Depot': [
        'storage.manage',
        'inventory.update'
      ],
      'Inspection': [
        'quality.check',
        'reports.create'
      ],
      'FACTURATION': [
        'invoice.create',
        'invoice.manage',
        'payment.process'
      ],
      'MAGASIN': [
        'inventory.view',
        'stock.update'
      ],
      'Conducteur': [
        'delivery.update',
        'route.view'
      ]
    };

    return rolePermissions[userRole] || [];
  }

  /**
   * Check if user has specific permission
   */
  hasPermission(permission: string): boolean {
    const userPermissions = this.getUserPermissions();
    return userPermissions.includes('*') || userPermissions.includes(permission);
  }
}
