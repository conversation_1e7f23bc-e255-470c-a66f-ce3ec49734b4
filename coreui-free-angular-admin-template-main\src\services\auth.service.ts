import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from '../environments/environment';
import { BehaviorSubject, catchError, Observable, tap, throwError, map, finalize } from 'rxjs';
import {
  LoginRequest,
  LoginResponse,
  User,
  AuthError,
  RegisterRequest,
  ApiError
} from '../models/auth.interfaces';
import { SessionStorageService } from './session-storage.service';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly apiURL = environment.apiURL;
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public readonly isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  private userSubject = new BehaviorSubject<User | null>(null);
  public readonly user$ = this.userSubject.asObservable();
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public readonly loading$ = this.loadingSubject.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router,
    private sessionStorageService: SessionStorageService
  ) {
    // Check for existing session on service initialization
    this.checkExistingSession();

    // Subscribe to session validity changes
    this.sessionStorageService.sessionValid$.subscribe(isValid => {
      if (!isValid && this.isAuthenticatedSubject.value) {
        // Session became invalid, logout user
        this.handleSessionExpired();
      }
    });
  }


  /**
   * Authenticate user with email and password
   * @param loginData - User login credentials
   * @returns Observable<LoginResponse>
   */
  login(loginData: LoginRequest): Observable<LoginResponse> {
    this.loadingSubject.next(true);

    return this.http.post<LoginResponse>(this.apiURL + 'customers/login', loginData, httpOptions).pipe(
      tap((response: LoginResponse) => {
        console.log('Login response:', response);
        if (response && response.id) {
          // Store user data using SessionStorageService
          this.sessionStorageService.storeSession(response);

          // Update observables
          this.isAuthenticatedSubject.next(true);
          this.userSubject.next({
            iduser: response.id,
            email: response.email,
            role: response.type_utilisateur || response.role || 'user',
            type_utilisateur: response.type_utilisateur,
            nom: response.nom,
            prenom: response.prenom,
            statut: response.statut
          });
        }
      }),
      catchError((error: HttpErrorResponse) => {
        console.error('Login error:', error);
        this.isAuthenticatedSubject.next(false);
        this.userSubject.next(null);
        return throwError(() => this.handleAuthError(error));
      }),
      finalize(() => this.loadingSubject.next(false))
    );
  }
  /**
   * Logout user and clear session data
   */
  logout(): Observable<void> {
    this.loadingSubject.next(true);

    // Clear session using SessionStorageService
    this.sessionStorageService.clearSession();

    // Update observables
    this.isAuthenticatedSubject.next(false);
    this.userSubject.next(null);

    // Note: Backend doesn't have a logout endpoint based on analysis
    // Just clear local session and redirect
    this.loadingSubject.next(false);

    // Return completed observable
    return new Observable<void>(observer => {
      observer.next();
      observer.complete();
    });
  }

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  /**
   * Get current user data
   */
  getUser(): User | null {
    return this.userSubject.value;
  }

  /**
   * Check for existing session on service initialization
   */
  private checkExistingSession(): void {
    const sessionData = this.sessionStorageService.getSession();

    if (sessionData && this.sessionStorageService.hasValidSession()) {
      const user: User = {
        iduser: sessionData.iduser,
        email: sessionData.email,
        role: sessionData.userRole,
        type_utilisateur: sessionData.type_utilisateur,
        nom: sessionData.nom,
        prenom: sessionData.prenom,
        statut: sessionData.statut
      };

      this.isAuthenticatedSubject.next(true);
      this.userSubject.next(user);

      // Update last activity
      this.sessionStorageService.updateLastActivity();
    } else if (sessionData) {
      // Session exists but is invalid, clear it
      this.sessionStorageService.clearSession();
    }
  }

  /**
   * Handle session expiration
   */
  private handleSessionExpired(): void {
    console.log('Session expired, logging out user');

    // Clear session and update observables
    this.sessionStorageService.clearSession();
    this.isAuthenticatedSubject.next(false);
    this.userSubject.next(null);

    // Redirect to login page
    this.router.navigate(['/login']);
  }

  /**
   * Extend current session
   */
  extendSession(additionalMinutes: number = 60): void {
    if (this.isAuthenticated()) {
      this.sessionStorageService.extendSession(additionalMinutes);
    }
  }

  /**
   * Get session validation status
   */
  getSessionValidation() {
    return this.sessionStorageService.validateSession();
  }


  /**
   * Handle authentication errors with proper typing and categorization
   */
  private handleAuthError(error: HttpErrorResponse): AuthError {
    let authError: AuthError;

    if (error.status === 0) {
      // Network error
      authError = {
        type: 'NETWORK_ERROR',
        message: 'Network error. Please check your internet connection and try again.',
        status: 0
      };
    } else if (error.status === 401) {
      // Unauthorized - invalid credentials
      authError = {
        type: 'INVALID_CREDENTIALS',
        message: 'Invalid email or password. Please check your credentials and try again.',
        status: 401
      };
    } else if (error.status === 400) {
      // Bad request - validation error
      authError = {
        type: 'VALIDATION_ERROR',
        message: 'Invalid request format. Please check your input and try again.',
        status: 400
      };
    } else if (error.status >= 500) {
      // Server error
      authError = {
        type: 'SERVER_ERROR',
        message: 'Server error. Please try again later or contact support.',
        status: error.status
      };
    } else {
      // Unknown error
      authError = {
        type: 'UNKNOWN_ERROR',
        message: error.error?.message || 'An unexpected error occurred. Please try again.',
        status: error.status
      };
    }

    return authError;
  }

  /**
   * Register new user
   */
  register(userData: RegisterRequest): Observable<LoginResponse> {
    this.loadingSubject.next(true);

    return this.http.post<LoginResponse>(this.apiURL + 'customers', userData, httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('Registration error:', error);
        return throwError(() => this.handleAuthError(error));
      }),
      finalize(() => this.loadingSubject.next(false))
    );
  }


  /**
   * Update user's last connection time
   */
  updateLastConnection(id: number): Observable<any> {
    return this.http.put<any>(this.apiURL + 'lastConnexion/' + id, null, httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('Update last connection error:', error);
        return throwError(() => this.handleAuthError(error));
      })
    );
  }

  /**
   * Send activation email to user
   */
  sendActivationEmail(emailData: { email: string; [key: string]: any }): Observable<any> {
    return this.http.post<any>(this.apiURL + 'email', emailData, httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('Send activation email error:', error);
        return throwError(() => this.handleAuthError(error));
      })
    );
  }

  /**
   * Find user by email address
   */
  findUserByEmail(email: string): Observable<User> {
    return this.http.get<User>(this.apiURL + `getUserByMail/${email}`, httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('Find user by email error:', error);
        return throwError(() => this.handleAuthError(error));
      })
    );
  }

  /**
   * Verify if username is available
   */
  verifyUsername(username: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'custo/' + username, httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('Verify username error:', error);
        return throwError(() => this.handleAuthError(error));
      })
    );
  }

  /**
   * Handle authentication errors and redirect if necessary
   */
  handleAuthenticationError(error: AuthError): void {
    if (error.status === 401 || error.status === 403 || error.status === 0) {
      this.sessionStorageService.clearSession();
      this.isAuthenticatedSubject.next(false);
      this.userSubject.next(null);
      this.router.navigate(['/login']);
    }
  }


  /**
   * Get all available roles
   */
  getAllRoles(): Observable<any[]> {
    return this.http.get<any[]>(this.apiURL + 'roles', httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('Get all roles error:', error);
        return throwError(() => this.handleAuthError(error));
      })
    );
  }

  /**
   * Update user information
   */
  updateUserInfo(userData: Partial<User> & { id: number }): Observable<User> {
    return this.http.put<User>(this.apiURL + 'updateUserById/' + userData.id, userData, httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('Update user info error:', error);
        return throwError(() => this.handleAuthError(error));
      })
    );
  }

  /**
   * Create chef user (using Observable instead of Promise for consistency)
   */
  createChefUser(userData: RegisterRequest): Observable<User> {
    return this.http.post<User>(this.apiURL + 'createChefUser', userData, httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('Create chef user error:', error);
        return throwError(() => this.handleAuthError(error));
      })
    );
  }

  /**
   * Find users by chef ID
   */
  findUsersByChef(chefId: number): Observable<User[]> {
    return this.http.get<User[]>(this.apiURL + `findUsersByChef/${chefId}`, httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('Find users by chef error:', error);
        return throwError(() => this.handleAuthError(error));
      })
    );
  }

  /**
   * Find all chef users
   */
  findAllChefs(): Observable<User[]> {
    return this.http.get<User[]>(this.apiURL + 'findAllChef', httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('Find all chefs error:', error);
        return throwError(() => this.handleAuthError(error));
      })
    );
  }

  /**
   * Find all client users
   */
  findAllClients(): Observable<User[]> {
    return this.http.get<User[]>(this.apiURL + 'findAllClient', httpOptions).pipe(
      catchError((error: HttpErrorResponse) => {
        console.error('Find all clients error:', error);
        return throwError(() => this.handleAuthError(error));
      })
    );
  }
}
