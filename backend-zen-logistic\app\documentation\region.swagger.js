/**
 * @swagger
 * tags:
 *   name: Regions
 *   description: Opérations liées à la gestion des régions
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Region:
 *       type: object
 *       required:
 *         - nom_region
 *         - id_ville
 *       properties:
 *         id:
 *           type: integer
 *           description: Identifiant unique de la région
 *         nom_region:
 *           type: string
 *           description: Nom de la région
 *         id_ville:
 *           type: integer
 *           description: Identifiant de la ville associée
 *         idVille:
 *           type: integer
 *           description: Identifiant de la ville (peut être redondant avec id_ville)
 */

/**
 * @swagger
 * /api/region:
 *   post:
 *     tags: [Regions]
 *     summary: Créer une nouvelle région
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Region'
 *     responses:
 *       201:
 *         description: Région créée avec succès
 *       400:
 *         description: Contenu de la requête invalide
 *       500:
 *         description: Erreur interne du serveur
 */

/**
 * @swagger
 * /api/region/{regionId_ville}:
 *   get:
 *     tags: [Regions]
 *     summary: Récupérer une région par ID de ville
 *     parameters:
 *       - in: path
 *         name: regionId_ville
 *         required: true
 *         description: ID de la ville associée à la région
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Région récupérée avec succès
 *       404:
 *         description: Aucune région trouvée pour cet ID de ville
 *       500:
 *         description: Erreur interne du serveur
 */

/**
 * @swagger
 * /api/region:
 *   get:
 *     tags: [Regions]
 *     summary: Récupérer toutes les régions
 *     responses:
 *       200:
 *         description: Liste de toutes les régions récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur
 */
