const sql = require("./db.js");


//constructeur  

const Ville = function(ville) {
    this.id=camion.id ;
    this.nom_ville = camion.nom_ville ;

  } ;
  
  // Fuction create ville 

  Ville.create = (newVille, result) => {
    sql.query("INSERT INTO ville SET ?", newVille, (err, res) => {
      if (err) {
        //console.log("error: ", err);
        result(err, null);
        return;
      }
  
      //console.log("created Ville: ", { id: res.insertId, ...newVille });
    result(null, { id: res.insertId, ...newVille });
  });
};




//Function get all Ville 
Ville.getAll = result => {
    sql.query("SELECT * FROM ville", (err, res) => {
      if (err) {
        //console.log("error: ", err);
        result(null, err);
        return;
      }
  
      //console.log("ville: ", res);
      result(null, res);
    });
  }

  module.exports = Ville ; 
