const sql = require("./db.js");

const Colisages = function(colisage) {
  this.volume = colisage.volume;
  this.depotEmetteur = colisage.depotEmetteur;
  this.depotRecepteur = colisage.depotRecepteur;
  this.fileName = colisage.fileName;
  this.status = colisage.status;
  this.nom_demandeur = colisage.nom_demandeur;
  this.idUser = colisage.idUser;
  this.date_voyage = colisage.date_voyage; 
  this.nom_demandeur = colisage.nom_demandeur; 

};

Colisages.addColisage = (colisage, result) => {
  sql.query(
    "SELECT * FROM colisage WHERE depotEmetteur = ? AND depotRecepteur = ? AND date_voyage = ?",
    [colisage.depotEmetteur, colisage.depotRecepteur, colisage.date_voyage],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      if (res.length > 0) {
        const existingColisage = res[0];
        if (existingColisage) {
          if (existingColisage.status === "Succès") {
            sql.query(
              "UPDATE colisage SET volume = ?, fileName = ?, nom_demandeur = ?, idUser = ?, depotEmetteur = ?, depotRecepteur = ?,nom_demandeur =? WHERE id = ?",
              [
                colisage.volume,
                colisage.fileName,
                colisage.nom_demandeur,
                colisage.idUser,
                colisage.depotEmetteur,
                colisage.depotRecepteur,
                colisage.nom_demandeur,
                existingColisage.id
              ],
              (err, res) => {
                if (err) {
                  console.log("error: ", err);
                  result(err, null);
                  return;
                }
        
                result(null, { id: existingColisage.id, ...colisage });
              }
            );
          } else {
            sql.query(
              "UPDATE colisage SET volume = ?, fileName = ?, status = ?, nom_demandeur = ?, idUser = ?, depotEmetteur = ?, depotRecepteur = ? WHERE id = ?",
              [
                colisage.volume,
                colisage.fileName,
                colisage.status,
                colisage.nom_demandeur,
                colisage.idUser,
                colisage.depotEmetteur,
                colisage.depotRecepteur,
                existingColisage.id
              ],
              (err, res) => {
                if (err) {
                  console.log("error: ", err);
                  result(err, null);
                  return;
                }
        
                //console.log("updated colisage: ", { id: existingColisage.id, ...colisage });
                result(null, { id: existingColisage.id, ...colisage });
              }
            );
          }
        }
         else {
          // Si une ligne existe mais que le statut n'est pas "Succès", retourner un message d'erreur
          result({ kind: "already_exists", message: "A colisage with the same depotEmetteur, depotRecepteur, and date_voyage already exists and its status is not 'Succès'." }, null);
        }
      } else {
        // Si aucune ligne n'existe, insérer le nouveau colisage
        sql.query("INSERT INTO colisage SET ?", colisage, (err, res) => {
          if (err) {
            console.log("error: ", err);
            result(err, null);
            return;
          }

          //console.log("created colisage: ", { id: res.insertId, ...colisage });
          result(null, { id: res.insertId, ...colisage });
        });
      }
    }
  );
};

Colisages.findByUser = (idUser, result) => {
  sql.query("SELECT * FROM colisage WHERE idUser = ? AND status='échouer' AND visible = true", [idUser], (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }
    result(null, res);
  });
};

Colisages.updateVisibleById = (id, result) => {
  sql.query("UPDATE colisage SET visible = false WHERE id = ?", [id], (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }

    if (res.affectedRows == 0) {
      result({ kind: "not_found" }, null);
      return;
    }

    console.log("Updated colisage with id: ", id);
    result(null, { id: id, visible: false });
  });
};



Colisages.updateColisage = (id, colisage, result) => {
  sql.query(
    "UPDATE colisage SET volume = ?, status = ?, nom_demandeur = ?,depotEmetteur= ?,depotRecepteur = ? WHERE id = ?",
            [
              colisage.volume,
              colisage.status,
              colisage.nom_demandeur,
              colisage.depotEmetteur,
              colisage.depotRecepteur,
              id
            ],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      if (res.affectedRows == 0) {
        // not found Colisage with the id
        result({ kind: "not_found" }, null);
        return;
      }

      //console.log("updated colisage: ", { id: id, ...colisage });
      result(null, { id: id, ...colisage });
    }
  );
};

Colisages.updateColisageStatus = (id, colisage, result) => {
  sql.query(
    "UPDATE colisage SET status = ? WHERE id = ?",
            [
              colisage.status,
              id
            ],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      if (res.affectedRows == 0) {
        // not found Colisage with the id
        result({ kind: "not_found" }, null);
        return;
      }

      //console.log("updated colisage: ", { id: id, ...colisage });
      result(null, { id: id, ...colisage });
    }
  );
};

Colisages.getAllWithPagination = (page = 1, limit = 50, result) => {
  const offset = (page - 1) * limit;

  sql.query("SELECT COUNT(*) AS total FROM colisage", (err, res) => {
      if (err) {
          console.log("error: ", err);
          result(null, err);
          return;
      }

      const total = res[0].total;

      // Update the SQL query to include ORDER BY
      sql.query("SELECT * FROM colisage ORDER BY date_creation DESC LIMIT ? OFFSET ?", [limit, offset], (err, res) => {
          if (err) {
              console.log("error: ", err);
              result(null, err);
              return;
          }

          //console.log("colisage: ", res);
          result(null, { total, data: res });
      });
  });
};



module.exports = Colisages;
