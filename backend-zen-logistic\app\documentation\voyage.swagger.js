/**
 * @swagger
 * tags:
 *   name: Voyages
 *   description: Opérations liées à la gestion des voyages
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Voyage:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Identifiant unique du voyage
 *         date_voyage:
 *           type: string
 *           format: date
 *           description: Date du voyage
 *         cancelled:
 *           type: boolean
 *           description: Indique si le voyage a été annulé
 */

/**
 * @swagger
 * /api/cancelVoyage/{id}:
 *   put:
 *     tags: [Voyages]
 *     summary: Annuler un voyage par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du voyage à annuler
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Voyage annulé avec succès et lignes associées mises à jour
 *       404:
 *         description: Voyage non trouvé pour cet identifiant
 *       400:
 *         description: Tous les points de chargement n'ont pas le statut 'Ajusté'
 *       500:
 *         description: Erreur interne du serveur
 */

/**
 * @swagger
 * /api/voyages:
 *   post:
 *     tags: [Voyages]
 *     summary: Rechercher des voyages par date
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - date_debut
 *               - date_fin
 *             properties:
 *               date_debut:
 *                 type: string
 *                 format: date
 *                 description: Date de début pour la recherche des voyages
 *               date_fin:
 *                 type: string
 *                 format: date
 *                 description: Date de fin pour la recherche des voyages
 *     responses:
 *       200:
 *         description: Liste des voyages récupérée avec succès
 *       400:
 *         description: Les dates de début et de fin sont requises
 *       500:
 *         description: Erreur interne du serveur
 */
