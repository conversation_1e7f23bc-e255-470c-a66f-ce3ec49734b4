/**
 * @swagger
 * tags:
 *   name: <PERSON><PERSON>
 *   description: Opérations sur les camions
 */

/**
 * @swagger
 * /api/camion:
 *   post:
 *     summary: Créer un nouveau camion
 *     tags: [Camion]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type_camion
 *               - immatriculation
 *               - date_circulation
 *               - poids
 *               - unite_poids
 *               - volume
 *               - nombre_palettes
 *               - longeur
 *               - largeur
 *               - hauteur
 *               - ajoutee_par
 *             properties:
 *               type_camion:
 *                 type: string
 *                 example: "Semi-remorque"
 *               immatriculation:
 *                 type: string
 *                 example: "123 TU 456"
 *               immat_rs_tn:
 *                 type: string
 *                 example: "RS123456"
 *               date_circulation:
 *                 type: string
 *                 format: date
 *                 example: "2023-01-15"
 *               poids:
 *                 type: number
 *                 example: 15000
 *               unite_poids:
 *                 type: string
 *                 example: "kg"
 *               volume:
 *                 type: number
 *                 example: 30.5
 *               nombre_palettes:
 *                 type: integer
 *                 example: 33
 *               palette_met_eur:
 *                 type: string
 *                 example: "EUR"
 *               longeur:
 *                 type: number
 *                 example: 13.6
 *               largeur:
 *                 type: number
 *                 example: 2.5
 *               hauteur:
 *                 type: number
 *                 example: 2.7
 *               image_carte_grise:
 *                 type: string
 *                 example: "http://example.com/image.jpg"
 *               ajoutee_par:
 *                 type: integer
 *                 example: 5
 *     responses:
 *       201:
 *         description: Camion créé avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 type_camion:
 *                   type: string
 *                 immatriculation:
 *                   type: string
 *                 immat_rs_tn:
 *                   type: string
 *                 date_circulation:
 *                   type: string
 *                   format: date
 *                 poids:
 *                   type: number
 *                 unite_poids:
 *                   type: string
 *                 volume:
 *                   type: number
 *                 nombre_palettes:
 *                   type: integer
 *                 palette_met_eur:
 *                   type: string
 *                 longeur:
 *                   type: number
 *                 largeur:
 *                   type: number
 *                 hauteur:
 *                   type: number
 *                 image_carte_grise:
 *                   type: string
 *                 ajoutee_par:
 *                   type: integer
 *       400:
 *         description: Requête invalide - corps manquant
 *       500:
 *         description: Erreur serveur
 */


/**
 * @swagger
 * /api/camion:
 *   get:
 *     summary: Récupérer la liste des camions
 *     tags: [Camion]
 *     responses:
 *       200:
 *         description: Liste des camions récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                     example: 1
 *                   type_camion:
 *                     type: string
 *                     example: "Semi-remorque"
 *                   immatriculation:
 *                     type: string
 *                     example: "123 TU 456"
 *                   immat_rs_tn:
 *                     type: string
 *                     example: "RS123456"
 *                   date_circulation:
 *                     type: string
 *                     format: date
 *                     example: "2023-01-15"
 *                   poids:
 *                     type: number
 *                     example: 15000
 *                   unite_poids:
 *                     type: string
 *                     example: "kg"
 *                   volume:
 *                     type: number
 *                     example: 30.5
 *                   nombre_palettes:
 *                     type: integer
 *                     example: 33
 *                   palette_met_eur:
 *                     type: string
 *                     example: "EUR"
 *                   longeur:
 *                     type: number
 *                     example: 13.6
 *                   largeur:
 *                     type: number
 *                     example: 2.5
 *                   hauteur:
 *                     type: number
 *                     example: 2.7
 *                   image_carte_grise:
 *                     type: string
 *                     example: "http://example.com/image.jpg"
 *                   ajoutee_par:
 *                     type: integer
 *                     example: 5
 *       500:
 *         description: Erreur serveur lors de la récupération des camions
 */


/**
 * @swagger
 * /api/camion/delate/{camionId}:
 *   get:
 *     summary: Supprimer un camion par ID
 *     tags: [Camion]
 *     parameters:
 *       - in: path
 *         name: camionId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID du camion à supprimer
 *     responses:
 *       200:
 *         description: Camion supprimé avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: camion was deleted successfully!
 *       404:
 *         description: Camion non trouvé
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Not found camion with id 123.
 *       500:
 *         description: Erreur serveur lors de la suppression
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Could not delete camion with id 123
 */


/**
 * @swagger
 * /api/camion/{camionId}:
 *   get:
 *     summary: Récupérer un camion par ID
 *     tags: [Camion]
 *     parameters:
 *       - in: path
 *         name: camionId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID du camion à récupérer
 *     responses:
 *       200:
 *         description: Données du camion trouvé
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   example: 1
 *                 type_camion:
 *                   type: string
 *                   example: "Camion léger"
 *                 immatriculation:
 *                   type: string
 *                   example: "123ABC"
 *                 immat_rs_tn:
 *                   type: string
 *                   example: "RS123456"
 *                 date_circulation:
 *                   type: string
 *                   format: date
 *                   example: "2023-01-15"
 *                 poids:
 *                   type: number
 *                   example: 1500
 *                 unite_poids:
 *                   type: string
 *                   example: "kg"
 *                 volume:
 *                   type: number
 *                   example: 12.5
 *                 nombre_palettes:
 *                   type: integer
 *                   example: 10
 *                 palette_met_eur:
 *                   type: integer
 *                   example: 5
 *                 longeur:
 *                   type: number
 *                   example: 6.5
 *                 largeur:
 *                   type: number
 *                   example: 2.5
 *                 hauteur:
 *                   type: number
 *                   example: 3.0
 *                 image_carte_grise:
 *                   type: string
 *                   example: "image123.jpg"
 *                 ajoutee_par:
 *                   type: integer
 *                   example: 42
 *       404:
 *         description: Camion non trouvé
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Not found camion with id 123.
 *       500:
 *         description: Erreur serveur lors de la récupération
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Error retrieving camion with id 123
 */


/**
 * @swagger
 * /api/camionTr/{idUser}:
 *   get:
 *     summary: Récupérer tous les camions ajoutés par un utilisateur spécifique
 *     tags: [Camion]
 *     parameters:
 *       - in: path
 *         name: idUser
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID de l'utilisateur (ajoutee_par)
 *     responses:
 *       200:
 *         description: Liste des camions trouvés
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                     example: 1
 *                   type_camion:
 *                     type: string
 *                     example: "Camion léger"
 *                   immatriculation:
 *                     type: string
 *                     example: "123ABC"
 *                   immat_rs_tn:
 *                     type: string
 *                     example: "RS123456"
 *                   date_circulation:
 *                     type: string
 *                     format: date
 *                     example: "2023-01-15"
 *                   poids:
 *                     type: number
 *                     example: 1500
 *                   unite_poids:
 *                     type: string
 *                     example: "kg"
 *                   volume:
 *                     type: number
 *                     example: 12.5
 *                   nombre_palettes:
 *                     type: integer
 *                     example: 10
 *                   palette_met_eur:
 *                     type: integer
 *                     example: 5
 *                   longeur:
 *                     type: number
 *                     example: 6.5
 *                   largeur:
 *                     type: number
 *                     example: 2.5
 *                   hauteur:
 *                     type: number
 *                     example: 3.0
 *                   image_carte_grise:
 *                     type: string
 *                     example: "image123.jpg"
 *                   ajoutee_par:
 *                     type: integer
 *                     example: 42
 *                   transporteur:
 *                     type: string
 *                     example: "JohnDoe"
 *       404:
 *         description: Aucun camion trouvé pour cet utilisateur
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Not found Camion with idUser 123
 *       500:
 *         description: Erreur serveur lors de la récupération des camions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Error retrieving Camion with idUser 123
 */
