import { Component } from '@angular/core';
import { NgStyle } from '@angular/common';
import { IconDirective } from '@coreui/icons-angular';
import { ContainerComponent, RowComponent, ColComponent, CardGroupComponent, TextColorDirective, CardComponent, CardBodyComponent, FormDirective, InputGroupComponent, InputGroupTextDirective, FormControlDirective, ButtonDirective } from '@coreui/angular';
import { AuthService } from '../../../../services/auth.service';
import { Router } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';

@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styleUrls: ['./login.component.scss'],
    imports: [ContainerComponent, RowComponent, ColComponent, CardGroupComponent, TextColorDirective, CardComponent, CardBodyComponent, FormDirective, InputGroupComponent, InputGroupTextDirective, IconDirective, FormControlDirective, ButtonDirective, NgStyle]
})
export class LoginComponent {
  username: string = '';
  password: string = '';
  errorMessage: string = '';

  constructor(private AuthService:AuthService,private router:Router) { }

  onSubmit(): void{
    this.AuthService.login({"email":this.username, "mot_de_passe":this.password}).subscribe({
      next: (success) =>{
        if (success) {
          this.router.navigate(['/dashboard']);
        } else {
          this.errorMessage = 'email ou mot de passse invalide';
        }
      },
      error: () => {
        this.errorMessage = 'Une erreur s\'est produite. Veuillez réessayer.'
      },
    }
      
    );
  }
}
