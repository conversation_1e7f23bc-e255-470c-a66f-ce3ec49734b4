const FournisseurModel = require("../models/fournisseur.model.js");
const cron = require('node-cron');

exports.create = (req, res) => {
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty"
    });
    return;
  }

  const fournisseur = new FournisseurModel({
    nom_fournisseur: req.body.nom_fournisseur,
    mat_fisc : req.body.mat_fisc,
    sous_traitant : req.body.sous_traitant,
    adresse : req.body.adresse,
    mail:req.body.mail,
    flux:req.body.flux,
    entreposage:req.body.entreposage,
    code_cegid:req.body.code_cegid,
    code_sageX3:req.body.code_sageX3
  });


  FournisseurModel.create(fournisseur, (err, data) => {

    if (err) {
      res.status(500).send({
        message: err.message || "Some error occurred while creating the fournisseur."
      });
    } else {
      res.send(data);
    }
  });
};

exports.findAll = (req, res) => {
    FournisseurModel.findAll((err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Some error occurred while retrieving fournisseur."
      });
    } else {
      res.send(data);
    }
  });
};
exports.findClientToInvoice = (req, res) => {
  FournisseurModel.findClientToInvoice((err, data) => {
  if (err) {
    res.status(500).send({
      message: err.message || "Some error occurred while retrieving fournisseur."
    });
  } else {
    res.send(data);
  }
});
};

exports.findClientInvoiced = (req, res) => {
  FournisseurModel.findClientInvoiced((err, data) => {
  if (err) {
    res.status(500).send({
      message: err.message || "Some error occurred while retrieving fournisseur."
    });
  } else {
    res.send(data);
  }
});
};



exports.findById = (req, res) => {
  const id = req.params.id; 
  FournisseurModel.findById(id, (err, data) => { 
    if (err) {
      res.status(500).send({
        message: err.message || "Some error occurred while retrieving fournisseur."
      });
    } else {
      res.send(data);
    }
  });
};





exports.findFournisseurWhithFLux = (req, res) => {
  FournisseurModel.findFournisseurWhithFLux((err, data) => {
  if (err) {
    res.status(500).send({
      message: err.message || "Some error occurred while retrieving fournisseur."
    });
  } else {
    res.send(data);
  }
});
};
exports.findFournisseurWhithEntreposage = (req, res) => {
  FournisseurModel.findFournisseurWhithEntreposage((err, data) => {
  if (err) {
    res.status(500).send({
      message: err.message || "Some error occurred while retrieving fournisseur."
    });
  } else {
    res.send(data);
  }
});
};




exports.delete = (req, res) => {
  const fournisseurId = req.params.id; // Make sure this line is correct and provides a value

  FournisseurModel.delete(fournisseurId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `fournisseur with id ${fournisseurId} not found.`
        });
      } else {
        res.status(500).send({
          message: err.message || `Some error occurred while deleting the fournisseur with id ${fournisseurId}.`
        });
      }
    } else {
      res.send({
        message: 'fournisseur deleted successfully.'
      });
    }
  });
};

exports.findByMatricule = (req, res) => {
    const matriculeFiscal = req.params.mat_fisc;
      FournisseurModel.findByMatricule(matriculeFiscal, (err, fournisseur) => {
      if (err) {
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la recherche du fournisseur par matricule fiscal',
          error: err
        });
      }
  
      if (!fournisseur || fournisseur.length === 0) {
        return res.status(204).json({
          success: false,
          message: 'Aucun fournisseur trouvé avec le matricule fiscal spécifié'
        });
      }
  
      res.status(200).json({
        success: true,
        message: 'Fournisseur déja existant',
        fournisseur: fournisseur
      });
    });
  };




  exports.findByType = (req, res) => {
    const type = req.params.type; // Supposons que le matricule fiscal soit passé en tant que paramètre dans l'URL
  
    FournisseurModel.findByType(type, (err, fournisseur) => {
      if (err) {
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la recherche du fournisseur par matricule fiscal',
          error: err
        });
      }
  
      if (!fournisseur || fournisseur.length === 0) {
        return res.status(204).json({
          success: false,
          message: 'Aucun fournisseur trouvé avec le matricule fiscal spécifié'
        });
      }
  
      res.status(200).json({
        success: true,
        fournisseur: fournisseur
      });
    });
  };


  exports.update = (req, res) => {
    const fournisseurId = req.params.id;
    const { adresse, sous_traitant,mail,entreposage,flux,code_cegid } = req.body;

  
    // Mettre à jour le fournisseur dans la base de données
    FournisseurModel.update(fournisseurId, adresse, sous_traitant,mail,entreposage,flux,code_cegid, (err, data) => {
        if (err) {
            if (err.kind === "not_found") {
                return res.status(404).send({ message: `Fournisseur avec l'ID ${fournisseurId} non trouvé.` });
            } else {
                return res.status(500).send({ message: `Une erreur s'est produite lors de la mise à jour du fournisseur avec l'ID ${fournisseurId}.` });
            }
        } else {
            return res.send(data);
        }
    });
};

exports.findClientFromSageByMat = (req, res) => {
  const mat = req.params.mat;  // Extract the 'mat' parameter from the request

  FournisseurModel.findClientFromSageByMat(mat, (err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Some error occurred while retrieving the client."
      });
    } else if (!data) {
      res.status(404).send({
        message: `No client found with matricule: ${mat}`
      });
    } else {
      res.send(data);
    }
  });
};



