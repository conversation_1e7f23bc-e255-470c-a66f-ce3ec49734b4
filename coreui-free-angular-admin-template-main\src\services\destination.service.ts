import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class DestinationService {
  private apiURL = environment.apiURL;
  private destination: any;
  private typeList: any;

  constructor(private http: HttpClient) { }

  /**
   * Add new destination
   * @param destinationData - Destination data
   * @returns Observable with creation result
   * @originalName addDestination
   */
  addDestination(destinationData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'destination', destinationData, httpOptions);
  }

  /**
   * Get destination by city, supplier and type
   * @param city - City
   * @param supplier - Supplier
   * @param type - Type
   * @returns Observable with destination data
   * @originalName getDestinationByVilleAndFournisseur
   */
  getDestinationByCityAndSupplier(city: any, supplier: any, type: any): Observable<any> {
    return this.http.get<any>(this.apiURL + `destination/${city}/${supplier}/${type}`, httpOptions);
  }

  /**
   * Find all destinations that have type
   * @returns Observable with destinations list
   * @originalName findAllDestinationHaveType
   */
  findAllDestinationsWithType(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'findAllDestinationHaveType', httpOptions);
  }

  /**
   * Find destinations by client ID
   * @param clientId - Client ID
   * @returns Observable with destinations list
   * @originalName findByIdClient
   */
  findByClientId(clientId: any): Observable<any> {
    return this.http.get<any>(this.apiURL + `destination/${clientId}`, httpOptions);
  }

  /**
   * Update destination
   * @param id - Destination ID
   * @param destinationData - Updated destination data
   * @returns Observable with update result
   * @originalName updateDestination
   */
  updateDestination(id: any, destinationData: any): Observable<any> {
    const url = `${this.apiURL}updateDestination/${id}`;
    return this.http.put(url, destinationData, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Delete destination
   * @param id - Destination ID
   * @returns Observable with deletion result
   * @originalName deleteDestination
   */
  deleteDestination(id: any): Observable<any> {
    const url = `${this.apiURL}delete/${id}`;
    return this.http.delete(url, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Set destination data in service
   * @param data - Destination data
   * @originalName setDestination
   */
  setDestination(data: any): void {
    this.destination = data;
    console.log("Destination updated:", this.destination);
  }

  /**
   * Get destination data from service
   * @returns Current destination data
   * @originalName getDestination
   */
  getDestination(): any {
    console.log("Destination retrieved:", this.destination);
    return this.destination;
  }

  /**
   * Update destination types
   * @param id - Destination ID
   * @param typeData - Type data
   * @returns Observable with update result
   * @originalName updateDestinationTypes
   */
  updateDestinationTypes(id: any, typeData: any): Observable<any> {
    const url = `${this.apiURL}updateDestinationType/${id}`;
    return this.http.put(url, typeData, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Find destination by ID
   * @param id - Destination ID
   * @returns Observable with destination data
   * @originalName findDestinationById
   */
  findDestinationById(id: any): Observable<any> {
    return this.http.get<any>(this.apiURL + `findDestinationById/${id}`, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Find destinations by company
   * @param clientId - Client ID
   * @returns Observable with destinations list
   * @originalName findDestinationByCompany
   */
  findDestinationsByCompany(clientId: any): Observable<any> {
    return this.http.get<any>(this.apiURL + `findDestinationByCompany/${clientId}`, httpOptions);
  }

  /**
   * Assign destination to user
   * @param userId - User ID
   * @param destinationData - Destination data
   * @returns Observable with assignment result
   * @originalName assignDestinationToUser
   */
  assignDestinationToUser(userId: any, destinationData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + `assignDestinationToUser/${userId}`, destinationData, httpOptions);
  }

  /**
   * Find destinations by user
   * @param clientId - Client ID
   * @returns Observable with destinations list
   * @originalName findDestinationByUser
   */
  findDestinationsByUser(clientId: any): Observable<any> {
    return this.http.get<any>(this.apiURL + `findDestinationByUser/${clientId}`, httpOptions);
  }

  /**
   * Delete destination by user
   * @param id - Destination ID
   * @returns Observable with deletion result
   * @originalName deleteDestinationByUser
   */
  deleteDestinationByUser(id: any): Observable<any> {
    return this.http.get<any>(this.apiURL + `deleteDestinationByUser/${id}`, httpOptions);
  }

  /**
   * Find all warehouses
   * @returns Observable with warehouses list
   * @originalName findAllWarhouses
   */
  findAllWarehouses(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'findAllWarhouses', httpOptions);
  }

  /**
   * Find warehouses by brand
   * @param brandId - Brand ID
   * @param type - Type
   * @returns Observable with warehouses list
   * @originalName findWarhousesByBrand
   */
  findWarehousesByBrand(brandId: number, type: any): Observable<any> {
    return this.http.get<any>(this.apiURL + `findWarhousesByBrand/${brandId}/${type}`, httpOptions);
  }

  /**
   * Add warehouse
   * @param warehouseData - Warehouse data
   * @returns Observable with creation result
   * @originalName addWarehouse
   */
  addWarehouse(warehouseData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'addWarehouse', warehouseData, httpOptions);
  }

  /**
   * Disable warehouse
   * @param id - Warehouse ID
   * @returns Observable with disable result
   * @originalName disableWharehouse
   */
  disableWarehouse(id: any): Observable<any> {
    return this.http.put<any>(this.apiURL + `disabledDepot/${id}`, null, httpOptions);
  }

  /**
   * Handle HTTP errors
   * @param error - Error object
   * @returns Observable error
   */
  private handleError(error: any): Observable<never> {
    console.error('An error occurred:', error);
    return throwError(() => new Error(error.message || 'Server error'));
  }
}
