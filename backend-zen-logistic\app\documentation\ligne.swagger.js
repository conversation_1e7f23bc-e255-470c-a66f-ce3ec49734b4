/**
 * @swagger
 * tags:
 *   name: Ptchargement
 *   description: Opérations liées à la gestion des points de chargement
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Ptchargement:
 *       type: object
 *       required:
 *         - telephone
 *         - idCommande
 *         - from_destination
 *         - to_destination
 *         - type_ligne
 *         - volume
 *         - id_client
 *         - date_depart
 *         - date_arrivee
 *         - quantite
 *         - id_demandeur
 *       properties:
 *         id:
 *           type: integer
 *           description: Identifiant unique du point de chargement
 *         telephone:
 *           type: string
 *           description: Numéro de téléphone associé
 *         idCommande:
 *           type: integer
 *           description: Identifiant de la commande
 *         from_destination:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *               description: ID de la destination de départ
 *             nom_locale:
 *               type: string
 *               description: Nom local de la destination de départ
 *         to_destination:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *               description: ID de la destination d'arrivée
 *             nom_locale:
 *               type: string
 *               description: Nom local de la destination d'arrivée
 *         type_ligne:
 *           type: string
 *           description: Type de ligne de transport
 *         volume:
 *           type: number
 *           description: Volume associé au point de chargement
 *         id_client:
 *           type: integer
 *           description: Identifiant du client
 *         date_depart:
 *           type: string
 *           format: date
 *           description: Date de départ
 *         date_arrivee:
 *           type: string
 *           format: date
 *           description: Date d'arrivée
 *         quantite:
 *           type: integer
 *           description: Quantité associée
 *         status:
 *           type: string
 *           description: Statut du point de chargement
 *         estimation:
 *           type: number
 *           description: Estimation associée
 *         sku:
 *           type: string
 *           description: SKU du produit
 */

/**
 * @swagger
 * /api/ptchargement:
 *   post:
 *     tags: [Ptchargement]
 *     summary: Créer un nouveau point de chargement
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Ptchargement'
 *     responses:
 *       201:
 *         description: Point de chargement créé avec succès
 *       400:
 *         description: Requête invalide
 *       500:
 *         description: Erreur interne du serveur

 * /api/ptchargement/valid:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer tous les points de chargement valides
 *     responses:
 *       200:
 *         description: Liste des points de chargement valides récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Ptchargement'
 *       500:
 *         description: Erreur interne du serveur



 * /api/ptchargement/delete/{ptchargementId}:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Supprimer un point de chargement par ID
 *     parameters:
 *       - in: path
 *         name: ptchargementId
 *         required: true
 *         description: ID du point de chargement à supprimer
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Point de chargement supprimé avec succès
 *       404:
 *         description: Point de chargement non trouvé
 *       500:
 *         description: Erreur interne du serveur

 * /api/ptchargement/{idCommande}:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer les détails d'un point de chargement par ID de commande
 *     parameters:
 *       - in: path
 *         name: idCommande
 *         required: true
 *         description: ID de la commande
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Détails du point de chargement récupérés avec succès
 *       404:
 *         description: Commande non trouvée
 *       500:
 *         description: Erreur interne du serveur

 * /api/pt/reserved:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer les points de chargement réservés par date et camion
 *     parameters:
 *       - in: query
 *         name: id_camion
 *         required: true
 *         description: ID du camion
 *         schema:
 *           type: integer
 *       - in: query
 *         name: date
 *         required: true
 *         description: Date de réservation
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: id_conducteur
 *         required: true
 *         description: ID du conducteur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des points de chargement réservés récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/pt/expedied:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer les points de chargement expédiés
 *     responses:
 *       200:
 *         description: Liste des points de chargement expédiés récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/countValid:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Compter le nombre de points de chargement valides
 *     responses:
 *       200:
 *         description: Nombre de points de chargement valides récupéré avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/countExpedied:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Compter le nombre de points de chargement expédiés
 *     responses:
 *       200:
 *         description: Nombre de points de chargement expédiés récupéré avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/countReserved:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Compter le nombre de points de chargement réservés
 *     responses:
 *       200:
 *         description: Nombre de points de chargement réservés récupéré avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/countDelivred:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Compter le nombre de points de chargement livrés
 *     responses:
 *       200:
 *         description: Nombre de points de chargement livrés récupéré avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/findVoyageList:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer la liste des voyages
 *     responses:
 *       200:
 *         description: Liste des voyages récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/findVoyageListExp:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer la liste des voyages expédiés
 *     responses:
 *       200:
 *         description: Liste des voyages expédiés récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/findVoyageListToAdjust:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer la liste des voyages à ajuster
 *     responses:
 *       200:
 *         description: Liste des voyages à ajuster récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/findAllLivred:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer tous les points de chargement livrés
 *     responses:
 *       200:
 *         description: Liste de tous les points de chargement livrés récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/findAllAdjusted:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer tous les points de chargement ajustés
 *     responses:
 *       200:
 *         description: Liste de tous les points de chargement ajustés récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/findAllToInspection:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer tous les points de chargement à inspecter
 *     responses:
 *       200:
 *         description: Liste de tous les points de chargement à inspecter récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/ptdechargement/{idCommande}:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer les détails de déchargement par ID de commande
 *     parameters:
 *       - in: path
 *         name: idCommande
 *         required: true
 *         description: ID de la commande
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Détails de déchargement récupérés avec succès
 *       404:
 *         description: Commande non trouvée
 *       500:
 *         description: Erreur interne du serveur

 * /api/ptchargement/reservation/{id}:
 *   put:
 *     tags: [Ptchargement]
 *     summary: Mettre à jour la réservation d'un point de chargement par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du point de chargement à mettre à jour
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id_conducteur:
 *                 type: integer
 *                 description: ID du conducteur
 *               id_camion:
 *                 type: integer
 *                 description: ID du camion
 *               date_voyage:
 *                 type: string
 *                 format: date
 *                 description: Date du voyage
 *               H_depart:
 *                 type: string
 *                 description: Heure de départ
 *               H_arrivee:
 *                 type: string
 *                 description: Heure d'arrivée
 *               tolerance:
 *                 type: number
 *                 description: Tolerance pour le voyage
 *     responses:
 *       200:
 *         description: Réservation mise à jour avec succès
 *       404:
 *         description: Point de chargement non trouvé
 *       500:
 *         description: Erreur interne du serveur

 * /api/ptchargement/updateStatusNoPickup/{id}:
 *   put:
 *     tags: [Ptchargement]
 *     summary: Mettre à jour le statut 'no pickup' d'un point de chargement par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du point de chargement à mettre à jour
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Statut mis à jour avec succès
 *       404:
 *         description: Point de chargement non trouvé
 *       500:
 *         description: Erreur interne du serveur

 * /api/ptchargement/livred/{id}:
 *   put:
 *     tags: [Ptchargement]
 *     summary: Mettre à jour le statut 'livré' d'un point de chargement par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du point de chargement à mettre à jour
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Statut mis à jour avec succès
 *       404:
 *         description: Point de chargement non trouvé
 *       500:
 *         description: Erreur interne du serveur

 * /api/ptchargement/client/{id}:
 *   put:
 *     tags: [Ptchargement]
 *     summary: Mettre à jour le client d'un point de chargement par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du point de chargement à mettre à jour
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id_client:
 *                 type: integer
 *                 description: ID du nouveau client
 *     responses:
 *       200:
 *         description: Client mis à jour avec succès
 *       404:
 *         description: Point de chargement non trouvé
 *       500:
 *         description: Erreur interne du serveur

 * /api/updateStatusToBeInvoiced/{id}:
 *   put:
 *     tags: [Ptchargement]
 *     summary: Mettre à jour le statut à 'à facturer' d'un point de chargement par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du point de chargement à mettre à jour
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Statut mis à jour avec succès
 *       404:
 *         description: Point de chargement non trouvé
 *       500:
 *         description: Erreur interne du serveur

 * /api/findLignesByweek/{intervalle}:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer les lignes par semaine
 *     parameters:
 *       - in: path
 *         name: intervalle
 *         required: true
 *         description: Intervalle en semaines pour récupérer les lignes
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des lignes récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/findRetard:
 *   post:
 *     tags: [Ptchargement]
 *     summary: Récupérer les lignes en retard
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date_debut:
 *                 type: string
 *                 format: date
 *                 description: Date de début pour la recherche
 *               date_fin:
 *                 type: string
 *                 format: date
 *                 description: Date de fin pour la recherche
 *     responses:
 *       200:
 *         description: Liste des lignes en retard récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/getProductivity:
 *   post:
 *     tags: [Ptchargement]
 *     summary: Récupérer la productivité
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date_debut:
 *                 type: string
 *                 format: date
 *                 description: Date de début pour la productivité
 *               date_fin:
 *                 type: string
 *                 format: date
 *                 description: Date de fin pour la productivité
 *     responses:
 *       200:
 *         description: Détails de la productivité récupérés avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/MoyVolumePerVoyage:
 *   post:
 *     tags: [Ptchargement]
 *     summary: Récupérer la moyenne de volume par voyage
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date_debut:
 *                 type: string
 *                 format: date
 *                 description: Date de début pour le calcul
 *               date_fin:
 *                 type: string
 *                 format: date
 *                 description: Date de fin pour le calcul
 *     responses:
 *       200:
 *         description: Moyenne de volume par voyage récupérée avec succès
 *       500:
 *         description: Erreur
 * /api/voyagePerDestination:
 *   post:
 *     tags: [Ptchargement]
 *     summary: Récupérer les voyages par destination
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date_debut:
 *                 type: string
 *                 format: date
 *                 description: Date de début pour la recherche
 *               date_fin:
 *                 type: string
 *                 format: date
 *                 description: Date de fin pour la recherche
 *               destination_id:
 *                 type: integer
 *                 description: ID de la destination
 *     responses:
 *       200:
 *         description: Détails des voyages par destination récupérés avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/getIndicateurByConducteur:
 *   post:
 *     tags: [Ptchargement]
 *     summary: Récupérer les indicateurs par conducteur
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date_debut:
 *                 type: string
 *                 format: date
 *                 description: Date de début pour la recherche
 *               date_fin:
 *                 type: string
 *                 format: date
 *                 description: Date de fin pour la recherche
 *               id_conducteur:
 *                 type: integer
 *                 description: ID du conducteur
 *               id_destination:
 *                 type: integer
 *                 description: ID de la destination
 *     responses:
 *       200:
 *         description: Détails des indicateurs récupérés avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/updateVolumeXls:
 *   post:
 *     tags: [Ptchargement]
 *     summary: Mettre à jour le volume à partir d'un fichier XLS
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   description: ID du point de chargement
 *                 volume:
 *                   type: number
 *                   description: Nouveau volume
 *     responses:
 *       200:
 *         description: Mise à jour du volume réussie
 *       500:
 *         description: Erreur interne du serveur

 * /api/ptchargement/fixPrice:
 *   post:
 *     tags: [Ptchargement]
 *     summary: Fixer le prix d'un point de chargement
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *                 description: ID du point de chargement
 *               price:
 *                 type: number
 *                 description: Nouveau prix à fixer
 *     responses:
 *       200:
 *         description: Prix fixé avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/ptchargement/updateVolumeColisage:
 *   post:
 *     tags: [Ptchargement]
 *     summary: Mettre à jour le volume de colisage
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   description: ID du point de chargement
 *                 volume:
 *                   type: number
 *                   description: Nouveau volume
 *     responses:
 *       200:
 *         description: Volume de colisage mis à jour avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/ptchargement/pdfNotUpdated:
 *   get:
 *     tags: [Ptchargement]
 *     summary: Récupérer les points de chargement pour lesquels le PDF n'a pas été mis à jour
 *     responses:
 *       200:
 *         description: Liste des points de chargement récupérée avec succès
 *       500:
 *         description: Erreur interne du serveur

 * /api/ptchargement/findByDestinationsAndDateVoyage:
 *   post:
 *     tags: [Ptchargement]
 *     summary: Récupérer les lignes par destination et date de voyage
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Date du voyage
 *               depotEmetteur:
 *                 type: string
 *                 description: Dépôt émetteur
 *               depotRecepteur:
 *                 type: string
 *                 description: Dépôt récepteur
 *     responses:
 *       200:
 *         description: Lignes récupérées avec succès
 *       500:
 *         description: Erreur interne du serveur
 */
