import { inject } from '@angular/core';
import { CanActivateFn, CanActivateChildFn, Router } from '@angular/router';
import { map, take, catchError } from 'rxjs';
import { of } from 'rxjs';
import { AuthService } from '../services/auth.service';
import { RoleService } from '../services/role.service';

export const roleGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const roleService = inject(RoleService);
  const router = inject(Router);

  // Get required roles from route data
  const requiredRoles = route.data['roles'] as string[] || route.data['expectedRole'] as string[] || [];

  // If no roles are required, allow access
  if (requiredRoles.length === 0) {
    return true;
  }

  return authService.user$.pipe(
    take(1),
    map(user => {
      if (!user) {
        console.log('Role guard: No user found, redirecting to login');
        router.navigate(['/login']);
        return false;
      }

      // Check if user has any of the required roles
      const userRole = user.role || user.type_utilisateur;
      const hasRequiredRole = requiredRoles.some(role =>
        userRole === role ||
        roleService.hasRole(role) ||
        roleService.hasAnyRole(requiredRoles)
      );

      if (hasRequiredRole) {
        console.log(`Role guard: Access granted. User role: ${userRole}, Required roles: ${requiredRoles.join(', ')}`);
        return true;
      } else {
        console.log(`Role guard: Access denied. User role: ${userRole}, Required roles: ${requiredRoles.join(', ')}`);
        router.navigate(['/unauthorized']);
        return false;
      }
    }),
    catchError(error => {
      console.error('Role guard error:', error);
      router.navigate(['/unauthorized']);
      return of(false);
    })
  );
};

export const roleChildGuard: CanActivateChildFn = (childRoute, state) => {
  return roleGuard(childRoute, state);
};
