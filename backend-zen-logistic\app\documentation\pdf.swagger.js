/**
 * @swagger
 * tags:
 *   name: Colisage
 *   description: Opérations liées à la gestion des colisages
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Colisage:
 *       type: object
 *       required:
 *         - depotEmetteur
 *         - depotRecepteur
 *         - date_voyage
 *       properties:
 *         id:
 *           type: integer
 *           description: Identifiant unique du colisage
 *         volume:
 *           type: number
 *           description: Volume du colisage
 *         depotEmetteur:
 *           type: string
 *           description: Dépôt de l'émetteur
 *         depotRecepteur:
 *           type: string
 *           description: Dépôt du récepteur
 *         fileName:
 *           type: string
 *           description: Nom du fichier associé
 *         status:
 *           type: string
 *           description: Statut du colisage
 *         nom_demandeur:
 *           type: string
 *           description: Nom du demandeur
 *         idUser:
 *           type: integer
 *           description: Identifiant de l'utilisateur
 *         date_voyage:
 *           type: string
 *           format: date
 *           description: Date du voyage
 *         visible:
 *           type: boolean
 *           description: Indicateur de visibilité du colisage
 */

/**
 * @swagger
 * /api/pdf/upload:
 *   post:
 *     tags: [Colisage]
 *     summary: Téléverser un fichier PDF et extraire les données
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: Données extraites du PDF avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 depotEmetteur:
 *                   type: string
 *                 depotRecepteur:
 *                   type: string
 *                 date:
 *                   type: string
 *                 volume:
 *                   type: number
 *       400:
 *         description: Aucune fichier fourni ou erreur de traitement
 *       500:
 *         description: Erreur interne du serveur

 * /api/addColisage:
 *   post:
 *     tags: [Colisage]
 *     summary: Ajouter un nouveau colisage
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Colisage'
 *     responses:
 *       201:
 *         description: Colisage créé avec succès
 *       400:
 *         description: Requête invalide
 *       500:
 *         description: Erreur interne du serveur

 * /api/findByUser/{idUser}:
 *   get:
 *     tags: [Colisage]
 *     summary: Trouver les colisages d'un utilisateur par ID
 *     parameters:
 *       - in: path
 *         name: idUser
 *         required: true
 *         description: ID de l'utilisateur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des colisages de l'utilisateur
 *       500:
 *         description: Erreur interne du serveur

 * /api/colisages/{id}:
 *   put:
 *     tags: [Colisage]
 *     summary: Mettre à jour un colisage par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du colisage à mettre à jour
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Colisage'
 *     responses:
 *       200:
 *         description: Colisage mis à jour avec succès
 *       400:
 *         description: Requête invalide
 *       404:
 *         description: Colisage non trouvé
 *       500:
 *         description: Erreur interne du serveur

 * /api/updateColisageStatus/{id}:
 *   put:
 *     tags: [Colisage]
 *     summary: Mettre à jour le statut d'un colisage par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du colisage à mettre à jour
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *     responses:
 *       200:
 *         description: Statut du colisage mis à jour avec succès
 *       400:
 *         description: Requête invalide
 *       404:
 *         description: Colisage non trouvé
 *       500:
 *         description: Erreur interne du serveur

 * /api/colisages:
 *   get:
 *     tags: [Colisage]
 *     summary: Récupérer tous les colisages avec pagination
 *     parameters:
 *       - in: query
 *         name: page
 *         required: false
 *         description: Numéro de la page
 *         schema:
 *           type: integer
 *       - in: query
 *         name: limit
 *         required: false
 *         description: Limite d'éléments par page
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste paginée des colisages
 *       500:
 *         description: Erreur interne du serveur

 * /api/updateVisibleById/{id}:
 *   put:
 *     tags: [Colisage]
 *     summary: Mettre à jour la visibilité d'un colisage par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du colisage à mettre à jour
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Visibilité du colisage mise à jour avec succès
 *       404:
 *         description: Colisage non trouvé
 *       500:
 *         description: Erreur interne du serveur
 */
