import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class OrderService {
  private apiURL = environment.apiURL;
  private currentOrderId: any;

  constructor(private http: HttpClient) { }

  /**
   * Add new order
   * @param orderData - Order data
   * @returns Observable with creation result
   */
  addOrder(orderData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'commande', orderData, httpOptions);
  }

  /**
   * Add order (Observable version)
   * @param orderData - Order data
   * @returns Observable with creation result
   */
  addOrderObservable(orderData: any): Observable<any> {
    return this.http.post<any>(this.apiURL + 'commande', orderData, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get all registered orders by user
   * @param id - User ID
   * @returns Observable with orders list
   */
  getAllRegisteredOrders(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'getAllCommande/' + id, httpOptions);
  }

  /**
   * Get all orders
   * @returns Observable with orders list
   */
  getAllOrders(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande', httpOptions);
  }

  /**
   * Find order by ID
   * @param id - Order ID
   * @returns Observable with order data
   */
  findOrder(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande/' + id, httpOptions);
  }

  /**
   * Find registered order by ID
   * @param id - Order ID
   * @returns Observable with order data
   */
  findRegisteredOrder(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande/' + id, httpOptions);
  }

  /**
   * Update order
   * @param orderData - Updated order data
   * @param id - Order ID
   * @returns Observable with update result
   */
  updateOrder(orderData: any, id: string): Observable<any> {
    return this.http.put<any>(this.apiURL + 'comm/update/' + id, orderData, httpOptions);
  }

  /**
   * Update order note
   * @param noteData - Note data
   * @param id - Order ID
   * @returns Observable with update result
   */
  updateOrderNote(noteData: any, id: string): Observable<any> {
    return this.http.put<any>(this.apiURL + 'comm/updatenote/' + id, noteData, httpOptions);
  }

  /**
   * Update order volume
   * @param orderId - Order ID
   * @param newVolume - New volume value
   * @returns Observable with update result
   */
  updateOrderVolume(orderId: string, newVolume: number): Observable<any> {
    const url = `${this.apiURL}commande/updateVolume/${orderId}`;
    const body = { volume: newVolume };

    return this.http.put(url, body, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get all orders by user
   * @param id - User ID
   * @returns Observable with orders list
   */
  getAllOrdersByUser(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'comm/' + id, httpOptions);
  }

  /**
   * Update order (expeditor version)
   * @param orderData - Updated order data
   * @param id - Order ID
   * @returns Observable with update result
   */
  updateOrderExpeditor(orderData: any, id: string): Observable<any> {
    return this.http.put<any>(this.apiURL + 'comm/up/' + id, orderData, httpOptions);
  }

  /**
   * Get all orders by user with error handling
   * @param id - User ID
   * @returns Observable with orders list
   */
  getAllOrdersByUserWithErrorHandling(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'com/' + id, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get all validated orders by transporter
   * @param id - Transporter ID
   * @returns Observable with orders list
   */
  getAllValidatedOrdersByTransporter(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'comTrpValide/' + id, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get reserved orders
   * @returns Observable with reserved orders list
   */
  getReservedOrders(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'comareserve/', httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get all reserved orders
   * @returns Observable with reserved orders list
   */
  getAllReservedOrders(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande/Reserved/All', httpOptions);
  }

  /**
   * Find orders by expeditor
   * @param id - Expeditor ID
   * @returns Observable with orders list
   */
  findOrdersByExpeditor(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande/exp/' + id, httpOptions);
  }

  /**
   * Update order for transporter (reserve order)
   * @param orderData - Updated order data
   * @param id - Order ID
   * @returns Observable with update result
   */
  updateOrderTransporter(orderData: any, id: string): Observable<any> {
    return this.http.put<any>(this.apiURL + 'comande/' + id, orderData, httpOptions);
  }

  /**
   * Find all reserved orders by transporter
   * @param id - Transporter ID
   * @returns Observable with reserved orders list
   */
  findAllReservedOrdersByTransporter(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande/Reservees/' + id, httpOptions);
  }

  /**
   * Find reserved orders by expeditor
   * @param id - Expeditor ID
   * @returns Observable with reserved orders list
   */
  findReservedOrdersByExpeditor(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'commande/Res/' + id, httpOptions);
  }

  /**
   * Delete registered order
   * @param id - Order ID
   * @returns Observable with deletion result
   */
  deleteRegisteredOrder(id: number): Observable<any> {
    return this.http.put<any>(`${this.apiURL}comm/deleteEnr/${id}`, null, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Delete order with lines
   * @param id - Order ID
   * @returns Observable with deletion result
   */
  deleteOrderWithLines(id: string): Observable<any> {
    return this.http.delete<any>(`${this.apiURL}commande/${id}`, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Set current order ID
   * @param id - Order ID
   */
  setCurrentOrderId(id: any): void {
    this.currentOrderId = id;
  }

  /**
   * Get current order ID
   * @returns Current order ID
   */
  getCurrentOrderId(): any {
    return this.currentOrderId;
  }

  /**
   * Get all orders by chef
   * @param chefId - Chef ID
   * @returns Observable with orders list
   */
  getAllOrdersByChef(chefId: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `getAllCommandeByChef/${chefId}`, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get orders to validate by chef
   * @param chefId - Chef ID
   * @returns Observable with orders list
   */
  getOrdersToValidateByChef(chefId: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `getCommandeToValidateByChef/${chefId}`, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Find registered orders by chef
   * @param chefId - Chef ID
   * @returns Observable with orders list
   */
  findRegisteredOrdersByChef(chefId: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `findEnrgBychef/${chefId}`, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Get all validated orders by chef
   * @param chefId - Chef ID
   * @returns Observable with orders list
   */
  getAllValidatedOrdersByChef(chefId: string): Observable<any> {
    return this.http.get<any>(this.apiURL + `getAllComValidByChef/${chefId}`, httpOptions)
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Handle HTTP errors
   * @param error - Error object
   * @returns Observable error
   */
  private handleError(error: any): Observable<never> {
    console.error('An error occurred:', error);
    return throwError(() => new Error(error.message || 'Server error'));
  }
}
