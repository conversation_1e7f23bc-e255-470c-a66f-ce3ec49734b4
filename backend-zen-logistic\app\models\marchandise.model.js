const sql = require("./db.js");

// Constructor
const MarchandiseModel = function(marchandise) {
    //console.log("rrrrrrrrr",marchandise)
  this.nom_marchandise = marchandise.nom_marchandise;
};

MarchandiseModel.create = (newMarchandise, result) => {
  sql.query("INSERT INTO marchandise SET ?", newMarchandise, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    //console.log("created customer: ", { id: res.insertId, ...newMarchandise });
    result(null, { id: res.insertId, ...newMarchandise });
  });
};

MarchandiseModel.findAll = (result) => {
    sql.query("SELECT * FROM marchandise", (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(err, null);
            return;
        }

        //console.log("marchandises: ", res);
        result(null, res);
    });
};

MarchandiseModel.delete = (marchandiseId, result) => {
  sql.query("DELETE FROM marchandise WHERE id = ?", marchandiseId, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      // No marchandise found with the given id
      result({ kind: "not_found" }, null);
      return;
    }

    //console.log("Deleted marchandise with id: ", marchandiseId);
    result(null, res);
  });
};

module.exports = MarchandiseModel;
