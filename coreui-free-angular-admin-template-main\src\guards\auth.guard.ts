import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { map, take } from 'rxjs';
import { AuthService } from '../services/auth.service';

export const authGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  return authService.isAuthenticated$.pipe(
    take(1),
    map(isAuthenticated => {
      if (isAuthenticated) {
        // User is authenticated, allow access
        return true;
      } else {
        // User is not authenticated, redirect to login
        console.log('Access denied. Redirecting to login...');

        // Store the attempted URL for redirecting after login
        sessionStorage.setItem('redirectUrl', state.url);

        // Navigate to login page
        router.navigate(['/login']);
        return false;
      }
    })
  );
};
