/**
 * @swagger
 * tags:
 *   - name: Flux
 *     description: Opérations sur les Flux
 *
 * @swagger
 * components:
 *   schemas:
 *     Flux:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: ID du flux
 *         description:
 *           type: string
 *           description: Description du flux
 *         montant:
 *           type: number
 *           format: float
 *           description: Montant du flux
 *
 * /api/flux:
 *   get:
 *     tags:
 *       - Flux
 *     summary: Obtenir tous les flux
 *     responses:
 *       200:
 *         description: Liste des flux
 *       500:
 *         description: Erreur lors de la récupération des flux
 *
 * /api/getByClientAndDate:
 *   post:
 *     tags:
 *       - Flux
 *     summary: Obtenir des flux par client et date
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id_client:
 *                 type: integer
 *               date:
 *                 type: string
 *                 format: date
 *     responses:
 *       200:
 *         description: Flux trouvés
 *       400:
 *         description: Données invalides
 *       500:
 *         description: Erreur interne
 *
 * /api/getFluxByidFacture/{id_facture}:
 *   get:
 *     tags:
 *       - Flux
 *     summary: Obtenir des flux par ID de facture
 *     parameters:
 *       - name: id_facture
 *         in: path
 *         required: true
 *         description: ID de la facture
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Flux trouvés
 *       404:
 *         description: Flux non trouvés
 *       500:
 *         description: Erreur lors de la récupération
 *
 * /api/brandFlux:
 *   get:
 *     tags:
 *       - Flux
 *     summary: Obtenir toutes les marques
 *     responses:
 *       200:
 *         description: Liste des marques
 *       500:
 *         description: Erreur lors de la récupération des marques
 */
