const VoyagetModel = require ("../models/voyage.model.js") ; 


exports.deleteVoyage = (req, res) => { 
  const voyageId = req.params.id;

  VoyagetModel.deleteVoyage(voyageId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Voyage with id ${voyageId} not found.`,
        });
      } else if (err.kind === "status_not_adjusted") {
        res.status(400).send({
          message: "Not all rows in `point_chargement` have status 'Ajusté'.",
        });
      } else {
        res.status(500).send({
          message: err.message || `Some error occurred while updating the voyage with id ${voyageId}.`,
        });
      }
    } else {
      res.send({
        message: "Voyage cancelled successfully and associated rows updated to 'Livré'.",
      });
    }
  });
};



exports.searchVoyageByDate = (req, res) => {
  const { date_debut, date_fin } = req.body;

  if (!date_debut || !date_fin) {
    return res.status(400).send({ message: "Les dates de début et de fin sont requises." });
  }

  VoyagetModel.searchByDate(date_debut, date_fin, (err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Une erreur s'est produite lors de la récupération des voyages.",
      });
    } else {
      res.send(data);
    }
  });
};