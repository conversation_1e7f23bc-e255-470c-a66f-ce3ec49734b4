import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Get notifications by user
   * @param userId - User ID
   * @returns Observable with notifications list
   */
  getNotificationsByUser(userId: string): Observable<any> {
    return this.http.get<any>(`${this.apiURL}notifications/${userId}`, httpOptions);
  }

  /**
   * Mark notification as read
   * @param notificationId - Notification ID
   * @returns Observable with update result
   */
  markAsRead(notificationId: string): Observable<any> {
    return this.http.put<any>(`${this.apiURL}notifications/read/${notificationId}`, null, httpOptions);
  }

  /**
   * Create notification
   * @param notificationData - Notification data
   * @returns Observable with creation result
   */
  createNotification(notificationData: any): Observable<any> {
    return this.http.post<any>(`${this.apiURL}notifications`, notificationData, httpOptions);
  }

  /**
   * Delete notification
   * @param notificationId - Notification ID
   * @returns Observable with deletion result
   */
  deleteNotification(notificationId: string): Observable<any> {
    return this.http.delete<any>(`${this.apiURL}notifications/${notificationId}`, httpOptions);
  }
}
