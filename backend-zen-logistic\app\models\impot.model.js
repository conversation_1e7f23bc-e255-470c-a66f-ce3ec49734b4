const sql = require("./db.js");
const Impot = function(impot) {
    this.id= impot.id ;
    this.timbre = impot.timbre ;
    this.tva_tr = impot.tva_tr ;
    this.tva_entreposage = impot.tva_entreposage ;
    this.tva_flux = impot.tva_flux ;
    this.prix_flux = impot.prix_flux
    this.prix_flux_mp = impot.prix_flux_mp
    this.prix_entreposage_mp = impot.prix_entreposage_mp ;
    this.prix_entreposage = impot.prix_entreposage
  } ;



  Impot.getAll = result => {
    sql.query("SELECT * FROM impot", (err, res) => {
      if (err) {
        console.log("SQL error: ", err);
        result(err, null); // Remplacer null par err en cas d'erreur
        return;
      }
  
      console.log("Impot: ", res);
      result(null, res);
    });
  };

  Impot.updateById = (id, updatedFields, result) => {
    // Vérification des valeurs dans updatedFields
    const hasValidValues = Object.values(updatedFields).every(value => value !== null  && value !== '');
    
    if (!hasValidValues) {
        result({ kind: "invalid_input" }, null);
        return;
    }

    const fieldsToUpdate = Object.keys(updatedFields)
      .map(key => `${key} = ?`)
      .join(", ");
    const values = Object.values(updatedFields);

    sql.query(
      `UPDATE impot SET ${fieldsToUpdate} WHERE id = ?`,
      [...values, id],
      (err, res) => {
        if (err) {
          console.log("Erreur SQL : ", err);
          result(err, null);
          return;
        }

        if (res.affectedRows == 0) {
          // Pas trouvé d'enregistrement avec l'ID
          result({ kind: "not_found" }, null);
          return;
        }

        console.log("Mise à jour réussie : ", { id: id, ...updatedFields });
        result(null, { id: id, ...updatedFields });
      }
    );
};

  

  module.exports = Impot ; 
