
const axios = require('axios');
const FormData = require('form-data');
const Colisages = require("../models/pdfModel.model.js");

// Remplacez ceci par vos clés publiques et secrètes CompDF
const PUBLIC_KEY = process.env.PUBLIC_KEY;
const SECRET_KEY = process.env.SECRET_KEY;

// Fonction pour obtenir le token d'accès
async function getAccessToken() {
    try {
        const response = await axios.post('https://api-server.compdf.com/server/v1/oauth/token', {
            publicKey: PUBLIC_KEY,
            secretKey: SECRET_KEY
        });

        console.log("getAccessToken response.data", response.data);

        if (response.data && response.data.data && response.data.data.accessToken) {
            return response.data.data.accessToken;
        } else {
            throw new Error('Échec de l\'authentification');
        }
    } catch (error) {
        console.error('Erreur lors de l\'obtention du token d\'accès:', error.message);
        throw error;
    }
}

// Fonction pour créer une tâche
async function createTask(accessToken) {
    try {
        const response = await axios.get('https://api-server.compdf.com/server/v1/task/pdf/txt', {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        console.log("createTask response.data", response.data);

        if (response.data && response.data.data && response.data.data.taskId) {
            return response.data.data.taskId;
        } else {
            throw new Error('Échec de la création de la tâche');
        }
    } catch (error) {
        console.error('Erreur lors de la création de la tâche:', error.message);
        throw error;
    }
}

// Fonction pour uploader le fichier PDF
async function uploadFile(accessToken, taskId, fileBuffer) {
    try {
        const uploadForm = new FormData();
        uploadForm.append('file', fileBuffer, { filename: 'file.pdf' });
        uploadForm.append('taskId', taskId);
        uploadForm.append('parameter', JSON.stringify({ isAllowOcr: 0, isContainOcrBg: 0 }));
        uploadForm.append('language', '');

        const response = await axios.post('https://api-server.compdf.com/server/v1/file/upload', uploadForm, {
            headers: {
                ...uploadForm.getHeaders(),
                'Authorization': `Bearer ${accessToken}`
            }
        });

        console.log("uploadFile response.data", response.data);

        if (response.data.error) {
            throw new Error(response.data.message);
        }
    } catch (error) {
        console.error('Erreur lors de l\'upload du fichier:', error.message);
        throw error;
    }
}

// Fonction pour démarrer le traitement
async function startProcessing(accessToken, taskId) {
    try {
        const response = await axios.get(`https://api-server.compdf.com/server/v1/execute/start?taskId=${taskId}`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        console.log("startProcessing response.data", response.data);

        if (response.data.error) {
            throw new Error(response.data.message);
        }
    } catch (error) {
        console.error('Erreur lors du démarrage du traitement:', error.message);
        throw error;
    }
}

// Fonction pour obtenir les informations de la tâche
async function getTaskInfo(accessToken, taskId) {
    try {
        const response = await axios.get(`https://api-server.compdf.com/server/v1/task/taskInfo?taskId=${taskId}`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        console.log("getTaskInfo response.data", response.data);

        return response.data;
    } catch (error) {
        console.error('Erreur lors de la récupération des informations de la tâche:', error.message);
        throw error;
    }
}

function extractSpecificInfo(textContent) {
    // Extraire tous les volumes à partir du texte
    const volumeMatches = [...textContent.matchAll(/Totale Volume:\s*([\d,\.]+)/g)];
    const volumes = volumeMatches.map(match => parseFloat(match[1].replace(',', '.')));
    const volume = Math.max(...volumes);
    const depotEmetteurMatch = textContent.match(/Dépôt émetteur: (.*)/);
    const depotRecepteurMatch = textContent.match(/Dépôt récepteur: (.*)/);
    const dateMatch = textContent.match(/Date: (\d{2}\/\d{2}\/\d{4})/);
    const depotEmetteur = depotEmetteurMatch ? depotEmetteurMatch[1].trim() : "";
    const depotRecepteur = depotRecepteurMatch ? depotRecepteurMatch[1].trim() : "";
    const date = dateMatch ? dateMatch[1] : "";

    return { depotEmetteur, depotRecepteur, date, volume };
}



// Fonction principale pour extraire les données PDF
exports.extractPdfData = async (req, res) => {
    try {
        const fileBuffer = req.file && req.file.buffer;
        if (!fileBuffer) {
            return res.status(400).json({ error: "Aucun fichier fourni." });
        }

        const accessToken = await getAccessToken();
        const taskId = await createTask(accessToken);
        await uploadFile(accessToken, taskId, fileBuffer);
        await startProcessing(accessToken, taskId);

        const checkTaskStatus = async () => {
            try {
                const taskInfo = await getTaskInfo(accessToken, taskId);
                console.log("checkTaskStatus taskInfo", taskInfo);

                if (taskInfo && taskInfo.data) {
                    switch (taskInfo.data.taskStatus) {
                        case 'TaskFinish':
                            if (taskInfo.data.fileInfoDTOList && taskInfo.data.fileInfoDTOList.length > 0) {
                                const textFileInfo = taskInfo.data.fileInfoDTOList[0];
                                const textFileUrl = textFileInfo.downloadUrl;
                                console.log('URL du fichier texte:', textFileUrl); // Log de l'URL du fichier texte

                                if (textFileUrl) {
                                    const textResponse = await axios.get(textFileUrl, {
                                        headers: { 'Authorization': `Bearer ${accessToken}` }
                                    });

                                    const textContent = textResponse.data;
                                    console.log('Texte extrait:', textContent);

                                    // Extraire les informations spécifiques du texte
                                    const extractedInfo = extractSpecificInfo(textContent);
                                    console.log('Informations extraites:', extractedInfo);

                                    res.json(extractedInfo);
                                } else {
                                    res.status(404).json({ error: 'Aucun URL de fichier texte trouvé.' });
                                }
                            } else {
                                res.status(404).json({ error: 'Aucun fichier texte trouvé.' });
                            }
                            break;
                        case 'TaskFail':
                            res.status(500).json({ error: 'Le traitement a échoué.' });
                            break;
                        default:
                            setTimeout(checkTaskStatus, 5000); // Vérifiez à nouveau après 5 secondes
                            break;
                    }
                } else {
                    console.error('Erreur: la réponse de l\'API ne contient pas les informations attendues.');
                    res.status(500).json({ error: 'Erreur lors de la récupération des informations de la tâche.' });
                }
            } catch (error) {
                console.error('Erreur lors de la vérification du statut de la tâche:', error.message);
                res.status(500).json({ error: 'Erreur lors de la vérification du statut de la tâche.' });
            }
        };

        checkTaskStatus();
    } catch (error) {
        console.error('Erreur lors de l\'extraction des données PDF :', error.message);
        if (error.response) {
            return res.status(error.response.status).json({ error: error.response.data.message });
        }
        res.status(500).send('Erreur serveur');
    }
};

exports.addColisage = (req, res) => {
 Colisages.addColisage(req.body, (err, data) => {
    if (err) {
      if (err.kind === "already_exists") {
        res.status(400).send({
          message: err.message || "A colisage with the same depotEmetteur, depotRecepteur, and date_voyage already exists."
        });
      } else {
        res.status(500).send({
          message:
            err.message || "Some error occurred while creating the Colisage."
        });
      }
    } else res.send(data);
  });
 
};


exports.findByUser = (req, res) => {
  const idUser = req.params.idUser;
  Colisages.findByUser(idUser, (err, data) => {
    if (err) {
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving colisages for the user."
      });
    } else res.send(data);
  });
};


exports.updateVisibleById  = (req, res) => {
  const id = req.params.id;
  console.log(id)
  Colisages.updateVisibleById(id, (err, data) => {
    if (err) {
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving colisages for the user."
      });
    } else res.send(data);
  });
};


exports.updateColisage = (req, res) => {
  // Validate Request
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    });
  }

  const id = req.params.id;
  const colisage = req.body;

  Colisages.updateColisage(id, colisage, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Colisage with id ${id}.`
        });
      } else {
        res.status(500).send({
          message: "Error updating Colisage with id " + id
        });
      }
    } else res.send(data);
  });
};


exports.updateColisageStatus = (req, res) => {
  // Validate Request
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    });
  }

  const id = req.params.id;
  const colisage = req.body;

  Colisages.updateColisageStatus(id, colisage, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Colisage with id ${id}.`
        });
      } else {
        res.status(500).send({
          message: "Error updating Colisage with id " + id
        });
      }
    } else res.send(data);
  });
};
exports.getAllWithPagination = (req, res) => {
  const { page, limit } = req.query;

  Colisages.getAllWithPagination(parseInt(page), parseInt(limit), (err, data) => {
      if (err) {
          res.status(500).send({
              message: err.message || "Some error occurred while retrieving Colisage."
          });
      } else {
          res.send(data);
      }
  });
};



