const Prices = require("../models/prices.model.js");

// const prices = new Prices ({
 
   
//     id : req.body.id,
//     distance : req.body.distance,
//     prix_court_distance : req.body.prix_court_distance,
//     prix_long_distance : req.body.prix_long_distance,
//     type : req.body.type,

 
//   });

exports.findAllPrices = (req, res) => {
    Prices.findAllPrices((err, data) => {
      if (err)
        res.status(500).send({
          message:
            err.message || "Some error occurred while retrieving Ville."
        });
      else res.send(data);
    });
   
  };


  exports.updatePrice = (req, res) => {
    const { id } = req.params;
    const { distance, prix_court_distance, prix_long_distance,montant,pourcentage  } = req.body;
    console.log(distance, prix_court_distance, prix_long_distance,id)
  
    Prices.updatePrice(id,  distance, prix_court_distance, prix_long_distance,montant,pourcentage , (err, updatedPrice) => {
      if (err) {
        if (err.kind === "not_found") {
          return res.status(404).json({ message: 'Prix non trouvé' });
        } else {
          return res.status(500).json({ message: 'Erreur lors de la mise à jour du Prix' });
        }
      }
  
      res.json({ message: 'Prix mis à jour avec succès' });
    });
  };