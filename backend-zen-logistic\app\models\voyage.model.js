const sql = require("./db.js");
const Voyage = function(ville) {

  } ;

Voyage.deleteVoyage = (circuitId, result) => { 
    // Étape 1 : Récupérer les lignes de `point_chargement` associées à `id_voyage`
    sql.query("SELECT * FROM `point_chargement` WHERE id_voyage = ?", [circuitId], (err, rows) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      // Étape 2 : Vérifier si toutes les lignes ont le statut "Ajusté"
      const allAdjusted = rows.every(row => row.status === "Ajusté" || row.status === "Annulé");
  
      if (!allAdjusted) {
        result({ kind: "status_not_adjusted" }, null);
        return;
      }
  
      // Étape 3 : Mettre à jour leur statut à "Livré"
      sql.query(
        "UPDATE `point_chargement` SET status = 'Livré',id_voyage=NULL WHERE id_voyage = ?",
        [circuitId],
        (err, res) => {
          if (err) {
            console.log("error: ", err);
            result(err, null);
            return;
          }
  
          // Étape 4 : Mettre à jour le champ `cancelled` à `true` dans la table `voyage`
          sql.query(
            "UPDATE `voyage` SET cancelled = true WHERE id = ?",
            [circuitId],
            (err, res) => {
              if (err) {
                console.log("error: ", err);
                result(err, null);
                return;
              }
  
              if (res.affectedRows == 0) {
                result({ kind: "not_found" }, null);
                return;
              }
  
              result(null, { message: "Voyage updated and cancelled successfully." });
            }
          );
        }
      );
    });
  };
  

  Voyage.searchByDate = (date_debut, date_fin, result) => {
    sql.query(
      "SELECT * FROM voyage WHERE date_voyage BETWEEN ? AND ? AND cancelled = False",
      [date_debut, date_fin],
      (err, res) => {
        if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
        }
  
        result(null, res);
      }
    );
  };

  module.exports = Voyage ; 
