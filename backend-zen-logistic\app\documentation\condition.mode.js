/**
 * @swagger
 * tags:
 *   name: Condition
 *   description: Gestion des conditions
 */

/**
 * @swagger
 * /api/condition:
 *   post:
 *     summary: Crée une nouvelle condition
 *     tags: [Condition]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - nom_condition
 *             properties:
 *               nom_condition:
 *                 type: string
 *                 example: Condition spéciale
 *     responses:
 *       200:
 *         description: Condition créée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: integer
 *                   example: 1
 *                 nom_condition:
 *                   type: string
 *                   example: Condition spéciale
 *       400:
 *         description: Requête invalide, contenu vide
 *       500:
 *         description: Erreur serveur lors de la création
 *
 *   get:
 *     summary: Récupère toutes les conditions
 *     tags: [Condition]
 *     responses:
 *       200:
 *         description: Liste des conditions
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                     example: 1
 *                   nom_condition:
 *                     type: string
 *                     example: Condition spéciale
 *       500:
 *         description: Erreur serveur lors de la récupération
 */

/**
 * @swagger
 * /api/condition/{id}:
 *   delete:
 *     summary: Supprime une condition par ID
 *     tags: [Condition]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID de la condition à supprimer
 *     responses:
 *       200:
 *         description: Condition supprimée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Condition deleted successfully.
 *       404:
 *         description: Condition non trouvée
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Condition with id 123 not found.
 *       500:
 *         description: Erreur serveur lors de la suppression
 */
