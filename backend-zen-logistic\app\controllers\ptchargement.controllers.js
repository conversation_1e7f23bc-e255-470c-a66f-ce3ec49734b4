const Ptchargement = require ("../models/ptchargement.model.js") ; 
const axios = require('axios');
const https = require('https');
const SmsModel = require ("../models/sms_history.model.js") ; 

// create a pt chargement 
exports.create = (req, res)=>
{ 
  //console.log('ptchargement')
  if(!req.body){
    res.status(400).send({
      message: "Content can not be empty"
    })
  }
 
  //console.log('Received data in Ptchargement controller:', req.body);

 const ptchargement = new Ptchargement({
  telephone: req.body.telephone,
  idCommande: req.body.idCommande,
  from_destination: req.body.from_destination.id,
  to_destination: req.body.to_destination.id,
  nom_depart: req.body.from_destination.nom_locale,
  nom_arrivee: req.body.to_destination.nom_locale,
  kilometrage: req.body.kilometrage,
  type_ligne: req.body.type_ligne,
  volume: req.body.volume,
  id_client : req.body.id_client,
  date_depart : req.body.date_depart,
  date_arrivee : req.body.date_arrivee,
  ajoutee_par : req.body.ajoutee_par,
  quantite : req.body.quantite,
  status: null,
  id_demandeur : req.body.id_demandeur,
  estimation : req.body.estimation,
  sku : req.body.sku
});

 /*//console.log('ttt')
//console.log(conducteur)  */

 // Save ptchargement in the database
 Ptchargement.create(ptchargement, (err, data) => {
  // //console.log('test');
   if (err)
     res.status(500).send({
       message:
         err.message || "Some error occurred while creating the ptchargement ."
     });
   else res.send(data);
 });
}

// Retrieve all ptchargement from the database.
exports.findAll = (req, res) => {
    Ptchargement.getAll((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving ptchargement."
      });
    else res.send(data);
  });
 
};  

// Delete a ptchargement with the specified ptchargementId 
exports.delete = (req, res) => {
  Ptchargement.remove(req.params.ptchargementId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found ptchargement with id ${req.params.ptchargementId}.`
        });
      } else {
        res.status(500).send({
          message: "Could not delete ptchargement with id " + req.params.ptchargementId
        });
      }
    } else res.send({ message: `ptchargement was deleted successfully!` });
  });
}; 



// Delete a ptchar with the specified ptCharId in the request
exports.delete = (req, res) => {
  Ptchargement.remove(req.params.ptcharId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found ptchar with id ${req.params.ptcharId}.`
        });
      } else {
        res.status(500).send({
          message: "Could not delete ptchar with id " + req.params.ptcharId
        });
      }
    } else res.send({ message: `ptchar was deleted successfully!` });
  });
};

exports.getPtdech = (req, res) => {
  //console.log('rrrr')
  Ptchargement.getPtdech(req.params.idCommande ,  (err, data) => {
    //console.log(req.params)
  //console.log(data)
  if (err) {
    if (err.kind === "not_found") {
      res.status(404).send({
        message: `Not found ptChargement with idCommande ${req.params.idCommande} `
      });
    } else {
      res.status(500).send({
        message: "Error retrieving ptChargement with idCommande " + req.params.idCommande 
      });
    }
  } else res.send(data);
});
};

exports.getPtdechChar = (req, res) => {
  //console.log('rrrr')
  Ptchargement.getALLByCMD(req.params.idCommande ,  (err, data) => {
    //console.log(req.params)
  //console.log(data)
  if (err) {
    if (err.kind === "not_found") {
      res.status(404).send({
        message: `Not found ptChargement and pt decha with idCommande ${req.params.idCommande} `
      });
    } else {
      res.status(500).send({
        message: "Error retrieving ptChargement and pt with idCommande " + req.params.idCommande 
      });
    }
  } else res.send(data);
});
};
exports.getComm = (req, res) => {
  ////console.log('rrrr')
  Ptchargement.getComm(req.params.idCommande ,  (err, data) => {
    //console.log(req.params)
  //console.log(data)
  if (err) {
    if (err.kind === "not_found") {
      res.status(404).send({
        message: `Not found ptChargement with idCommande ${req.params.idCommande} `
      });
    } else {
      res.status(500).send({
        message: "Error retrieving ptChargement with idCommande " + req.params.idCommande 
      });
    }
  } else res.send(data);
});
};
exports.addLieuxDechar = (req, res) => {
  // Validate Request
 if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    });
  }

 
  Ptchargement.addLieuxDechar(
    req.body ,
    (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found Customer `
          });
        } else {
          res.status(500).send({
            message: "Error setting client"
          });
        }
      } else res.send(data);
    }
  );
};
exports.getLienuxDecharByCustomer = (req, res) => {
  // Validate Request
 if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    });
  }

 
  Ptchargement.getLienuxDecharByCustomer(
    req.body ,
    (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Not found Customer `
          });
        } else {
          res.status(500).send({
            message: "Error setting client"
          });
        }
      } else res.send(data);
    }
  );
};

exports.updateVolume = (req, res) => {
  const { id } = req.params;
  const { volume } = req.body;

  Ptchargement.updateVolume(id, volume, (err, updatedPt) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de la mise à jour du volume' });
      }
    }

    res.json({ message: 'Volume mis à jour avec succès', pt: updatedPt });
  });
};

exports.updateStatusValid = (req, res) => {
  const { id } = req.params;

  Ptchargement.updateStatusValid(id, (err, updatedPt) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de la mise à jour du volume' });
      }
    }

    res.json({ message: 'Status mis à jour avec succès', pt: updatedPt });
  });
};


exports.updateStatusCancel = (req, res) => {
  const { id } = req.params;

  Ptchargement.updateStatusCancel(id, (err, updatedPt) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de la mise à jour du volume' });
      }
    }

    res.json({ message: 'Status mis à jour avec succès', pt: updatedPt });
  });
};


exports.updateStatusReady = (req, res) => {
  const { id, status } = req.body;

  // Vérification des paramètres requis
  if (id === undefined || status === undefined) {
    return res.status(400).json({ message: 'Les champs "id" et "status" sont requis' });
  }

  Ptchargement.updateStatusReady(id, status, (err, updatedPt) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de la mise à jour du statut' });
      }
    }

    res.json({ message: 'Statut mis à jour avec succès', pt: updatedPt });
  });
};


exports.findAllValid = (req, res) => {
  Ptchargement.findAllValid((err, data) => {
  if (err)
    res.status(500).send({
      message:
        err.message || "Some error occurred while retrieving ptchargement."
    });
  else res.send(data);
});

};  



exports.updateReserved = (req, res) => {
  const { updates } = req.body; // `updates` est une liste d'objets contenant les détails à mettre à jour

  if (!Array.isArray(updates) || updates.length === 0) {
    return res.status(400).json({ message: 'Liste des mises à jour invalide ou vide' });
  }

  Ptchargement.updateReserved(updates, (err, updatedPts) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Un ou plusieurs points de chargement non trouvés' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de la mise à jour des points de chargement' });
      }
    }

    res.json({ message: 'Points de chargement mis à jour avec succès', updatedPts });
  });
};





exports.findReservedByDateAndCamion = (req, res) => {
  const { id_camion, date, id_conducteur } = req.query;
  //console.log("------------------------",req.query)

  Ptchargement.findReservedByDateAndCamion(id_camion, date,id_conducteur, (err, result) => {
    if (err) {
      return res.status(500).json({ error: 'Internal Server Error' });
    }

    res.status(200).json(result);
  });
};

exports.updateReservation = (req, res) => {
  //console.log("///////////////////",req.body)
  const { id } = req.params;
  
    const {id_conducteur, id_camion,date_voyage,H_depart,H_arrivee,tolerance } = req.body;
  
    Ptchargement.updateReservation(id, id_camion, id_conducteur,date_voyage,H_depart,H_arrivee,tolerance, (err, updatedPt) => {
      if (err) {
        if (err.kind === "not_found") {
          return res.status(404).json({ message: 'Point de chargement non trouvé' });
        } else {
          return res.status(500).json({ message: 'Erreur lors de la mise à jour du point de chargement' });
        }
      }
  
      res.json({ message: 'Point de chargement mis à jour avec succès', pt: updatedPt });
    });
  };

  exports.updateExpedition = (req, res) => {
    const { id } = req.params;
  
    Ptchargement.updateExpedition(id)
      .then((updatedPt) => {
        // Retourne un statut 200 avec les détails mis à jour
        res.status(200).json({ 
          message: 'Point de chargement mis à jour avec succès', 
          pt: updatedPt 
        });
      })
      .catch((err) => {
        if (err.kind === "not_found") {
          // Retourne un statut 404 si le point n'est pas trouvé
          return res.status(404).json({ message: 'Point de chargement non trouvé' });
        }
        // Retourne un statut 500 pour toute autre erreur
        res.status(500).json({ message: 'Erreur lors de la mise à jour du point de chargement' });
      });
  };
  

    // exports.adjustPrice = (req, res) => {
    //   //console.log("Requête reçue:", req.body);
    
    //   const { idList } = req.body;
    
    //   if (!Array.isArray(idList) || idList.length === 0) {
    //     return res.status(400).json({
    //       message: 'La liste des identifiants doit être un tableau non vide.',
    //     });
    //   }
    
    //   Ptchargement.adjustPrice(idList, (err, updatedPt) => {
    //     if (err) {
    //       if (err.kind === 'not_found') {
    //         return res.status(404).json({
    //           message: 'Point de chargement non trouvé pour les identifiants fournis.',
    //         });
    //       } else {
    //         console.error('Erreur lors de la mise à jour:', err);
    //         return res.status(500).json({
    //           message: 'Erreur interne lors de la mise à jour du point de chargement.',
    //         });
    //       }
    //     }
    
    //     res.status(200).json({
    //       message: 'Point de chargement mis à jour avec succès.',
    //       updatedPoints: updatedPt,
    //     });
    //   });
    // };
    
    exports.updatePanne = (req, res) => {
      //console.log("///////////////////",req.body)
      const { id } = req.params;
      
       // const {id} = req.body;
      
        Ptchargement.updatePanne(id ,(err) => {
          if (err) {
            if (err.kind === "not_found") {
              return res.status(200).json({ message: 'Point de chargement non trouvé' });
            } else {
              return res.status(500).json({ message: 'Erreur lors de la mise à jour du point de chargement' });
            }
          }
      
          res.json({ message: 'Point de chargement mis à jour avec succès' });
        });
      };

      exports.updateStatusLivred = async (req, res) => {
        const { id } = req.params;
      
        try {
          const updatedPt = await Ptchargement.updateStatusLivred(id);
          res.status(200).json({ message: 'Status mis à jour avec succès', pt: updatedPt }); 
        } catch (err) {
          if (err.kind === "not_found") {
            return res.status(404).json({ message: 'Point de chargement non trouvé' });
          } else {
            return res.status(500).json({ message: 'Erreur lors de la mise à jour du volume', error: err });
          }
        }
      };
      

    exports.findExpedied = (req, res) => {
      const { id_camion, date, id_conducteur } = req.query;
      //console.log("------------------------",req.query)
    
      Ptchargement.findExpedied(id_camion, date,id_conducteur, (err, result) => {
        if (err) {
          return res.status(500).json({ error: 'Internal Server Error' });
        }
    
        res.status(200).json(result);
      });
    };
    exports.findLineToAdjust = (req, res) => {
      const { id_camion, date, id_conducteur } = req.query;
      //console.log("------------------------",req.query)
    
      Ptchargement.findLineToAdjust(id_camion, date,id_conducteur, (err, result) => {
        if (err) {
          return res.status(500).json({ error: 'Internal Server Error' });
        }
    
        res.status(200).json(result);
      });
    };
    

    

    exports.findAFactureByClient = (req, res) => {
      const { date_debut, date_fin, id_client } = req.query;
      //console.log("------------------------",req.query)
    
      Ptchargement.findAFactureByClient(date_debut, date_fin,id_client, (err, result) => {
        if (err) {
          return res.status(500).json({ error: 'Internal Server Error' });
        }
    
        res.status(200).json(result);
      });
    };

    exports.findAllLivred = (req, res) => {
      Ptchargement.findAllLivred((err, result) => {
        if (err) {
          return res.status(500).json({ error: 'Internal Server Error' });
        }
    
        res.status(200).json(result);
      });
    };

    exports.findAllAdjusted = (req, res) => {
      Ptchargement.findAllAdjusted((err, result) => {
        if (err) {
          return res.status(500).json({ error: 'Internal Server Error' });
        }
    
        res.status(200).json(result);
      });
    };

    
    
    exports.findLivredByDateAndtype = (req, res) => {
      const { date_debut, date_fin, type_ligne } = req.query;
    
      Ptchargement.findLivredByDateAndtype(date_debut, date_fin, type_ligne, (err, result) => {
        if (err) {
          return res.status(500).json({ error: 'Internal Server Error' });
        }
    
        res.status(200).json(result);
      });
    };




    exports.updateClient = (req, res) => {
      //console.log("///////////////////",req.body)
      const { id } = req.params;
      const {id_client} = req.body
      
       // const {id} = req.body;
      
        Ptchargement.updateClient(id,id_client, (err, updatedPt) => {
          if (err) {
            if (err.kind === "not_found") {
              return res.status(200).json({ message: 'Point de chargement non trouvé' });
            } else {
              return res.status(500).json({ message: 'Erreur lors de la mise à jour du point de chargement' });
            }
          }
      
          res.json({ message: 'Point de chargement mis à jour avec succès', pt: updatedPt });
        });
      };








  
      exports.findReservedByIdUser = (req, res) => {
        const { id_demandeur } = req.params;
        //console.log("------------------------",req.query)
      
        Ptchargement.findReservedByIdUser(id_demandeur, (err, result) => {
          if (err) {
            return res.status(500).json({ error: 'Internal Server Error' });
          }
      
          res.status(200).json(result);
        });
      };


      exports.findExpediedByIdUser = (req, res) => {
        const { id_demandeur } = req.params;
        //console.log("------------------------",req.query)
      
        Ptchargement.findExpediedByIdUser(id_demandeur, (err, result) => {
          if (err) {
            return res.status(500).json({ error: 'Internal Server Error' });
          }
      
          res.status(200).json(result);
        });
      };



      exports.findLignesByTypeAndSku = (req, res) => {
        const { type , sku } = req.body;
        //console.log("------------------------",req.query)
      
        Ptchargement.findLignesByTypeAndSku(type,sku, (err, result) => {
          if (err) {
            return res.status(500).json({ error: 'Internal Server Error' });
          }
      
          res.status(200).json(result);
        });
      };


      exports.findLignesByIdToVerification = (req, res) => {
        const { id } = req.params;
        //console.log("------------------------",req.query)
      
        Ptchargement.findLignesByIdToVerification(id, (err, result) => {
          if (err) {
            return res.status(500).json({ error: 'Internal Server Error' });
          }
      
          res.status(200).json(result);
        });
      };

    


      

      

      exports.findLivredByIdUser = (req, res) => {
        const { id_demandeur } = req.params;
        //console.log("------------------------",req.query)
      
        Ptchargement.findLivredByIdUser(id_demandeur, (err, result) => {
          if (err) {
            return res.status(500).json({ error: 'Internal Server Error' });
          }
      
          res.status(200).json(result);
        });
      };
      


      exports.countValid = (req, res) => {
        Ptchargement.countValid((err, data) => {
          if (err) {
            return res.status(500).send({
              message: err.message || "Une erreur s'est produite lors de la récupération des données."
            });
          }
          res.json({ numberOfValidPoints: data });
        });
      };

      exports.countExpedied = (req, res) => {
        Ptchargement.countExpedied((err, data) => {
          if (err) {
            return res.status(500).send({
              message: err.message || "Une erreur s'est produite lors de la récupération des données."
            });
          }
          res.json({ numberOfValidPoints: data });
        });
      };

      exports.countReserved = (req, res) => {
        Ptchargement.countReserved((err, data) => {
          if (err) {
            return res.status(500).send({
              message: err.message || "Une erreur s'est produite lors de la récupération des données."
            });
          }
          res.json({ numberOfValidPoints: data });
        });
      };


      exports.countDelivred = (req, res) => {
        Ptchargement.countDelivred((err, data) => {
          if (err) {
            return res.status(500).send({
              message: err.message || "Une erreur s'est produite lors de la récupération des données."
            });
          }
          res.json({ numberOfValidPoints: data });
        });
      };
      

      exports.findVoyageList = (req, res) => {
      
        Ptchargement.findVoyageList((err, result) => {
          if (err) {
            return res.status(500).json({ error: 'Internal Server Error' });
          }
      
          res.status(200).json(result);
        });
      };


      exports.findVoyageListExp = (req, res) => {
      
        Ptchargement.findVoyageListExp((err, result) => {
          if (err) {
            return res.status(500).json({ error: 'Internal Server Error' });
          }
      
          res.status(200).json(result);
        });
      };


      exports.findVoyageListToAdjust = (req, res) => {
      
        Ptchargement.findVoyageListToAdjust((err, result) => {
          if (err) {
            return res.status(500).json({ error: 'Internal Server Error' });
          }
      
          res.status(200).json(result);
        });
      };



      exports.findLigneInSametrip = (req, res) => {
        const { id_camion, id_conducteur, date_voyage } = req.params;
    
        Ptchargement.findLigneInSametrip(id_camion, id_conducteur, date_voyage, (err, result) => {
            if (err) {
                if (err.kind === "not_found") {
                    return res.status(404).json({ message: 'Point de chargement non trouvé' });
                } else {
                    return res.status(500).json({ message: 'Erreur lors de retrait à jour du point de chargement' });
                }
            }
    
            res.json({ message: 'Succès', result });
        });
    };
    
      
    exports.updateStatusToBeInvoiced = (req, res) => {
      const { id } = req.params;
    
      Ptchargement.updateStatusToBeInvoiced(id, (err, updatedPt) => {
        if (err) {
          if (err.kind === "not_found") {
            return res.status(404).json({ message: 'Point de chargement non trouvé' });
          } else {
            return res.status(500).json({ message: 'Erreur lors de la mise à jour du status' });
          }
        }
    
        res.json({ message: 'Status mis à jour avec succès', pt: updatedPt });
      });
    };


// Fonction pour envoyer un SMS
// exports.sendSMS = async (req, res) => {
//   const { phone, message } = req.body;

//   // Vérifier si les données phone et message sont présentes
//   if (!phone || !message) {
//     return res.status(400).json({ error: "Veuillez fournir un numéro de téléphone et un message" });
//   }

//   // Encoder le texte du message
//   const smsText = encodeURIComponent(message);
//   const phoneNumber = encodeURIComponent(phone);

//   const smsUrl = `http://41.226.169.210/API/sendsms.php?SPID=62&LOGIN=Zen%20&PASS=Zen%212019%24&TEXT=${smsText}&SC=ZN_LOGISTIC&MOBILE=216${phoneNumber}`;

//   try {
//     const response = await axios.get(smsUrl);
//     res.json(response.data);
//   } catch (error) {
//     console.error('Erreur lors de l\'envoi du SMS :', error);
//     res.status(500).json({ error: error.message });
//   }
// };










exports.findAllToInspection = (req, res) => {
  // Appeler la méthode Ptchargement.findAllToInspection pour récupérer toutes les données
  Ptchargement.findAllToInspection((err, data) => {
    if (err) {
      // En cas d'erreur, renvoyer une réponse d'erreur
      return res.status(500).send({
        message: err.message || "Une erreur s'est produite lors de la récupération des données de ptchargement."
      });
    }
    // Si les données sont récupérées avec succès, renvoyer les données au format JSON
    res.send(data);
  });
};



exports.updatePieceCegid = (req, res) => {
  const { id } = req.params;
  const {pience } = req.body;

  Ptchargement.updatePieceCegid(id, pience, (err, updatedPt) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de la mise à jour du volume' });
      }
    }

    res.json({ message: 'NUM piece mis à jour avec succès', pt: updatedPt });
  });
};

    

exports.findLigneDelivredByTruck = (req, res) => {
  const {id_camion, date_debut,date_fin } = req.body;

  Ptchargement.findLigneDelivredByTruck(id_camion, date_debut,date_fin, (err, updatedPt) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de la mise à jour du volume' });
      }
    }

    res.json({  data: updatedPt });
  });
};


exports.findLigneDelivredByDriver = (req, res) => {
  const {id_conducteur, date_debut,date_fin } = req.body;

  Ptchargement.findLigneDelivredByDriver(id_conducteur, date_debut,date_fin, (err, updatedPt) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de la mise à jour du volume' });
      }
    }

    res.json({  data: updatedPt });
  });
};

exports.findLignesByweek = (req, res) => {
  const {intervalle } = req.params;

  Ptchargement.findLignesByweek(intervalle, (err, updatedPt) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de la extraction des données' });
      }
    }

    res.json({  data: updatedPt });
  });
};


exports.findReservedByChef = (req, res) => {
  const { idChef } = req.params; 
  
  Ptchargement.findReservedByChef(idChef, (err, updatedPt) => { 
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de l\'extraction des données' });
      }
    }

    res.json({ data: updatedPt });
  });
};



exports.findExpediedByChef = (req, res) => {
  const { idChef } = req.params; 
  
  Ptchargement.findExpediedByChef(idChef, (err, updatedPt) => { 
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de l\'extraction des données' }); 
      }
    }

    res.json({ data: updatedPt });
  });
};

exports.findDeliveredByChef = (req, res) => {
  const { idChef } = req.params; 
  
  Ptchargement.findDeliveredByChef(idChef, (err, updatedPt) => { 
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de l\'extraction des données' }); // Correction: Ajoutez un backslash avant l'apostrophe pour éviter une erreur de syntaxe
      }
    }

    res.json({ data: updatedPt });
  });
};


exports.findRetard = (req, res) => {
  const dateDebut = req.body.date_debut ? new Date(req.body.date_debut) : new Date(`${new Date().getFullYear()}-01-01`);
  dateDebut.setHours(0, 0, 0, 0); 

  const dateFin = req.body.date_fin ? new Date(req.body.date_fin) : new Date(`${new Date().getFullYear()}-12-31`);
  dateFin.setHours(23, 59, 59, 999);

  Ptchargement.findRetard(dateDebut, dateFin, (err, result) => {
      if (err) {
          console.error("Error: ", err);
          return res.status(500).json({ error: 'Internal Server Error' });
      }

      res.status(200).json(result);
  });
};



exports.getProductivity = (req, res) => {
  const dateDebut = req.body.date_debut ? new Date(req.body.date_debut) : new Date(`${new Date().getFullYear()}-01-01`);
  const dateFin = req.body.date_fin ? new Date(req.body.date_fin) : new Date(`${new Date().getFullYear()}-12-31`);

  Ptchargement.getProductivity(dateDebut, dateFin, (err, result) => {
      if (err) {
          console.error("Error: ", err);
          return res.status(500).json({ error: 'Internal Server Error' });
      }

      res.status(200).json(result);
  });
};



exports.MoyVolumePerVoyage = (req, res) => {
  const dateDebut = req.body.date_debut ? new Date(req.body.date_debut) : new Date(`${new Date().getFullYear()}-01-01`);
  const dateFin = req.body.date_fin ? new Date(req.body.date_fin) : new Date(`${new Date().getFullYear()}-12-31`);

  Ptchargement.MoyVolumePerVoyage(dateDebut, dateFin, (err, result) => {
      if (err) {
          console.error("Error: ", err);
          return res.status(500).json({ error: 'Internal Server Error' });
      }

      res.status(200).json(result);
  });
};

exports.getIndicateurByConducteur = (req, res) => {
  const dateDebut = req.body.date_debut ? new Date(req.body.date_debut) : new Date(`${new Date().getFullYear()}-01-01`);
  const dateFin = req.body.date_fin ? new Date(req.body.date_fin) : new Date(`${new Date().getFullYear()}-12-31`);
  const id_conducteur = req.body.id_conducteur
  const id_destination = req.body.id_destination

  Ptchargement.getIndicateurByConducteur(dateDebut, dateFin,id_conducteur,id_destination, (err, result) => {
      if (err) {
          console.error("Error: ", err);
          return res.status(500).json({ error: 'Internal Server Error' });
      }

      res.status(200).json(result);
  });
};








exports.voyagePerDestination = (req, res) => {
  const dateDebut = req.body.date_debut ? new Date(req.body.date_debut) : new Date(`${new Date().getFullYear()}-01-01`);
  const dateFin = req.body.date_fin ? new Date(req.body.date_fin) : new Date(`${new Date().getFullYear()}-12-31`);
  const destination_id = req.body.destination_id;

  if (destination_id) {
    Ptchargement.voyagePerDestination(dateDebut, dateFin, destination_id, (err, result) => {
      if (err) {
        console.error("Error: ", err);
        return res.status(500).json({ error: 'Internal Server Error' });
      }
      res.status(200).json(result);
    });
  } else {
    Destination.getAllVoyage(dateDebut, dateFin,(err, destinations) => {
      if (err) {
        console.error("Error: ", err);
        return res.status(500).json({ error: 'Internal Server Error' });
      }
      res.status(200).json(destinations);
    });
  }
};


exports.updateComment = (req, res) => {
  const { id } = req.params;
  const {comment } = req.body;

  Ptchargement.updateComment(id, comment, (err, updatedPt) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de la mise à jour du comment' });
      }
    }

    res.json({ message: 'NUM piece mis à jour avec succès', pt: updatedPt });
  });
};

exports.updateStatusNoPickup = (req, res) => {
  const { id } = req.params;

  Ptchargement.updateStatusNoPickup(id, (err, updatedPt) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de la mise à jour du updateStatusNoPickup' });
      }
    }

    res.json({ message: 'Status mis à jour avec succès', pt: updatedPt });
  });
};


exports.findAllValidWithLocalisation = (req, res) => {
  Ptchargement.findAllValidWithLocalisation((err, data) => {
  if (err)
    res.status(500).send({
      message:
        err.message || "Some error occurred while retrieving ptchargement."
    });
  else res.send(data);
});

};  


exports.getLigneByDestinationAndUser = (req, res) => {
  const { id } = req.params; 

  Ptchargement.getLigneByDestinationAndUser(id, (err, data) => { 
    if (err) {
      res.status(500).send({
        message: err.message || "Une erreur s'est produite lors de la récupération des données de chargement."
      });
    } else {
      res.send(data);
    }
  });
};

exports.getLigneWithPackageById = (req, res) => {
  const { id } = req.params; 

  Ptchargement.getLigneWithPackageById(id, (err, data) => { 
    if (err) {
      res.status(500).send({
        message: err.message || "Une erreur s'est produite lors de la récupération des données de chargement."
      });
    } else {
      res.send(data);
    }
  });
};


 

exports.getLigneByDestinationAndUserLiv = (req, res) => {
  const { id } = req.params; 

  Ptchargement.getLigneByDestinationAndUserLiv(id, (err, data) => { 
    if (err) {
      res.status(500).send({
        message: err.message || "Une erreur s'est produite lors de la récupération des données de chargement."
      });
    } else {
      res.send(data);
    }
  });
};



exports.findAllRetourAndInter = (req, res) => {
  Ptchargement.findAllRetourAndInter( (err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Une erreur s'est produite lors de la récupération des données de chargement."
      });
    } else {
      res.send(data);
    }
  });
};


exports.updateVolumeXls = (req, res) => {
  const data = req.body; 
  Ptchargement.updateVolumeXls(data, (err, result) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Une erreur s'est produite lors de la mise à jour du volume."
      });
    } else {
      res.status(200).json({
        message: "Mise à jour du volume, PU et PrixTot réussie"
      });
    }
  });
};

exports.fixPrice = (req, res) => {
  const data = req.body; 
  Ptchargement.fixPrice(data, (err, result) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Une erreur s'est produite lors de la mise à jour du volume."
      });
    } else {
      res.status(200).json({
        message: "Mise à jour du volume, PU et PrixTot réussie"
      });
    }
  });
};

exports.pdfNotUpdated = (req, res) => {

  Ptchargement.pdfNotUpdated((err, result) => {
    if (err) {
      return res.status(500).json({ error: 'Internal Server Error' });
    }

    res.status(200).json(result);
  });
};



exports.findByDestinationsAndDateVoyage = (req, res) => {
  const data = req.body; 
  if (!data.date || !data.depotEmetteur || !data.depotRecepteur) {
    return res.status(400).send({ message: "Données manquantes" });
  }
  Ptchargement.findByDestinationsAndDateVoyage(data, (err, result) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Une erreur d\' Extraction des donné."
      });
    } else {
      res.status(200).json({
        message: "Extraction des donné",
        data: result // Inclure les données retournées
      });
    }
  });
};
exports.updateVolumeColisage = (req, res) => {
  const data = req.body; // Récupérer les données envoyées dans le corps de la requête
  //console.log(data);
  Ptchargement.updateVolumeColisage(data, (err, result) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Une erreur s'est produite lors de la mise à jour du volume."
      });
    } else {
      res.status(200).json({
        message: "Mise à jour du volume, PU et PrixTot réussie"
      });
    }
  });
};


exports.findLignesByIdVoyage = (req, res) => {
  const { idVoyage } = req.params; 
  
  Ptchargement.findLignesByIdVoyage(idVoyage, (err, updatedPt) => { 
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: 'Point de chargement non trouvé' });
      } else {
        return res.status(500).json({ message: 'Erreur lors de l\'extraction des données' });
      }
    }

    res.json({ data: updatedPt });
  });
};


exports.sendSMS = async (req, res) => {
  const { phone, message } = req.body || req;

  if (!phone || !message) {
    return res.status(400).json({ error: "Veuillez fournir un numéro de téléphone et un message" });
  }

  const smsText = encodeURIComponent(message);
  const phoneNumber = encodeURIComponent(phone);

  const smsUrl = `https://41.226.169.210/API/sendsms.php?SPID=62&LOGIN=Zen%20&PASS=Zen%212019%24&TEXT=${smsText}&SC=ZN_LOGISTIC&MOBILE=216${phoneNumber}`;
  const options = {
    method: 'GET',
    rejectUnauthorized: false
  };

  https.get(smsUrl, options, (response) => {
    let responseData = '';

    response.on('data', (chunk) => {
      responseData += chunk;
    });

    response.on('end', () => {
      try {
        // Supposons que la réponse soit au format JSON
        const responseJson = JSON.parse(responseData);
        
        // Vérification si la réponse contient les champs attendus
        if (responseJson.ref && responseJson.code) {
          const smsData = {
            ref: responseJson.ref,
            content: message,
            mobile: phone,
            status: responseJson.code === 200 ? "Sent" : "error",
            code: responseJson.code
          };

          // Insertion dans la base de données
          SmsModel.create(smsData, (err, data) => {
            if (err) {
              console.error("Erreur lors de l'insertion dans la base de données : ", err);
              return res.status(500).json({ error: "Erreur lors de l'enregistrement du SMS dans la base de données" });
            }
            console.log("SMS enregistré avec succès", data);
          });
          
          // Retourner la réponse du service SMS
          res.json(responseJson);
        } else {
          console.error("Réponse du service SMS invalide : ", responseJson);
          res.status(500).json({ error: "Réponse du service SMS invalide" });
        }
      } catch (error) {
        console.error("Erreur lors du traitement de la réponse du service SMS : ", error);
        res.status(500).json({ error: "Erreur interne du serveur" });
      }
    });
  }).on('error', (error) => {
    console.error('Erreur lors de l\'envoi du SMS :', error);
    res.status(500).json({ error: error.message });
  });
};



exports.updateVoyageToAdjust = (req, res) => {
  const id_voyage = req.query.id_voyage;

  if (!id_voyage) {
    return res.status(400).json({ error: 'id_voyage manquant dans la requête' });
  }

  Ptchargement.updateVoyageToAdjust(id_voyage, (err, result) => {
    if (err) {
      // Si err.code et err.message existent, on les utilise
      return res.status(err.code || 500).json({ error: err.message || 'Erreur serveur interne' });
    }

    // Succès
    res.status(200).json({ message: result.message, affectedRows: result.affectedRows });
  });
};





// exports.getdecharge = (req, res) => {
//   ////console.log('rrrr')
//   Ptchargement.getdecharge(req.params.idCommande ,  (err, data) => {
//     //console.log(req.params)
//   //console.log(data)
//   if (err) {
//     if (err.kind === "not_found") {
//       res.status(404).send({
//         message: `Not found point_char_decharr with idCommande ${req.params.idCommande} `
//       });
//     } else {
//       res.status(500).send({
//         message: "Error retrieving point_char_decharr with idCommande " + req.params.idCommande 
//       });
//     }
//   } else res.send(data);
// });
// };
