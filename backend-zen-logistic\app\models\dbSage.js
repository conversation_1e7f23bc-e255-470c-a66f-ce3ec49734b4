const mssql = require("mssql");
const dbConfig = require("../config/db.sage.js");

const config = {
  user: dbConfig.USER,
  password: dbConfig.PASSWORD,
  server: dbConfig.HOST, // Utiliser HOST tel quel, sans modification
  database: dbConfig.DB,
  port: dbConfig.PORT || 1433, // Port par défaut de SQL Server
  options: {
    encrypt: false, // Désactiver le chiffrement si non requis
    enableArithAbort: true, // Pour éviter certaines erreurs
    trustServerCertificate: true, // Ignorer les problèmes de certificat TLS
  },
};

// Création de la connexion
const poolPromise = new mssql.ConnectionPool(config)
  .connect()
  .then(pool => {
    console.log('Connected to SQL SAGE');
    return pool;
  })
  .catch(err => {
    console.error('SQL Server SAGE connection failed', err);
    throw err;
  });

module.exports = {
  mssql,
  poolPromise
};
