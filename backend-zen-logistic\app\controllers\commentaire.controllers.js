const CommentaireModel = require("../models/commentaire.model.js");



exports.create = (req, res) => {
    if (!req.body) {
      res.status(400).send({
        message: "Content can not be empty"
      });
      return;
    }
  
    const commentaire = new CommentaireModel({
        id_ligne: req.body.id_ligne,
        value : req.body.value,
        nom_author : req.body.nom_author,
        id_author : req.body.id_author


    });
  
    CommentaireModel.create(commentaire, (err, data) => {
      if (err) {
        res.status(500).send({
          message: err.message || "Some error occurred while creating the circuit."
        });
      } else {
        res.send(data);
      }
    });
  };


  exports.findCommentsByLigne = (req, res) => {
    const { id } = req.params;

    // Valider la requête
    if (!id) {
      return res.status(400).json({ message: "Tous les champs sont obligatoires." });
    }

    // Appeler la méthode du modèle pour rechercher la destination par ID
    CommentaireModel.findCommentsByLigne(id, (err, commentaire) => {
      if (err) {
        if (err.kind === "not_found") {
          return res.status(404).json({ message: `Destination non trouvée avec l'ID ${id}.` });
        } else {
          return res.status(500).json({ message: `Erreur lors de la recherche de la destination avec l'ID ${id}.` });
        }
      }

      // Envoyer uniquement le tableau de commentaires sans l'envelopper dans un objet JSON
      res.json(commentaire); // Notez que 'commentaire' est le tableau de commentaires
    });
};




exports.findAllComments = (req, res) => {
  const page = req.query.page ? parseInt(req.query.page) : 1; // Default page is 1
  const limit = req.query.limit ? parseInt(req.query.limit) : 10; // Default limit is 10
  
  // Call the model method to search for comments with pagination
  CommentaireModel.findAllComments(page, limit, (err, result) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).json({ message: `.` });
      } else {
        return res.status(500).json({ message: `` });
      }
    }

    // Send the array of comments and pagination information
    res.json({
      page,
      limit,
      totalItems: result.totalItems,
      commentaire: result.commentaire
    });
  });
};




