const CircuitModel = require("../models/circuit.model.js");

exports.create = (req, res) => {
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty"
    });
    return;
  }

  const circuit = new CircuitModel({
    nom_circuit: req.body.nom_circuit,
    price : req.body.price,
    margin : req.body.margin,
    price_without_margin : req.body.price_without_margin

  });


  CircuitModel.create(circuit, (err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Some error occurred while creating the circuit."
      });
    } else {
      res.send(data);
    }
  });
};

exports.findAll = (req, res) => {
    CircuitModel.findAll((err, data) => {
    if (err) {
      res.status(500).send({
        message: err.message || "Some error occurred while retrieving circuit."
      });
    } else {
      res.send(data);
    }
  });
};

exports.deleteCircuit = (req, res) => {
  const circuitId = req.params.id; // Make sure this line is correct and provides a value

  CircuitModel.delete(circuitId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `circuit with id ${circuitId} not found.`
        });
      } else {
        res.status(500).send({
          message: err.message || `Some error occurred while deleting the circuit with id ${circuitId}.`
        });
      }
    } else {
      res.send({
        message: 'circuit deleted successfully.'
      });
    }
  });
};


exports.adjustVoyage = (req, res) => {
  const { id_circuit, lignes } = req.body;
  CircuitModel.adjustVoyage(id_circuit, lignes, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Circuit not found with id ${id_circuit}.`
        });
      } else {
        res.status(500).send({
          message: err.message || "Some error occurred while retrieving circuit."
        });
      }
    } else {
      res.send(data);
    }
  });
};


exports.updateCircuitById = (req, res) => {
  const id = req.params.id;
  const updatedFields = req.body;

  if (!updatedFields || Object.keys(updatedFields).length === 0) {
    return res.status(400).send({
      message: "Le contenu ne peut pas être vide",
    });
  }

  CircuitModel.updateCircuitById(id, updatedFields, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        return res.status(404).send({
          message: `Pas d'enregistrement trouvé avec l'ID ${id}.`,
        });
      }
      if (err.kind === "invalid_input") {
        return res.status(400).send({
          message: "Données invalides ou incomplètes pour la mise à jour.",
        });
      }
      return res.status(500).send({
        message:
          err.message || "Une erreur est survenue lors de la mise à jour de l'enregistrement.",
      });
    }
    res.send({
      message: "Mise à jour réussie.",
      data,
    });
  });
};
