const cegid = require("./dbCegid.js");
const db = require("./db.js");  // Connexion à ta base de données locale
const mssql = require("mssql");
const bwipjs = require('bwip-js');

const package = {};



const getUserDestinationCodes = async (userId) => {
  const query = `
    SELECT d.code_erp
    FROM user_destination ud
    JOIN destination d ON ud.id_destination = d.id
    WHERE ud.id_user = ?
  `;

  try {
    const codes = await new Promise((resolve, reject) => {
      db.query(query, [userId], (err, res) => {
        if (err) return reject(err);
        resolve(res.map(row => row.code_erp));
      });
    });

    return codes;
  } catch (error) {
    console.error("Erreur lors de la récupération des codes ERP :", error);
    throw error;
  }
};


package.findAllPackage = () => {
    return new Promise((resolve, reject) => {
      cegid.then(pool => {
        pool.request()
          .query(`SELECT DISTINCT MCE_CODEBARRE, MCE_DATECREATION, GP_DEPOT, GP_DEPOTDEST, GP_DEPOT_LIB, GP_DEPOTDEST_LIB 
                  FROM ycolis`, (err, res) => {
            if (err) {
              console.error("Erreur lors de la requête : ", err);
              reject(err); // Rejeter l'erreur
              return;
            }
            console.log(res);
            resolve(res.recordset); // Résoudre la promesse avec les données de la requête
          });
      }).catch(err => {
        reject(err); // Gérer l'erreur de la connexion à la base de données
      });
    });
  };


  package.getStatusByPackage = (id, result) => {
    const query = `SELECT * FROM status_package WHERE id_package = ? ORDER BY created_date DESC`;
  
    db.query(query, [id], (err, res) => {
      if (err) {
        console.log("Erreur dans la récupération des statuts : ", err);
        result(err, null);
      } else {
        if (res.length) {
          result(null, res); // Si des résultats sont trouvés
        } else {
          result({ kind: "not_found" }, null); // Si aucun résultat n'est trouvé
        }
      }
    });
  };
  
  // Fonction dans le contrôleur (controller)
  exports.findAllPackage = (req, res) => {
    // Appel de la fonction findAllPackage et gestion des réponses
    Package.findAllPackage()
      .then(data => {
        res.send(data); // Répondre avec les données de la requête
      })
      .catch(err => {
        // Gérer les erreurs
        res.status(500).send({
          message: err.message || "Certaines erreurs sont survenues lors de la récupération des packages."
        });
      });
  };
  

package.insertPackage = async (packages) => {
    try {
      console.log("Données reçues pour insertion :", packages); // Vérifie le contenu
  
      if (!Array.isArray(packages)) {
        throw new Error('packages doit être un tableau');
      }
  
      if (packages.length === 0) {
        console.log("Aucun package à insérer.");
        return { message: 'Aucun package à insérer', insertedBarcodes: [] };
      }
  
      const query = `INSERT INTO package (barcode, departure, code_departure, arrival, code_arrival, created_at)
                     VALUES (?, ?, ?, ?, ?, ?)`;
  
      const insertedBarcodes = [];
  
      for (const packageData of packages) {
        try {
          if (!packageData.MCE_CODEBARRE) {
            console.warn("Données invalides détectées, ignorées :", packageData);
            continue;
          }
  
          const values = [
            packageData.MCE_CODEBARRE,  // barcode
            packageData.GP_DEPOT_LIB,   // departure
            packageData.GP_DEPOT,       // code_departure
            packageData.GP_DEPOTDEST_LIB, // arrival
            packageData.GP_DEPOTDEST,   // code_arrival
            packageData.MCE_DATECREATION // created_at
          ];
  
          // Exécuter la requête et capturer la réponse complète
          const result = await db.query(query, values);
          console.log('Résultat complet de db.query :', result);
  
          // Si la bibliothèque retourne un objet avec une propriété rows
          const rows = result.rows || result.recordset || [];
          console.log('Résultat de la requête INSERT :', rows);
  
          insertedBarcodes.push(packageData.MCE_CODEBARRE);
  
        } catch (err) {
          console.error(`Erreur d'insertion pour ${packageData.MCE_CODEBARRE}:`, err.message);
          continue; // Continue avec le prochain élément en cas d'erreur
        }
      }
  
      if (insertedBarcodes.length > 0) {
        await package.deleteInsertedPackagesFromCegid(insertedBarcodes);
      }
  
      console.log('Toutes les insertions valides ont été effectuées avec succès.');
      return { message: 'Insertion réussie et suppression de cegid effectuée', insertedBarcodes };
  
    } catch (error) {
      console.error('Erreur lors de l\'insertion des données:', error);
      return { message: 'Erreur lors de l\'insertion des données', error: error.message };
    }
  };
  
  
package.deleteInsertedPackagesFromCegid = async (barcodes) => {
  if (barcodes.length === 0) return;

  try {
    const pool = await cegid;
    const request = pool.request();

    // Créer une clause WHERE avec tous les codes-barres
    const barcodeList = barcodes.map(barcode => `'${barcode}'`).join(',');
    const query = `DELETE FROM ycolis WHERE MCE_CODEBARRE IN (${barcodeList})`;

    await request.query(query);
    console.log(`Suppression réussie de ${barcodes.length} éléments de ycolis.`);
  } catch (err) {
    console.error('Erreur lors de la suppression des packages de ycolis:', err);
  }
};

const checkLastReceptor = async (packageIds, userId) => {
  console.log("---------------------", packageIds, userId);

  // Vérifier si packageIds est vide
  if (!Array.isArray(packageIds) || packageIds.length === 0) {
    console.error("Aucun package ID fourni.");
    throw new Error("Aucun package ID fourni.");
  }

  // Dynamically generate placeholders for the IN clause
  const placeholders = packageIds.map(() => '?').join(',');
  const checkQuery = `
    SELECT id_user
    FROM status_package
    WHERE id_package IN (${placeholders})
    ORDER BY created_date DESC
    LIMIT 1
  `;

  try {
    // Execute the query with the packageIds array
    const results = await db.query(checkQuery, packageIds);

    // Log the raw result to understand its structure
    console.log("Résultat brut de la requête :", results);

    // Extract rows from the result
    const rows = results; // The rows are directly available in the result

    // Vérification de la réponse
    if (rows.length > 0 && rows[0].id_user === userId) {
      console.log("Le dernier récepteur est le même.");
      return true; // No action needed
    }

    console.log("Le dernier récepteur est différent.");
    return false; // Proceed with the update
  } catch (error) {
    console.error("Erreur lors de la vérification du dernier récepteur :", error);
    throw error;
  }
};















































// Extraction des barcodes depuis la liste des colis
const extractBarcodes = (packages) => {
  if (!Array.isArray(packages) || packages.length === 0) {
    throw new Error('La liste des packages doit être un tableau non vide.');
  }

  return packages.map(pkg => {
    if (!pkg.barcode) throw new Error('Un package ne contient pas de barcode valide.');
    return pkg.barcode;
  });
};

const checkPackagesExist = (barcodes) => {
  return new Promise((resolve, reject) => {
    if (!Array.isArray(barcodes) || barcodes.length === 0) {
      return reject(new Error('Les barcodes fournis sont invalides.'));
    }

    const placeholders = barcodes.map(() => '?').join(',');
    const query = `
      SELECT id, barcode, code_arrival, arrival
      FROM package 
      WHERE barcode IN (${placeholders}) AND status != 'livré'
    `;

    db.query(query, barcodes, (err, res) => {
      if (err) return reject(err);

      const found = Array.isArray(res) ? res : [];
      const foundBarcodes = found.map(p => p.barcode);

      // Marquer les barcodes non trouvés
      const allPackages = barcodes.map(barcode => {
        const match = found.find(p => p.barcode === barcode);
        if (match) {
          return match;
        } else {
          return {
            barcode,
            message: 'Code-barres inexistant ou déjà livré',
            status: 'non existant'
          };
        }
      });

      resolve(allPackages);
    });
  });
};


// Vérifie si un colis est déjà réceptionné
const isPackageUpToDate = async (packageId, userId) => {
  const query = `SELECT * FROM package WHERE id = ?`;

  try {
    const [packageData] = await new Promise((resolve, reject) => {
      db.query(query, [packageId], (err, res) => err ? reject(err) : resolve(res));
    });

    if (!packageData) return false;

    const userCodes = await getUserDestinationCodes(userId);
    const isDelivered = userCodes.includes(packageData.code_arrival);

    const status = isDelivered ? "livré" : "partiellement livré";

    return {
      id: packageData.id,
      message: packageData.id_user === userId ? "Déjà à jour." : "Nécessite une mise à jour.",
      userName: packageData.name_receptor,
      barcode: packageData.barcode,
      status,
      code_arrival: status === "partiellement livré" ? userCodes[0] : packageData.code_arrival
    };
  } catch (err) {
    console.error("Erreur isPackageUpToDate:", err);
    throw err;
  }
};


// Mise à jour de l'état des colis
const updatePackages = async (userName, userId, code, status, packageIds) => {
  const query = `
    UPDATE package 
    SET last_reception = NOW(), name_receptor = ?, id_user = ?, code_receiver = ?, status = ?, id_driver = NULL
    WHERE id IN (?)
  `;
  return db.query(query, [userName, userId, code, status, packageIds]);
};

// Insertion d’un log de statut pour les colis réceptionnés
const insertStatusPackage = async (userName, status, packageIds) => {
  if (!Array.isArray(packageIds) || packageIds.length === 0) return;

  const values = packageIds.map(id => [userName, status, new Date(), id]);
  const query = `
    INSERT INTO status_package (receptor_name, status, created_date, id_package)
    VALUES ?
  `;
  return db.query(query, [values]);
};

// Fonction principale appelée à la réception des colis
package.receptionPackage = async (packages, userId, userName, code) => {
  try {
    const barcodes = extractBarcodes(packages);
    const packageRecords = await checkPackagesExist(barcodes); // retourne tous les codes, même non trouvés

    const response = [];
    const packagesToUpdate = {}; // Regroupés par destination
    const alreadyUpToDate = [];

    for (const pkg of packageRecords) {
      // Cas : barcode inexistant ou déjà livré
      if (!pkg.id) {
        response.push({
          barcode: pkg.barcode,
          message: pkg.message || 'Code-barres inexistant ou déjà livré',
          status: pkg.status || 'non existant',
          userName
        });
        continue;
      }

      const status = await isPackageUpToDate(pkg.id, userId);
      if (!status) continue;

      response.push({
        id: pkg.id,
        barcode: pkg.barcode,
        message: status.message,
        userName,
        status: status.status,
        code_arrival: status.code_arrival
      });

      if (status.message === 'Déjà à jour.') {
        alreadyUpToDate.push(pkg.id);
      } else {
        if (!packagesToUpdate[status.code_arrival]) {
          packagesToUpdate[status.code_arrival] = [];
        }
        packagesToUpdate[status.code_arrival].push(pkg.id);
      }
    }

    // Mise à jour et log des statuts
    for (const [destinationCode, packageIds] of Object.entries(packagesToUpdate)) {
      const statusSample = response.find(p => packageIds.includes(p.id));
      const status = (statusSample && statusSample.status) ? statusSample.status : 'partiellement livré';

      await updatePackages(userName, userId, destinationCode, status, packageIds);
      await insertStatusPackage(userName, status, packageIds);
    }

    return { message: 'Opération terminée.', response };
  } catch (err) {
    console.error("Erreur dans receptionPackage:", err);
    return { message: 'Erreur lors de la réception des packages', error: err.message };
  }
};









package.receptionPackageMagasin = async (packages, userId, userName) => {
  try {
    const userCodes = await getUserDestinationCodes(userId);
    const barcodes = extractBarcodes(packages);
    const packageRecords = await checkPackagesExist(barcodes);

    const response = [];
    const packagesToUpdate = {};
    const alreadyUpToDate = [];

    for (const pkg of packageRecords) {
      // Si le package n’a pas d’ID, il est inexistant
      if (!pkg.id) {
        response.push({
          barcode: pkg.barcode,
          message: pkg.message,
          status: pkg.status,
          userName
        });
        continue;
      }

      if (!userCodes.includes(pkg.code_arrival)) {
        response.push({
          id: pkg.id,
          barcode: pkg.barcode,
          message: `Colis destiné à ${pkg.arrival}`,
          userName,
          status: "non autorisé",
          code_arrival: pkg.code_arrival
        });
        continue;
      }

      const status = await isPackageUpToDate(pkg.id, userId);
      if (!status) continue;

      response.push({
        id: pkg.id,
        barcode: pkg.barcode,
        message: status.message,
        userName,
        status: status.status,
        code_arrival: status.code_arrival
      });

      if (status.message === 'Déjà à jour.') {
        alreadyUpToDate.push(pkg.id);
      } else {
        if (!packagesToUpdate[status.code_arrival]) {
          packagesToUpdate[status.code_arrival] = [];
        }
        packagesToUpdate[status.code_arrival].push(pkg.id);
      }
    }

    // Met à jour uniquement les colis valides
    for (const [destinationCode, packageIds] of Object.entries(packagesToUpdate)) {
      const statusSample = response.find(p => packageIds.includes(p.id));
      const status = (statusSample && statusSample.status) ? statusSample.status : 'partiellement livré';

      await updatePackages(userName, userId, destinationCode, status, packageIds);
      await insertStatusPackage(userName, status, packageIds);
    }

    return { message: 'Opération terminée.', response };
  } catch (err) {
    console.error("Erreur dans receptionPackageMagasin:", err);
    return { message: 'Erreur lors de la réception des packages', error: err.message };
  }
};











































package.findPackages = (barcode, startDate, endDate, status, callback) => {
  let query = "SELECT * FROM package WHERE 1=1";
  const params = [];

  // Nettoyage et validation des entrées
  if (barcode) {
      if (!/^\d+$/.test(barcode)) {
          return callback(new Error("Barcode invalide"), null);
      }
      query = "SELECT * FROM package WHERE barcode = ?";
      params.push(barcode);
  } else {
    if (startDate && endDate) {
      if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate) || !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
        return callback(new Error("Format de date invalide (YYYY-MM-DD)"), null);
      }
    
      const startDateTime = `${startDate} 00:00:00`;
      const endDateTime = `${endDate} 23:59:59`;
    
      query += " AND created_at BETWEEN ? AND ?";
      params.push(startDateTime, endDateTime);
    }
    
      if (status) {
          const allowedStatuses = ["en attente", "en transit", "livré", "partiellement livré"];
          if (!allowedStatuses.includes(status)) {
              return callback(new Error("Statut invalide"), null);
          }
          query += " AND status = ?";
          params.push(status);
      }
  }
  console.log(params)
  console.log(query)

  // Exécution de la requête SQL sécurisée
  db.query(query, params, (err, results) => {
      if (err) {
          callback(err, null);
      } else {
          callback(null, results);
      }
  });
};



package.getBarcodes = async (ids) => {
  try {
    console.log(ids)
    const barcodes = await Promise.all(
      ids.map(async (id) => {
        try {
          const png = await bwipjs.toBuffer({
            bcid: 'code128',   
            text: id.toString(), 
            scale: 3, 
            height: 10, 
            includetext: true, 
            textxalign: 'center', 
          });
          const base64 = png.toString('base64');
          return { id, base64: base64 }; 
        } catch (err) {
          return { id, error: err.message };
        }
      })
    );

    return barcodes; 
  } catch (err) {
    throw new Error('Erreur lors de la génération des codes-barres : ' + err.message);
  }
};


const insertBarcodes = async (barcodes) => {
  try {
    // Séparer ceux avec et sans code
    const withCodes = barcodes.filter(b => b.code_departure && b.code_arrival);
    const withoutCodes = barcodes.filter(b => !b.code_departure || !b.code_arrival);

    let results = [];

    // Insertion avec codes
    if (withCodes.length > 0) {
      const insertWithCodesQuery = `
        INSERT INTO package 
          (barcode, departure, arrival, fk_id_ligne, code_departure, code_arrival, created_at)
        VALUES ?
      `;

      const valuesWithCodes = withCodes.map(b => [
        b.barcode,
        b.departure,
        b.arrival,
        b.id_ligne,
        b.code_departure,
        b.code_arrival,
        new Date()
      ]);

      const resWithCodes = await new Promise((resolve, reject) => {
        db.query(insertWithCodesQuery, [valuesWithCodes], (err, res) => {
          if (err) reject(err);
          else resolve(res);
        });
      });

      results.push(resWithCodes);
    }

    // Insertion sans codes
    if (withoutCodes.length > 0) {
      const insertWithoutCodesQuery = `
        INSERT INTO package 
          (barcode, departure, arrival, fk_id_ligne, created_at)
        VALUES ?
      `;

      const valuesWithoutCodes = withoutCodes.map(b => [
        b.barcode,
        b.departure,
        b.arrival,
        b.id_ligne,
        new Date()
      ]);

      const resWithoutCodes = await new Promise((resolve, reject) => {
        db.query(insertWithoutCodesQuery, [valuesWithoutCodes], (err, res) => {
          if (err) reject(err);
          else resolve(res);
        });
      });

      results.push(resWithoutCodes);
    }

    return results;
  } catch (err) {
    console.error('Erreur lors de l\'insertion des codes-barres:', err.message);
    throw new Error('Erreur lors de l\'insertion des codes-barres : ' + err.message);
  }
};





const getPointChargementById = async (id) => {
  try {
    const rows = await new Promise((resolve, reject) => {
      const query = `
        SELECT 
          pc.*, 
          d_from.code_erp AS from_code_erp, 
          d_to.code_erp AS to_code_erp
        FROM 
          point_chargement pc
        LEFT JOIN 
          destination d_from ON pc.from_destination = d_from.id
        LEFT JOIN 
          destination d_to ON pc.to_destination = d_to.id
        WHERE 
          pc.id = ?
      `;

      db.query(query, [id], (err, res) => {
        if (err) {
          console.error("Erreur SQL:", err);
          reject(err);
        } else {
          resolve(res);
        }
      });
    });

    if (rows.length === 0) {
      return null;  // Aucun point de chargement trouvé
    }

    return rows[0];  // Retourne le premier enregistrement trouvé

  } catch (err) {
    throw new Error('Erreur lors de la récupération du point de chargement : ' + err.message);
  }
};



const getLastPackageNumberStartingWith9 = async () => {
  try {
    // Requête SQL pour récupérer le dernier package qui commence par "9"
    const rows = await new Promise((resolve, reject) => {
      db.query("SELECT MAX(barcode) AS lastPackageId FROM package WHERE barcode LIKE '9%'",
        (err, res) => {
          if (err) {
            console.error("Erreur SQL:", err);
            reject(err);
          } else {
            resolve(res);
          }
        });
    });

    // Afficher le résultat pour débogage
    console.log('Résultat de la requête:', rows);

    // Vérifier si result est un tableau et contient un objet avec lastPackageId
    if (!Array.isArray(rows) || rows.length === 0 || !rows[0].lastPackageId) {
      console.log('Aucun package trouvé commençant par "9".');
      return null; // Aucun package trouvé, retourne null
    }

    // Retourner le dernier barcode trouvé
    return rows[0].lastPackageId;
  } catch (err) {
    // Gestion des erreurs lors de l'exécution de la requête
    console.error('Erreur lors de la récupération du dernier package commençant par 9 :', err.message);
    throw new Error('Erreur lors de la récupération du dernier package commençant par 9 : ' + err.message);
  }
};



package.generateCode = async (id,count) => {
  try {
    const point = await getPointChargementById(id);
    if (!point) {
      throw new Error('Point de chargement introuvable');
    }
    const lastPackageNumber = await getLastPackageNumberStartingWith9();
    const nextNumber = lastPackageNumber ? parseInt(lastPackageNumber.slice(1)) + 1 : 1;
    const barcodes = [];
    for (let i = 0; i < count; i++) {
      const packageNumber = nextNumber + i;
    
      const packageNumberStr = `${packageNumber}`.padStart(12, '0');
    
      const barcode = `9${packageNumberStr}`;
    
      const EAN = {
        barcode: barcode,
        departure: point.nom_depart,
        arrival: point.nom_arrivee,
        id_ligne: point.id,
        code_departure : point.from_code_erp,
        code_arrival : point.to_code_erp,

      };
    
      // Ajouter le code-barres généré à la liste
      barcodes.push(EAN);
    }
    

    // 5. Insérer les codes-barres dans la table `barcode`
    await insertBarcodes(barcodes);
    
    // Retourner les codes-barres générés
    return barcodes;
  } catch (err) {
    throw new Error('Erreur lors de la génération des codes-barres : ' + err.message);
  }
};


package.getHistoricalStatusStats = (result) => {
  const query = `
    SELECT 
      DATE(created_at) AS jour,
      status,
      COUNT(*) AS total
    FROM package
    WHERE created_at >= CURDATE() - INTERVAL 1 MONTH
    GROUP BY DATE(created_at), status
    ORDER BY jour DESC, status
  `;

  db.query(query, (err, rows) => {
    if (err) {
      console.log("Erreur dans la récupération des statuts : ", err);
      result(err, null);
    } else {
      // Transformation des résultats vers le format souhaité
      const statsMap = {};

      rows.forEach(row => {
        const { jour, status, total } = row;
        if (!statsMap[jour]) {
          statsMap[jour] = {
            jour,
            statuts: {}
          };
        }
        statsMap[jour].statuts[status] = total;
      });

      // Conversion en tableau
      const statsParJour = Object.values(statsMap);

      result(null, statsParJour);
    }
  });
};

package.getPackageByStatusAndDate = (date, status, result) => {
  console.log("Date reçue :", date, "| Statut :", status);

  // Conversion "14/04/2025" → "2025-04-14"
  const [day, month, year] = date.split('/');
  const formattedDate = `${year}-${month}-${day}`;

  const startDate = `${formattedDate} 00:00:00`;
  const endDate = `${formattedDate} 23:59:59`;

  const query = `
    SELECT * FROM package
    WHERE created_at BETWEEN ? AND ?
    AND status = ?
  `;

  db.query(query, [startDate, endDate, status], (err, res) => {
    if (err) {
      console.log("Erreur dans la récupération des colis : ", err);
      result(err, null);
      return;
    }
    result(null, res);
  });
};




package.getStatisticPackage = (result) => {
  const query = `
    SELECT 
      p.status,
      COUNT(*) AS total,
      ROUND(AVG(
        CASE 
          WHEN p.status = 'en attente' THEN TIMESTAMPDIFF(MINUTE, p.created_at, NOW())
          WHEN p.status = 'en transit' THEN TIMESTAMPDIFF(MINUTE, p.last_reception, NOW())
          WHEN p.status IN ('livré', 'partiellement livré') THEN
            TIMESTAMPDIFF(MINUTE,
              (SELECT MIN(sp_transit.created_date) 
               FROM status_package sp_transit
               WHERE sp_transit.id_package = p.id 
               AND sp_transit.status = 'en transit'),
              NOW()
            )
        END
      )) AS moyenne_minutes,
      MIN(p.created_at) AS date_min_status,
      MAX(p.created_at) AS date_max_status
    FROM package p
    LEFT JOIN status_package sp 
      ON sp.id_package = p.id AND sp.status = p.status
    GROUP BY p.status;
  `;

  db.query(query, (err, rows) => {
    if (err) {
      console.error("Erreur dans la récupération des statuts :", err);
      return result(err, null);
    }

    try {
      const resultWithConvertedTime = rows.map(row => {
        if (row.moyenne_minutes === null) {
          return { 
            status: row.status,
            total: row.total,
            moyenne_minutes: null,
            moyenne_formatee: '—',
            date_min_status: row.date_min_status,
            date_max_status: row.date_max_status
          };
        }

        const minutes = Math.round(row.moyenne_minutes);
        const jours = Math.floor(minutes / 1440);
        const heures = Math.floor((minutes % 1440) / 60);
        const mins = minutes % 60;

        return {
          status: row.status,
          total: row.total,
          moyenne_minutes: minutes,
          moyenne_formatee: `${jours}j ${heures}h ${mins}m`,
          date_min_status: row.date_min_status,
          date_max_status: row.date_max_status
        };
      });

      result(null, resultWithConvertedTime);
    } catch (error) {
      console.error("Erreur lors du formatage des résultats :", error);
      result(error, null);
    }
  });
};



package.findPackageDepartByUser = (userId, result) => {
  const query = `
    SELECT p.* 
    FROM user_destination ud
    JOIN destination d ON ud.id_destination = d.id
    JOIN package p ON p.code_departure = d.code_erp
    WHERE ud.id_user = ?
      AND p.status IN ('en attente', 'en transit', 'partiellement livré')
AND p.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 20 DAY) AND NOW()
  `;

  db.query(query, [userId], (err, res) => {
    if (err) {
      console.log("Erreur lors de la récupération des colis : ", err);
      result(err, null);
    } else {
      if (res.length) {
        result(null, res);
      } else {
        result({ kind: "not_found" }, null);
      }
    }
  });
};



package.findPackageArrivalByUser = (userId, result) => {
  const query = `
    SELECT p.* 
    FROM user_destination ud
    JOIN destination d ON ud.id_destination = d.id
    JOIN package p ON p.code_arrival = d.code_erp
    WHERE ud.id_user = ?
      AND p.status IN ('en attente', 'en transit', 'partiellement livré')
      AND p.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 20 DAY) AND NOW()

  `;

  db.query(query, [userId], (err, res) => {
    if (err) {
      console.log("Erreur lors de la récupération des colis : ", err);
      result(err, null);
    } else {
      if (res.length) {
        result(null, res);
      } else {
        result({ kind: "not_found" }, null);
      }
    }
  });
};



module.exports = package;
