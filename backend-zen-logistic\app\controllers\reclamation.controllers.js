const Reclamation = require ("../models/reclamation.model.js") ; 


// create  region  
exports.create = (req, res)=>
{ 
  console.log('reclamation')
  if(!req.body){
    res.status(400).send({
      message: "Content can not be empty"
    })
  }
 
 
 const reclamation = new Reclamation ({
   
    id_commande  : req.body.id_commande ,
   id_conducteur  : req.body.id_conducteur ,
   id_transporteur  : req.body.id_transporteur ,
   id_expediteur  : req.body.id_expediteur ,
   type : req.body.type,
    image : req.body.image 
 });

 /*console.log('ttt')
console.log(conducteur)  */

 // Save Region in the database
 Reclamation.create(reclamation, (err, data) => {
  // console.log('test');
   if (err)
     res.status(500).send({
       message:
         err.message || "Some error occurred while creating the region ."
     });
   else res.send(data);
 });


}
exports.findByExpediteur = (req, res) => {
  //console.log(req.params.regionId_ville)

  Reclamation.findByExpediteur(req.params.expediteur, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Reclamation with id_expediteur ${req.params.expediteur}.`
        });
      } else {
        res.status(500).send({
          message: "Error retrieving Reclamation with id_expediteur " + req.params.expediteur
        });
      }
    } else res.send(data);
  });
};
exports.findByTransporteur = (req, res) => {
//  console.log(req.params.transporteur)

  Reclamation.findByTransporteur(req.params.transporteur, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Reclamation with id_transporteur ${req.params.transporteur}.`
        });
      } else {
        res.status(500).send({
          message: "Error retrieving Reclamation with id_transporteur " + req.params.transporteur
        });
      }
    } else res.send(data);
  }); 
};
exports.findAll = (req, res) => {
//  console.log(req.params.transporteur)

  Reclamation.findAll( (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Reclamation.`
        });
      } else {
        res.status(500).send({
          message: "Error retrieving Reclamation " 
        });
      }
    } else res.send(data);
  }); 
};