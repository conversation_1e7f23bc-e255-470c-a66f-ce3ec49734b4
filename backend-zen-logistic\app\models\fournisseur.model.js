// Importez la connexion à la base de données
const sql = require('./db.js');
const { poolPromise } = require('./dbSage.js');

const cron = require('node-cron');
const fs = require('fs');
const path = require('path');

const FournisseurModel = function(fournisseur) {
  this.nom_fournisseur = fournisseur.nom_fournisseur;
  this.mat_fisc = fournisseur.mat_fisc;
  this.sous_traitant = fournisseur.sous_traitant;
  this.adresse = fournisseur.adresse
  this.mail = fournisseur.mail,
  this.flux=fournisseur.flux,
  this.entreposage=fournisseur.entreposage,
  this.code_cegid=fournisseur.code_cegid
  this.code_sageX3=fournisseur.code_sageX3

};

FournisseurModel.create = (newFournisseur, result) => {
  if (newFournisseur.mat_fisc === '0000') {
    // Si mat_fisc est "0000", effectuez l'insertion directe
    sql.query('INSERT INTO fournisseur SET ?', newFournisseur, (err, res) => {
      if (err) {
        console.log('Erreur lors de la création du fournisseur : ', err);
        result(err, null);
        return;
      }

      console.log('Fournisseur créé : ', { id: res.insertId, ...newFournisseur });
      result(null, { id: res.insertId, ...newFournisseur });
    });
  } else {
    // Si mat_fisc n'est pas "0000", vérifiez d'abord si le fournisseur existe
    sql.query(
      'SELECT * FROM fournisseur WHERE mat_fisc = ?',
      [newFournisseur.mat_fisc],
      (err, existingFournisseurs) => {
        if (err) {
          console.log('Erreur lors de la vérification du fournisseur existant : ', err);
          result(err, null);
          return;
        }

        // Si le fournisseur existe déjà, informez le client
        if (existingFournisseurs.length > 0) {
          result({ message: 'Fournisseur déjà existant' }, null);
          return;
        }

        // Si le fournisseur n'existe pas, procédez à l'insertion
        sql.query('INSERT INTO fournisseur SET ?', newFournisseur, (err, res) => {
          if (err) {
            console.log('Erreur lors de la création du fournisseur : ', err);
            result(err, null);
            return;
          }

          console.log('Fournisseur créé : ', { id: res.insertId, ...newFournisseur });
          result(null, { id: res.insertId, ...newFournisseur });
        });
      }
    );
  }
};







FournisseurModel.findAll = (result) => {
  sql.query(
    "SELECT * FROM fournisseur  ORDER BY nom_fournisseur ASC",
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      //console.log("Fournisseur: ", res);
      result(null, res);
    }
  );
};

FournisseurModel.findClientToInvoice = (result) => {
  sql.query(
    `SELECT DISTINCT f.* 
    FROM fournisseur f
    JOIN point_chargement pc ON pc.id_client = f.id
    WHERE pc.status = 'à facturer' 
      AND pc.id_client IS NOT NULL
    ORDER BY f.nom_fournisseur ASC`,
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      //console.log("Fournisseur: ", res);
      result(null, res);
    }
  );
};






FournisseurModel.findById = (id, result) => { 
  sql.query("SELECT * FROM fournisseur WHERE id = ?", [id], (err, res) => { 
    if (err) {
      console.log("Error: ", err);
      result(err, null);
      return;
    }

    if (res.length === 0) {
      result({ kind: "not_found" }, null);
      return;
    }
    result(null, res[0]);
  });
};


FournisseurModel.delete = (fournisseurId, result) => {
  sql.query("DELETE FROM fournisseur WHERE id = ?", fournisseurId, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      // No fournisseur found with the given id
      result({ kind: "not_found" }, null);
      return;
    }

    console.log("Deleted fournisseur with id: ", fournisseurId);
    result(null, res);
  });
};


FournisseurModel.findByMatricule = (mat_fisc, result) => {
    const sqlQuery = "SELECT * FROM fournisseur WHERE mat_fisc = ?";
    console.log("SQL Query:", sqlQuery, [mat_fisc]);
  
    sql.query(sqlQuery, [mat_fisc], (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      console.log("Fournisseur found: ", res);
      result(null, res);
    });
  };




  FournisseurModel.findByType = (type, result) => {
    const sqlQuery = `SELECT DISTINCT fournisseur.*
    FROM fournisseur
    JOIN destination ON fournisseur.id = destination.id_fournisseur
    JOIN destination_typecommande ON destination.id = destination_typecommande.id_destination
    WHERE destination_typecommande.id_typecommande = ?;
    `;
    console.log("SQL Query:", sqlQuery, [type]);
  
    sql.query(sqlQuery, [type], (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }
  
      console.log("Fournisseur found: ", res);
      result(null, res);
    });
  };


  FournisseurModel.update = (fournisseurId, adresse, sous_Traitant,mail,entreposage,flux,code_cegid, result) => {
    sql.query(
        "UPDATE fournisseur SET adresse = ?, sous_traitant = ?,mail=?,entreposage=?,flux=?,code_cegid=? WHERE id = ?",
        [adresse, sous_Traitant,mail,entreposage,flux,code_cegid, fournisseurId],
        (err, res) => {
            if (err) {
                console.log("error: ", err);
                result(err, null);
                return;
            }

            if (res.affectedRows == 0) {
                // Fournisseur non trouvé avec l'ID spécifié
                result({ kind: "not_found" }, null);
                return;
            }

            //console.log("Fournisseur mis à jour: ", { id: fournisseurId, adresse, sous_Traitant });
            result(null, { id: fournisseurId,  });
        }
    );
};






function deletePDFFiles() {
  const directoryPath = path.join(__dirname, '../../factures');
  console.log(directoryPath)
  // Lire le contenu du répertoire des factures
  fs.readdir(directoryPath, (err, files) => {
    if (err) {
      console.error('Erreur lors de la lecture du répertoire des factures :', err);
      return;
    }

    // Parcourir tous les fichiers dans le répertoire
    files.forEach(file => {
      // Vérifier si le fichier est un fichier PDF
      if (file.endsWith('.pdf')) {
        // Construire le chemin complet du fichier
        const filePath = path.join(directoryPath, file);
        
        // Supprimer le fichier PDF
        fs.unlink(filePath, err => {
          if (err) {
            console.error('Erreur lors de la suppression du fichier PDF :', err);
            return;
          }
          console.log(`Le fichier PDF ${file} a été supprimé avec succès.`);
        });
      }
    });
  });
}



FournisseurModel.findFournisseurWhithFLux = (result) => {
  sql.query("SELECT * FROM fournisseur WHERE flux=true ORDER BY nom_fournisseur ASC", (err, res) => {
      if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
      }
      result(null, res);
  });
};

FournisseurModel.findFournisseurWhithEntreposage = (result) => {
  sql.query("SELECT * FROM fournisseur WHERE entreposage = true", (err, res) => {
      if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
      }
      result(null, res);
  });
};


FournisseurModel.findClientFromSageByMat = async (mat, result) => {
  try {
    const pool = await poolPromise;

    // Requête corrigée avec guillemet manquant
    const query = `
USE x3v12;
SELECT BPRNUM_0, BPRNAM_0, CRN_0
FROM ZENPRD.BPARTNER
WHERE BRGCOD_0 LIKE 'C%' 
AND CRN_0 LIKE '%${mat}%';

`;

    const res = await pool.request().query(query);
    result(null, res.recordset); 
  } catch (err) {
    console.error("Erreur lors de la récupération des clients: ", err);
    result(err, null);
  }
};



exports.updateFournisseurCode = async (id, bprnum) => {
  try {
    const pool = await poolPromise;
    await pool.request()
      .input('id', sql.Int, id) // Assurez-vous que le type correspond
      .input('bprnum', sql.VarChar, bprnum)
      .query('UPDATE fournisseur SET code_sageX3 = @bprnum WHERE id = @id'); // Mettez à jour le nom de la table et la colonne id si nécessaire
  } catch (err) {
    console.error('Erreur lors de la mise à jour du fournisseur:', err);
    throw err; // Propager l'erreur
  }
};


// cron.schedule('*/20 * * * * *', async () => {
//   try {
//     const pool = await poolPromise;

//     // Récupérer tous les fournisseurs
//     sql.query("SELECT * FROM fournisseur ORDER BY nom_fournisseur ASC", async (err, fournisseursResult) => {
//       if (err) {
//         console.error("Erreur lors de la récupération des fournisseurs: ", err);
//         return;
//       }

//       const fournisseurs = fournisseursResult;
//       if (!fournisseurs || fournisseurs.length === 0) {
//         console.log("Aucun fournisseur trouvé.");
//         return;
//       }

//       console.log(fournisseurs);

//       // Parcourir chaque fournisseur
//       for (const fournisseur of fournisseurs) {
//         const mat_fisc = fournisseur.mat_fisc;

//         // Vérifier si le matricule fiscal contient au moins 7 chiffres
//         if (!mat_fisc || mat_fisc.length < 7) {
//           // Si le matricule fiscal ne contient pas 7 chiffres, mettre à jour code_sageX3 à NULL
//           const updateNullQuery = `
//             UPDATE fournisseur 
//             SET code_sageX3 = NULL 
//             WHERE id = ${fournisseur.id}
//           `;

//           sql.query(updateNullQuery, (updateErr, updateResult) => {
//             if (updateErr) {
//               console.error("Erreur lors de la mise à jour du fournisseur avec code_sageX3 à NULL: ", updateErr);
//               return;
//             }
//             console.log(`Mise à jour du fournisseur nom ${fournisseur.nom_fournisseur} avec code_sageX3 à NULL`);
//           });
//           continue; // Passer au prochain fournisseur
//         }

//         // Extraire les 7 premiers chiffres de mat_fisc
//         const sevenDigits = mat_fisc.substring(0, 7);

//         // Exécuter la requête sur ZENP.BPARTNER
//         const query = `
//           USE x3v12;
// SELECT BPRNUM_0, BPRNAM_0, CRN_0
// FROM ZENPRD.BPARTNER
// WHERE BRGCOD_0 LIKE 'C%' 
// AND CRN_0 LIKE '%${sevenDigits}%'
//         `;

//         // Effectuer la requête sur ZENP.BPARTNER
//         const partnerResult = await pool.request().query(query);

//         // Si une ligne existe, récupérer BPRNUM_0 et mettre à jour le fournisseur
//         if (partnerResult.recordset.length > 0) {
//           const bprnum = partnerResult.recordset[0].BPRNUM_0;
//           if(partnerResult.recordset.length > 1){
//             console.log(mat_fisc)
//             continue;
//           }

//           // Mettre à jour le champ code_sageX3 dans la table fournisseur
//           const updateQuery = `
//             UPDATE fournisseur 
//             SET code_sageX3 = '${bprnum}' 
//             WHERE id = ${fournisseur.id}
//           `;
          
//           sql.query(updateQuery, (updateErr, updateResult) => {
//             if (updateErr) {
//               console.error("Erreur lors de la mise à jour du fournisseur: ", updateErr);
//               return;
//             }
//           //  console.log(`Mise à jour du fournisseur nom ${fournisseur.nom_fournisseur} avec BPRNUM_0: ${bprnum}`);
//           });
//         } else {
//           console.log(`Aucun partenaire trouvé pour le fournisseur ID ${fournisseur.id} avec le matricule: ${mat_fisc}`);
//         }
//       }
//     });
//   } catch (error) {
//     console.error('Erreur lors de l\'exécution de la tâche cron:', error);
//   }
// });



FournisseurModel.findClientInvoiced = (result) => {
  sql.query(
    `SELECT DISTINCT f.* 
    FROM fournisseur f
    JOIN facture pc ON pc.id_client = f.id
    WHERE pc.id_client IS NOT NULL
    ORDER BY f.nom_fournisseur ASC`,
    (err, res) => {
      if (err) {
        console.log("Erreur SQL: ", err);
        result(err, null);
        return;
      }

      result(null, res);
    }
  );
};




module.exports = FournisseurModel;
