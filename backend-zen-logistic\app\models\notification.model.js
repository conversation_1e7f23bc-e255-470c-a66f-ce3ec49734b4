// models/notification.model.js
const sql = require("./db.js");
const messageDb = require("./dbMess.js");
const admin = require('../config/firebase');
const Notification = function(notification) {
    this.id_user = notification.id_user;
    this.message = notification.message;
    this.url = notification.url;
};

// Retrieve user notifications
Notification.getUserNotifications = (userId, result) => {
    sql.query("SELECT * FROM notifications WHERE id_user = ? ORDER BY created_at DESC LIMIT 35", [userId], (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(err, null);
            return;
        }
        result(null, res);
    });
};

Notification.create = (newNotification, result) => {
    sql.query("INSERT INTO notifications SET ?", newNotification, (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(err, null);
            return;
        }
        result(null, { id: res.insertId, ...newNotification });
    });
};


Notification.markAsRead = (ids, result) => {
  const query = "UPDATE notifications SET readed = true WHERE id IN (?)"; // Using IN for multiple IDs
  sql.query(query, [ids], (err, res) => {
      if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
      }
      result(null, { affectedRows: res.affectedRows });
  });
};


Notification.createNotificationForAdmins = (message,url, callback) => {
  // First, retrieve admin users
  sql.query("SELECT id FROM utilisateurs WHERE type_utilisateur IN ('Administrateur', 'SuperAdmin')", (err, adminUsers) => {
      if (err) {
          console.log("Error retrieving admin users: ", err);
          callback(err, null);
          return;
      }

      // Check if there are admin users
      if (adminUsers.length === 0) {
          console.log("No admin users found.");
          callback(null, { message: "No admin users found." });
          return;
      }

      // Prepare notifications for each admin user
      const notificationsToCreate = adminUsers.map(user => ({
          id_user: user.id,
          message: message,
          url:url
      }));

      // Insert notifications into the database
      const createdNotifications = [];
      let errorOccurred = false;

      notificationsToCreate.forEach(notification => {
          Notification.create(notification, (err, createdNotification) => {
              if (err) {
                  console.log("Error creating notification for user ID:", notification.id_user);
                  errorOccurred = true; // Mark that an error occurred
                  return; // Optionally handle this error
              }
              createdNotifications.push(createdNotification);
          });
      });

      // After all notifications are attempted to be created
      if (!errorOccurred) {
          callback(null, createdNotifications);
      } else {
          callback({ message: "Some notifications could not be created." }, null);
      }
  });
};


Notification.countMessageNotReaded = (userId, result) => {
    messageDb.query("SELECT COUNT(*) AS unreadCount FROM `message_utilisateur` WHERE est_lu = false AND id_utilisateur = ?", [userId], (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(err, null);
            return;
        }

        // Renvoie directement le nombre de messages non lus
        const unreadCount = (res[0] && res[0].unreadCount) ? res[0].unreadCount : 0; // Fournir une valeur par défaut de 0
        result(null, unreadCount);
    });
};


Notification.updateMessagesAsRead = (userId, result) => {
    const query = `
        UPDATE message_utilisateur 
        SET est_lu = true, date_lecture = NOW() 
        WHERE est_lu = false AND id_utilisateur = ?
    `;

    messageDb.query(query, [userId], (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(err, null);
            return;
        }

        // Renvoie le nombre de messages mis à jour
        result(null, res.affectedRows); // res.affectedRows contains the number of rows updated
    });
};


Notification.sendNotificationToUserFireBase = (driverId, title, message, result) => {
    // La requête pour récupérer le token de notification de l'utilisateur
    const query = `SELECT notification_token FROM conducteur WHERE id = ?`;

    sql.query(query, [driverId], async (err, res) => {
        if (err) {
            console.log("Error: ", err);
            result(err, null); // Retourne l'erreur si la requête échoue
            return;
        }

        // Vérifier si l'utilisateur existe
        if (res && res.length > 0) {
            const notificationToken = res[0].notification_token;

            // Vérifier si un token est disponible
            if (!notificationToken) {
                result(new Error('Aucun token de notification trouvé pour cet utilisateur.'), null);
                return;
            }

            // Construire la notification
            const messagePayload = {
                notification: {
                  title: title,
                  body: message,
                },
                // android: {
                //   notification: {
                //     icon: "https://images.zen.com.tn/medias/images/logo_zen.png"  // Icône de la notification pour Android
                //   }
                // },
                token: notificationToken,
              };
              

            try {
                // Envoyer la notification
                const response = await admin.messaging().send(messagePayload);
                //result(null, { success: true, message: 'Notification envoyée avec succès.', response });
            } catch (error) {
                console.log('Erreur lors de l\'envoi de la notification:', error);
                result(error, null); // Retourne l'erreur si l'envoi échoue
            }
        } else {
            result(new Error('Utilisateur non trouvé'), null);
        }
    });
};





module.exports = Notification;
