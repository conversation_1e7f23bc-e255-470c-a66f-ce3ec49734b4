const sql = require("./db.js");
const Role = function(role) {
    this.id= role.id ;
    this.nom = role.nom ;
    this.value = role.value ;

  } ;



  Role.getAll = result => {
    sql.query("SELECT * FROM role", (err, res) => {
      if (err) {
        console.log("SQL error: ", err);
        result(err, null); // Remplacer null par err en cas d'erreur
        return;
      }
  
     // console.log("roles: ", res);
      result(null, res);
    });
  };
  module.exports = Role ; 
