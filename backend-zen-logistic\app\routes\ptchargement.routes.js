module.exports = app => {
    const ptchargement = require("../controllers/ptchargement.controllers.js") ;

  // Create a new ptchargement
  app.post("/api/ptchargement", ptchargement.create) ;
  app.post("/api/searchBysku", ptchargement.findLignesByTypeAndSku);
  app.post("/api/ptchargement/sendSms", ptchargement.sendSMS) ;
  
  // Retrieve all ptchargement
  app.get("/api/ptchargement/valid", ptchargement.findAllValid);

  app.get("/api/ptchargement", ptchargement.findAll);
  app.get("/api/findLignesByIdToVerification/:id", ptchargement.findLignesByIdToVerification);

  // Delete a ptchargement with ptchargementid
  app.get("/api/ptchargement/delete/:ptchargementId", ptchargement.delete);
  
  app.get("/api/ptchargement/:idCommande",ptchargement.getComm);
  app.get("/api/pt/reserved", ptchargement.findReservedByDateAndCamion);
  app.get("/api/pt/expedied", ptchargement.findExpedied);

  app.get("/api/pt/findLineToAdjust", ptchargement.findLineToAdjust);


  app.get("/api/ligneCmd/toFac", ptchargement.findAFactureByClient);
  app.get("/api/ligneCmd/type", ptchargement.findLivredByDateAndtype);
  app.get("/api/ligneCmdByDemand/:id_demandeur", ptchargement.findReservedByIdUser); 
  app.get("/api/ligneCmdExpByDemand/:id_demandeur", ptchargement.findExpediedByIdUser);
  app.get("/api/ligneCmdLivredByDemand/:id_demandeur", ptchargement.findLivredByIdUser);
  app.get("/api/findLignesByIdVoyage/:idVoyage", ptchargement.findLignesByIdVoyage);

  app.get("/api/countValid", ptchargement.countValid);
  app.get("/api/countExpedied", ptchargement.countExpedied);
  app.get("/api/countReserved", ptchargement.countReserved);
  app.get("/api/countDelivred", ptchargement.countDelivred);
  app.get("/api/findVoyageList", ptchargement.findVoyageList);
  app.get("/api/findVoyageListExp", ptchargement.findVoyageListExp);
  app.get("/api/findVoyageListToAdjust", ptchargement.findVoyageListToAdjust);

  app.get("/api/volumeBytrip/:id_camion/:id_conducteur/:date_voyage", ptchargement.findLigneInSametrip);
  app.get("/api/findAllLivred", ptchargement.findAllLivred);
  app.get("/api/findAllAdjusted", ptchargement.findAllAdjusted);

  
 // app.get("/api/findInspection", ptchargement.findToInspection);
  app.get("/api/findAllToInspection", ptchargement.findAllToInspection);
  app.post("/api/findDelivredByTruck", ptchargement.findLigneDelivredByTruck);
  app.post("/api/findLigneDelivredByDriver", ptchargement.findLigneDelivredByDriver);
  app.get("/api/findReservedByChef/:idChef", ptchargement.findReservedByChef);
  app.get("/api/findExpediedByChef/:idChef", ptchargement.findExpediedByChef);
  app.get("/api/findDeliveredByChef/:idChef", ptchargement.findDeliveredByChef);
  app.get("/api/findAllValidWithLocalisation", ptchargement.findAllValidWithLocalisation);
  app.get("/api/getLigneByDestinationAndUser/:id", ptchargement.getLigneByDestinationAndUser);
  app.get("/api/getLigneByDestinationAndUserLiv/:id", ptchargement.getLigneByDestinationAndUserLiv);
  app.get("/api/findAllRetourAndInter", ptchargement.findAllRetourAndInter);
  app.get("/api/pdfNotUpdated", ptchargement.pdfNotUpdated);

  
  
  // app.get("/api/ptdecharge/:idCommande",ptchargement.getdecharge)

    app.get("/api/ptdechargement/:idCommande",ptchargement.getPtdech)
    app.get("/api/ptchargDecharg/:idCommande",ptchargement.getPtdechChar)
    app.post("/api/addLieuxDechar",ptchargement.addLieuxDechar)
    app.post("/api/getLienuxDecharByCustomer",ptchargement.getLienuxDecharByCustomer)
    app.put("/api/ptchargement/:id",ptchargement.updateVolume)
    app.put("/api/ptchargement/valid/:id",ptchargement.updateStatusValid)
    app.put("/api/ptchargement/cancel/:id",ptchargement.updateStatusCancel)
    app.post("/api/ptchargement/reserved",ptchargement.updateReserved)
    app.put("/api/ptchargement/reservation/:id",ptchargement.updateReservation)
    app.put("/api/ptchargement/expedition/:id",ptchargement.updateExpedition)
    app.put("/api/ptchargement/updateStatusNoPickup/:id",ptchargement.updateStatusNoPickup)
    app.put("/api/ptchargement/livred/:id",ptchargement.updateStatusLivred)
    app.put("/api/ptchargement/client/:id",ptchargement.updateClient)
    app.put("/api/updateStatusToBeInvoiced/:id",ptchargement.updateStatusToBeInvoiced)
    app.put("/api/updatePieceCegid/:id",ptchargement.updatePieceCegid)
    app.get("/api/findLignesByweek/:intervalle",ptchargement.findLignesByweek)
    app.post("/api/findRetard",ptchargement.findRetard)
    app.post("/api/getProductivity",ptchargement.getProductivity)
    app.post("/api/MoyVolumePerVoyage",ptchargement.MoyVolumePerVoyage)
    app.post("/api/voyagePerDestination",ptchargement.voyagePerDestination)
    app.post("/api/getIndicateurByConducteur",ptchargement.getIndicateurByConducteur)
    app.post("/api/updateVolumeXls",ptchargement.updateVolumeXls)  
    app.put("/api/ptchargement/updatePanne/:id",ptchargement.updatePanne)
    //app.post("/api/adjustPrice",ptchargement.adjustPrice)  
    app.post("/api/fixPrice",ptchargement.fixPrice)  
    app.post("/api/ptchargement/findByDestinationsAndDateVoyage",ptchargement.findByDestinationsAndDateVoyage)
    app.post("/api/ptchargement/updateVolumeColisage",ptchargement.updateVolumeColisage)
    app.post("/api/ptchargement/updateStatusReady",ptchargement.updateStatusReady)
    app.get("/api/getLigneWithPackageById/:id",ptchargement.getLigneWithPackageById)
    app.get("/api/updateVoyageToAdjust", ptchargement.updateVoyageToAdjust);


    
}