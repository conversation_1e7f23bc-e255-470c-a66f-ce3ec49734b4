import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { Observable } from 'rxjs';

const httpOptions = {
  headers: new HttpHeaders({ 
    'Content-Type': 'application/json', 
    'Authorization': 'Basic ' + btoa('med:123456') 
  })
};

@Injectable({
  providedIn: 'root'
})
export class ComplaintService {
  private apiURL = environment.apiURL;

  constructor(private http: HttpClient) { }

  /**
   * Get complaints by expeditor
   * @param id - Expeditor ID
   * @returns Observable with complaints list
   * @originalName getReclamationExp
   */
  getComplaintsByExpeditor(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'reclamation/expediteur/' + id, httpOptions);
  }

  /**
   * Get complaints by transporter
   * @param id - Transporter ID
   * @returns Observable with complaints list
   * @originalName getReclamationTransporteur
   */
  getComplaintsByTransporter(id: string): Observable<any> {
    return this.http.get<any>(this.apiURL + 'reclamation/transporteur/' + id, httpOptions);
  }

  /**
   * Get all complaints
   * @returns Observable with complaints list
   * @originalName getReclamations
   */
  getAllComplaints(): Observable<any> {
    return this.http.get<any>(this.apiURL + 'reclamations', httpOptions);
  }
}
