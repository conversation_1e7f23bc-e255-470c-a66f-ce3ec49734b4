require('dotenv').config();



// db.sage.js
// module.exports = {
//   USER: 'DEV',
//   PASSWORD: 'devm@2024',
//   HOST: '************\\ZEN', // Serveur SQL Server
//   DB: 'x3zen',
//   PORT: 1433,
//   options: {
//     encrypt: false, // Mettre à true si vous utilisez SSL
//     trustServerCertificate: true // Ignorer les problèmes de certificat
//   }
// };


module.exports = {
  USER: process.env.USER_SAGE,
  PASSWORD:  process.env.PASSWORD_SAGE,
  HOST:  process.env.HOST_SAGE,
  DB:  process.env.DB_SAGE,
  PORT: parseInt(process.env.PORT_SAGE),
  options: {
    encrypt: false,
    trustServerCertificate: true
  }
};