// controllers/notification.controllers.js
const Notification = require("../models/notification.model.js");

let clients = []; // Array to store connected clients

// Retrieve all notifications for a user from the database
exports.getUserNotifications = (req, res) => {
    const userId = req.params.userId; // Get userId from request parameters

    Notification.getUserNotifications(userId, (err, data) => {
        if (err) {
            return res.status(500).send({
                message: err.message || "Some error occurred while retrieving notifications."
            });
        }
        res.send(data);
    });
};

// Listen for notifications via SSE
exports.listenForNotifications = (req, res) => {
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    // Add the client to the clients array
    clients.push(res);
    console.log("Client connected. Total clients: ", clients.length);

    // Remove client when connection is closed
    req.on('close', () => {
        clients = clients.filter(client => client !== res);
        console.log("Client disconnected. Total clients: ", clients.length);
    });
};

// Function to notify all clients
function notifyClients(notification) {
    clients.forEach(client => {
        client.write(`data: ${JSON.stringify(notification)}\n\n`);
    });
}

// Function to create a new notification
exports.createNotification = (req, res) => {
    const newNotification = {
        id_user: req.body.id_user,
        message: req.body.message,  
        url: req.body.url,
        timestamp: new Date()       
    };

    // Validate the incoming data (basic validation)
    if (!newNotification.id_user || !newNotification.message) {
        return res.status(400).send({
            message: "User ID and message are required."
        });
    }

    // Save the notification to the database
    Notification.create(newNotification, (err, notification) => {
        if (err) {
            return res.status(500).send({
                message: err.message || "Some error occurred while creating the notification."
            });
        }

        notifyClients(notification);
        res.status(201).send(notification);
    });
};

exports.updateNotificationStatus = (req, res) => {
  const { ids } = req.body;

  if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).send({
          message: "Invalid request: IDs are required."
      });
  }

  Notification.markAsRead(ids, (err, result) => {
      if (err) {
          return res.status(500).send({
              message: err.message || "Some error occurred while updating notification status."
          });
      }
      res.send({ message: "Notifications updated successfully.", result });
  });
};


exports.countMessageNotReaded = (req, res) => {
    const userId = req.params.userId; // Récupérer l'userId des paramètres de la requête

    Notification.countMessageNotReaded(userId, (err, unreadCount) => { // Renvoie directement le nombre
        if (err) {
            return res.status(500).send({
                message: err.message || "Une erreur est survenue lors de la récupération des notifications."
            });
        }

        // Renvoie du nombre dans la réponse
        res.status(200).json({ unreadCount }); // Envoie directement le nombre
    });
};

exports.updateMessagesAsRead = (req, res) => {
    const userId = req.params.userId; // Récupérer l'userId des paramètres de la requête

    Notification.updateMessagesAsRead(userId, (err, result) => {
        if (err) {
            return res.status(500).send({
                message: err.message || "Une erreur est survenue lors de la mise à jour des messages."
            });
        }

        res.status(200).send({ message: "Messages updated successfully", result });
    });
};



exports.sendNotificationToUserFireBase = (req, res) => {
    const { driverId, title, message } = req.body;

    // Vérification que les champs nécessaires sont présents
    if (!driverId || !title || !message) {
        return res.status(400).send({
            message: "Invalid request: driverId, title, and message are required."
        });
    }

    // Appel de la fonction pour envoyer la notification
    Notification.sendNotificationToUserFireBase(driverId, title, message, (err, result) => {
        if (err) {
            return res.status(500).send({
                message: err.message || "Some error occurred while sending the notification."
            });
        }
        res.send({ message: "Notification sent successfully.", result });
    });
};



