/**
 * @swagger
 * tags:
 *   name: Commande
 *   description: Opérations sur les commandes
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Commande:
 *       type: object
 *       required:
 *         - type_camion
 *         - type_chargement
 *         - date_depart
 *         - date_arrivee
 *         - type_conditionnement
 *         - type_marchandise
 *         - poids
 *         - volume
 *         - quantite
 *         - statut
 *         - prix
 *       properties:
 *         id:
 *           type: integer
 *           description: ID unique de la commande
 *         type_camion:
 *           type: string
 *           description: Type de camion
 *         type_chargement:
 *           type: string
 *           description: Type de chargement
 *         date_depart:
 *           type: string
 *           format: date
 *           description: Date de départ
 *         date_arrivee:
 *           type: string
 *           format: date
 *           description: Date d'arrivée
 *         type_conditionnement:
 *           type: string
 *           description: Type de conditionnement
 *         type_marchandise:
 *           type: string
 *           description: Type de marchandise
 *         poids:
 *           type: number
 *           description: Poids de la marchandise
 *         volume:
 *           type: number
 *           description: Volume de la marchandise
 *         quantite:
 *           type: integer
 *           description: Quantité de marchandise
 *         mention:
 *           type: string
 *           description: Mentions spéciales
 *         fragile:
 *           type: boolean
 *           description: Indique si la marchandise est fragile
 *         code:
 *           type: string
 *           description: Code de la commande
 *         enrg_commande:
 *           type: string
 *           description: Indique si la commande est enregistrée
 *         ajoutee_par:
 *           type: string
 *           description: Utilisateur ayant ajouté la commande
 *         reservee_par:
 *           type: string
 *           description: Utilisateur ayant réservé la commande
 *         statut:
 *           type: string
 *           description: Statut de la commande
 *         prix:
 *           type: number
 *           description: Prix de la commande
 *         id_camion:
 *           type: integer
 *           description: ID du camion associé
 *         id_conducteur:
 *           type: integer
 *           description: ID du conducteur associé
 *         labelle:
 *           type: string
 *           description: Label de la commande
 *         nom_conducteur:
 *           type: string
 *           description: Nom du conducteur
 *         nom_utilisateur:
 *           type: string
 *           description: Nom de l'utilisateur
 */

/**
 * @swagger
 * /api/commande:
 *   post:
 *     tags: [Commande]
 *     summary: Créer une nouvelle commande
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Commande'
 *     responses:
 *       201:
 *         description: Commande créée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Commande'
 *       400:
 *         description: Le contenu ne peut pas être vide

 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes
 *     responses:
 *       200:
 *         description: Liste des commandes récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/commande/{commandeId}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer une commande par ID
 *     parameters:
 *       - in: path
 *         name: commandeId
 *         required: true
 *         description: ID de la commande à récupérer
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Commande récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Commande non trouvée
 *       500:
 *         description: Erreur lors de la récupération de la commande

 *   put:
 *     tags: [Commande]
 *     summary: Mettre à jour une commande par ID
 *     parameters:
 *       - in: path
 *         name: commandeId
 *         required: true
 *         description: ID de la commande à mettre à jour
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Commande'
 *     responses:
 *       200:
 *         description: Mise à jour réussie
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Commande'
 *       400:
 *         description: Le contenu ne peut pas être vide
 *       404:
 *         description: Commande non trouvée
 *       500:
 *         description: Erreur lors de la mise à jour de la commande

 *   delete:
 *     tags: [Commande]
 *     summary: Supprimer une commande par ID
 *     parameters:
 *       - in: path
 *         name: commandeId
 *         required: true
 *         description: ID de la commande à supprimer
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Commande supprimée avec succès
 *       404:
 *         description: Commande non trouvée
 *       500:
 *         description: Erreur lors de la suppression de la commande

 * /api/email:
 *   post:
 *     tags: [Commande]
 *     summary: Envoyer un e-mail
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - content
 *             properties:
 *               email:
 *                 type: string
 *                 description: Adresse e-mail du destinataire
 *               content:
 *                 type: string
 *                 description: Contenu de l'e-mail
 *     responses:
 *       200:
 *         description: E-mail envoyé avec succès
 *       400:
 *         description: Le contenu ne peut pas être vide
 *       500:
 *         description: Erreur lors de l'envoi de l'e-mail

 * /api/getAllCommande/{idUser}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes enregistrées pour un utilisateur
 *     parameters:
 *       - in: path
 *         name: idUser
 *         required: true
 *         description: ID de l'utilisateur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des commandes récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Aucune commande trouvée pour cet utilisateur
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/comm/update/{commandeId}:
 *   put:
 *     tags: [Commande]
 *     summary: Mettre à jour une commande par ID (administrateur)
 *     parameters:
 *       - in: path
 *         name: commandeId
 *         required: true
 *         description: ID de la commande à mettre à jour
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Commande'
 *     responses:
 *       200:
 *         description: Mise à jour réussie
 *       400:
 *         description: Le contenu ne peut pas être vide
 *       404:
 *         description: Commande non trouvée
 *       500:
 *         description: Erreur lors de la mise à jour de la commande

 * /api/comm/deleteEnr/{commandeId}:
 *   put:
 *     tags: [Commande]
 *     summary: Marquer une commande comme non enregistrée
 *     parameters:
 *       - in: path
 *         name: commandeId
 *         required: true
 *         description: ID de la commande à mettre à jour
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Commande mise à jour avec succès
 *       404:
 *         description: Commande non trouvée
 *       500:
 *         description: Erreur lors de la mise à jour de la commande

 * /api/comm/{idUser}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes pour un utilisateur
 *     parameters:
 *       - in: path
 *         name: idUser
 *         required: true
 *         description: ID de l'utilisateur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des commandes récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Aucune commande trouvée pour cet utilisateur
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/com/{idUser}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes validées pour un utilisateur
 *     parameters:
 *       - in: path
 *         name: idUser
 *         required: true
 *         description: ID de l'utilisateur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des commandes validées récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Aucune commande validée trouvée pour cet utilisateur
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/commande/exp/{idUser}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes en cours de traitement pour un utilisateur
 *     parameters:
 *       - in: path
 *         name: idUser
 *         required: true
 *         description: ID de l'utilisateur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des commandes en cours récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Aucune commande en cours trouvée pour cet utilisateur
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/comTrpValide/{idUser}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes validées pour un transporteur
 *     parameters:
 *       - in: path
 *         name: idUser
 *         required: true
 *         description: ID de l'utilisateur (transporteur)
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des commandes validées pour le transporteur récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Aucune commande validée trouvée pour ce transporteur
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/commande/Reservees/{idUser}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes réservées pour un utilisateur
 *     parameters:
 *       - in: path
 *         name: idUser
 *         required: true
 *         description: ID de l'utilisateur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des commandes réservées récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Aucune commande réservée trouvée pour cet utilisateur
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/commande/Res/{idUser}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes réservées (expéditeur) pour un utilisateur
 *     parameters:
 *       - in: path
 *         name: idUser
 *         required: true
 *         description: ID de l'utilisateur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des commandes réservées récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Aucune commande réservée trouvée pour cet utilisateur
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/commande/conducteur/{conducteurId}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer les commandes par conducteur
 *     parameters:
 *       - in: path
 *         name: conducteurId
 *         required: true
 *         description: ID du conducteur
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des commandes récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Aucune commande trouvée pour ce conducteur
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/comareserve:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes à réserver
 *     responses:
 *       200:
 *         description: Liste des commandes à réserver récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/comm/updatenote/{id}:
 *   put:
 *     tags: [Commande]
 *     summary: Mettre à jour la note d'une commande par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID de la commande
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - note
 *             properties:
 *               note:
 *                 type: string
 *                 description: Note à mettre à jour
 *     responses:
 *       200:
 *         description: Mise à jour réussie
 *       404:
 *         description: Commande non trouvée
 *       500:
 *         description: Erreur lors de la mise à jour de la commande

 * /api/commande/updateVolume/{id}:
 *   put:
 *     tags: [Commande]
 *     summary: Mettre à jour le volume d'une commande par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID de la commande
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - volume
 *             properties:
 *               volume:
 *                 type: number
 *                 description: Nouveau volume à mettre à jour
 *     responses:
 *       200:
 *         description: Mise à jour réussie
 *       404:
 *         description: Commande non trouvée
 *       500:
 *         description: Erreur lors de la mise à jour de la commande

 * /api/commande/Reserved/All:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes réservées
 *     responses:
 *       200:
 *         description: Liste des commandes réservées récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/commande/infoAdmin:
 *   post:
 *     tags: [Commande]
 *     summary: Envoyer des informations à l'administrateur
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               message:
 *                 type: string
 *                 description: Message à envoyer à l'administrateur
 *     responses:
 *       200:
 *         description: Informations envoyées avec succès
 *       500:
 *         description: Erreur lors de l'envoi des informations

 * /api/commande/{id}:
 *   delete:
 *     tags: [Commande]
 *     summary: Supprimer une commande par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID de la commande à supprimer
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Commande supprimée avec succès
 *       404:
 *         description: Commande non trouvée
 *       500:
 *         description: Erreur lors de la suppression de la commande

 * /api/getAllCommandeByChef/{idChef}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes associées à un chef
 *     parameters:
 *       - in: path
 *         name: idChef
 *         required: true
 *         description: ID du chef
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des commandes récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Aucune commande trouvée pour ce chef
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/getCommandeToValidateByChef/{idChef}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes à valider pour un chef
 *     parameters:
 *       - in: path
 *         name: idChef
 *         required: true
 *         description: ID du chef
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des commandes à valider récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Aucune commande à valider trouvée pour ce chef
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/findEnrgBychef/{idChef}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes enregistrées par un chef
 *     parameters:
 *       - in: path
 *         name: idChef
 *         required: true
 *         description: ID du chef
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des commandes enregistrées récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Aucune commande enregistrée trouvée pour ce chef
 *       500:
 *         description: Erreur lors de la récupération des commandes

 * /api/getAllComValidByChef/{idChef}:
 *   get:
 *     tags: [Commande]
 *     summary: Récupérer toutes les commandes validées par un chef
 *     parameters:
 *       - in: path
 *         name: idChef
 *         required: true
 *         description: ID du chef
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Liste des commandes validées récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Commande'
 *       404:
 *         description: Aucune commande validée trouvée pour ce chef
 *       500:
 *         description: Erreur lors de la récupération des commandes
 */
