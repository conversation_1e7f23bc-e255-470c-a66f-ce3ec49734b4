const Camion = require ("../models/camion.model.js") ; 

 
 // Create a Camion
 exports.create = (req, res)=>
 { 
   if(!req.body){
     res.status(400).send({
       message: "Content can not be empty"
     })
   }
  
  
  const camion = new Camion({
   // id:req.body.id ,
    type_camion:req.body.type_camion ,
    immatriculation: req.body.immatriculation,
    immat_rs_tn: req.body.immat_rs_tn,
    date_circulation: req.body.date_circulation,
    poids	: req.body.poids, 
    unite_poids: req. body.unite_poids,
    volume : req.body.volume,
    nombre_palettes: req.body.nombre_palettes,
    palette_met_eur : req.body.palette_met_eur,
    longeur : req.body.longeur,
    largeur : req.body.largeur,
    hauteur : req.body.hauteur,
    image_carte_grise : req.body.image_carte_grise,
    ajoutee_par : req.body.ajoutee_par,
   

  });

  Camion.create(camion, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while creating the camion ."
      });
    else res.send(data);
  });
}


// Retrieve all Camion from the database.
exports.findAll = (req, res) => {
  Camion.getAll((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving Camion."
      });
    else res.send(data);
  });
 
};

// Delete a camion with the specified camionId in the request
exports.delete = (req, res) => {
  Camion.remove(req.params.camionId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found camion with id ${req.params.camionId}.`
        });
      } else {
        res.status(500).send({
          message: "Could not delete camion with id " + req.params.camionId
        });
      }
    } else res.send({ message: `camion was deleted successfully!` });
  });
};

// Find a single camion with a camionId
exports.findOne = (req, res) => {

  Camion.findById(req.params.camionId, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found camion with id ${req.params.camionId}.`
        });
      } else {
        res.status(500).send({
          message: "Error retrieving camion with id " + req.params.camionId
        });
      }
    } else res.send(data);
  });
};



   exports.findAllCamion= (req, res) => {
    Camion.findCamion(req.params.idUser ,  (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Camion with idUser ${req.params.idUser} `
        });
      } else {
        res.status(500).send({
          message: "Error retrieving Camion with idUser " + req.params.idUser 
        });
      }
    } else res.send(data);
  });
  };


