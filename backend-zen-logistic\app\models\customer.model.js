const sql = require("./db.js");
const messSql = require("./dbMess.js");

// constructor

const Customer = function(customer) {
 // this.id=customer.id ;
  this.sexe = customer.sexe;
  this.nom = customer.nom;
  this.prenom = customer.prenom;
  this.cin = customer.cin;
  this.image_cin = customer.image_cin; 
  this.type_utilisateur = customer.type_utilisateur == 'Expéditeur'? 'Expediteur' : customer.type_utilisateur;
  this.forme_juridique = customer.forme_juridique;
  this.raison_sociale = customer.raison_sociale;
  this.num_tva = customer.num_tva ;
  this.email = customer.email;
  this.adresse = customer.adresse;
  this.mobile = customer.mobile;
  this.nom_utilisateur = customer.nom_utilisateur;
  this.mot_de_passe= customer.mot_de_passe;
  this.statut= customer.statut;
  this.cle_activation= customer.cle_activation;
  this.client_direct= customer.client_direct;
  this.color = customer.color;
} ;




// function create Customer
Customer.create = (newCustomer, result) => {
  sql.query("INSERT INTO utilisateurs SET ?", newCustomer, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    // Partie désactivée pour messSql :
    /*
    messSql.query("INSERT INTO utilisateurs SET ?", newCustomer, (messErr, messRes) => {
      if (messErr) {
        console.log("error: ", messErr);
        result(messErr, null);
        return;
      }

      result(null, { id1: res.insertId, id2: messRes.insertId, ...newCustomer });
    });
    */

    // Si tu veux quand même retourner quelque chose sans messSql :
    result(null, { id1: res.insertId, ...newCustomer });
  });
};




Customer.findById = (customerId, result) => {
  sql.query(`SELECT * FROM utilisateurs WHERE id = ${customerId}`, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.length) {
      //console.log("found customer: ", res[0]);
      result(null, res[0]);
      return;
    }

    // not found Customer with the id
    result({ kind: "not_found" }, null);
  });
};

//function get all customer 

Customer.getAll = result => {
  sql.query("SELECT * FROM utilisateurs", (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }

    //console.log("customer: ", res);
    result(null, res);
  });
};

Customer.updateById = (id, customer, result) => {
  sql.query(
    "UPDATE utilisateurs  SET statut=? , sexe =?, nom =?, prenom =? , cin =? , image_cin =?, type_utilisateur =? , forme_juridique =? , raison_sociale=? , num_tva=? , email =? , adresse =? , mobile =? , nom_utilisateur =? , mot_de_passe =?  WHERE id = ? ",
    [customer.statut , customer.sexe, customer.nom, customer.prenom, customer.cin, customer.image_cin ,customer.type_utilisateur , customer.forme_juridique , customer.raison_sociale , customer.num_tva , customer.email , customer.adresse,customer.mobile , customer.nom_utilisateur , customer.mot_de_passe , id ],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        // not found Customer with the id
        result({ kind: "not_found" }, null);
        return;
      }

    //  //console.log("updated customer: ", { id: id, ...customer });
      result(null, res);
    }
  );
};

// delete one cust with id 

Customer.remove = (id, result) => {
  sql.query("DELETE FROM utilisateurs WHERE id = ?", id, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }

    if (res.affectedRows == 0) {
      // not found Customer with the id
      result({ kind: "not_found" }, null);
      return;
    }

    //console.log("deleted customer with id: ", id);
    result(null, res);
  });
};

Customer.removeAll = result => {
  sql.query("DELETE FROM customers", (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }

    //console.log(`deleted ${res.affectedRows} customers`);
    result(null, res);
  });
};



// trouver un utilisateur par cle d'activation 

Customer.findByCle = (customerCleActivation, result) => {
  //console.log('cle')
  sql.query(`SELECT * FROM utilisateurs WHERE cle_activation = "${customerCleActivation}" `
   , (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.length) {
      //console.log("found customer: ", res[0]);
      result(null, res[0]);
      return;
    }

    // not found Customer with the id
    result({ kind: "not_found" }, null);
  });
}; 


//update by cle d'activation 

Customer.updateByCle = (cle_activation, customer, result) => {

  sql.query(
    `UPDATE utilisateurs  SET statut =?  WHERE cle_activation = "${cle_activation}"`,
    [  customer.statut ],
   
    (err, res) => {
      //console.log(res)

      if (err) {

        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        // not found Customer with the cle 
        result({ kind: "not_found" }, null);
        return;
      }

      
      result(null, {  ...customer });
    } 
  
    
  );  
};  

Customer.findAuth = (customerNu , customerMdp , result) => {
  //console.log(`SELECT * FROM utilisateurs WHERE email = "${customerNu}"  AND  mot_de_passe="${customerMdp}" `)
  sql.query(`SELECT * FROM utilisateurs WHERE email="${customerNu}"  AND  mot_de_passe="${customerMdp}" `
  , (err, res) => {  
    ////console.log('lalalal')

    if (err) {
      console.log("error:", err);
      result(err, null);
      return;
    } 

    if (res.length) {
      //console.log("found customer: ", res[0]);
      result(null, res[0]);
      return;
    } 

    // not found Customer with user name and password 
    result({ kind: "not_found" }, null);  

  });    
};    
 // Function update customer by ID  

 Customer.updateUserById = (id, customer, result) => {
  if (!customer.nom || !customer.prenom || !customer.email || !customer.type_utilisateur || 
    (customer.mot_de_passe && customer.mot444_de_passe.length < 8)) {
  result({ kind: "invalid_input" }, null);
  return;
}

  let query = `UPDATE utilisateurs SET nom = ?, prenom = ?, email = ?, type_utilisateur = ? WHERE id = ?`;
  
  let params = [customer.nom, customer.prenom, customer.email, customer.type_utilisateur, id];
  
  if (customer.mot_de_passe) {
    query = `UPDATE utilisateurs SET nom = ?, prenom = ?, email = ?, mot_de_passe = ?, type_utilisateur = ? WHERE id = ?`;
    params = [customer.nom, customer.prenom, customer.email, customer.mot_de_passe, customer.type_utilisateur, id];
  }
  
  sql.query(query, params, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }

    if (res.affectedRows === 0) {
      // Not found user with the given ID
      result({ kind: "not_found" }, null);
      return;
    }

    result(null, { id, ...customer });
  });
};


 


Customer.getAllTr = result => {
  sql.query(`SELECT *
  FROM utilisateurs
  WHERE type_utilisateur IN ('Transporteur', 'Transporteur sous traitance');`, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }

   // //console.log("customer: ", res);
    result(null, res);
  });
};



Customer.getAllExp = result => {
  sql.query(`SELECT * FROM utilisateurs WHERE type_utilisateur != "SuperAdmin"  `, (err, res) => {
    if (err) {
      //console.log("error: ", err);
      result(null, err);
      return;
    }

    ////console.log("customer: ", res);
    result(null, res);
  });
};




//update trp by id 

Customer.upCustTr = (id , customer, result) => {

  sql.query(
    `UPDATE utilisateurs  SET statut =?  WHERE id = "${id}"`,
    [  customer.statut ],
   
    (err, res) => {
      //console.log(res)

      if (err) {

        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        // not found Customer with the cle 
        result({ kind: "not_found" }, null);
        return;
      }

      
      result(null, {  ...customer });
    } 
  
    
  );  
};  

Customer.lastConnexion = (id, result) => {
  const currentDateTime = new Date(); // Récupérer la date et l'heure actuelles

  sql.query(
    `UPDATE utilisateurs SET last_connexion = ? WHERE id = ?`,
    [currentDateTime, id],  // Utilisation de currentDateTime pour last_connexion
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        return result(err, null);  // Utiliser result correctement ici
      }

      if (res.affectedRows == 0) {
        // Si aucun utilisateur n'est trouvé avec l'ID donné
        return result({ kind: "not_found" }, null);
      }

      // Si mise à jour réussie, renvoyer l'ID et la date de connexion
      result(null, { id: id, last_connexion: currentDateTime });
    }
  );
};

 





  Customer.getAllEntrprise = result => { 
    sql.query("SELECT DISTINCT(U.num_tva), case when (E.nom is null) then U.raison_sociale else E.nom end as raison_sociale , case when (E.adresse is null) then U.adresse else E.adresse end as adresse FROM `utilisateurs`U left join entreprise E on E.matricule=U.num_tva WHERE U.type_utilisateur='Expediteur' GROUP BY U.raison_sociale,E.nom", (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }
  
    //  //console.log("enreprise: ", res);
      result(null, res);
    });
    //result({"te":"gdfg"});
  };
  Customer.setClient = (req, result) => {
   sql.query("INSERT INTO exp_client (`id_exp`, `id_client`) VALUES (?,?)", [req.idExp,req.idClient], (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

   // //console.log("SETTING CLIENT: ", { exp :req.idExp, client :req.idClient });
    result(null, { exp :req.idExp, client :req.idClient });
  });
};
Customer.removeClient = (req, result) => {
  sql.query("delete from exp_client where `id_exp`= ? and  `id_client`=? ", [req.idExp,req.idClient], (err, res) => {
   if (err) {
     console.log("error: ", err);
     result(err, null);
     return;
   }

   //console.log("SETTING CLIENT: ", { exp :req.idExp, client :req.idClient });
   result(null, { exp :req.idExp, client :req.idClient });
 });
};


Customer.disabledClient = (req, result) => {
  //console.log("++++++++++++++++++++++++++++++++++++++++",req)
  const id = req.idClient; // Assuming req.idClient is the ID you want to use

  sql.query(
    `UPDATE utilisateurs SET statut = 'invalide' WHERE id = ?`,
    [id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(err, null);
        return;
      }

      // The update was successful
      //console.log("Updated successfully");
      result(null, res);
    }
  );
};

Customer.getAllClients = result => { 
  sql.query("select id,nom , prenom from utilisateurs where type_utilisateur='Client' and statut='activé' ", (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }

    //console.log("clients: ", res);
    result(null, res);
  });
  //result({"te":"gdfg"});
};
Customer.getClientByExp = (id, result) => {
 //console.log(`select U.id,U.nom , U.prenom from utilisateurs U inner join exp_client E ON U.id=E.id_client where U.type_utilisateur='Client' and U.statut='activé' and E.id_exp="${id}" `);
  sql.query(`select U.id,U.nom , U.prenom from utilisateurs U inner join exp_client E ON U.id=E.id_client where U.type_utilisateur='Client' OR U.type_utilisateur = 'Administrateur' and U.statut='activé' and E.id_exp="${id}" `
   , (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    if (res.length) {
    
      result(null, res );
      return;
    }

    // not found Customer with the id
    result({ kind: "not_found" }, null);
  });
}; 


Customer.findByEmail = (email, result) => {
  sql.query(`SELECT id FROM utilisateurs WHERE email = ?`, email, (err, res) => {
    if (err) {
      //console.log("Erreur: ", err);
      result(err, null);
      return;
    }

    if (res.length) {
      //console.log("Utilisateur trouvé: ", res[0]);
      result(null, res[0]);
      return;
    }

    // Utilisateur non trouvé avec l'e-mail
    result({ kind: "not_found" }, null);
  });
};




Customer.findAllClient = result => {
  sql.query(`
    SELECT utilisateurs.id, utilisateurs.nom_utilisateur
    FROM utilisateurs 
    LEFT JOIN chef_user ON utilisateurs.id = chef_user.id_user 
    WHERE chef_user.id_user IS NULL 
      AND (utilisateurs.type_utilisateur = "Expediteur" OR utilisateurs.type_utilisateur = "Client" OR utilisateurs.type_utilisateur = "GS" OR utilisateurs.type_utilisateur = "GE")`,
    (err, res) => {
      if (err) {
        console.log("error retrieving users not in chef_user:", err);
        result(err, null);
        return;
      }

      //console.log("Users not in chef_user retrieved successfully:", res);
      result(null, res);
    });
};





Customer.findAllChef = result => {
  sql.query(`SELECT * FROM utilisateurs WHERE  type_utilisateur = "Chef Departement"  `, (err, res) => {
    if (err) {
      //console.log("error: ", err);
      result(null, err);
      return;
    }

    ////console.log("customer: ", res);
    result(null, res);
  });
};


Customer.createChefUser = (id_chef, id_users, result) => {
  if (!id_chef || !id_users || id_users.length === 0) {
    // Vérifie si id_chef et id_users sont définis et non vides
    //console.log("Invalid id_chef or id_users.");
    result(null, "Invalid id_chef or id_users.");
    return;
  }

  // Échappez les valeurs id_chef et id_users si nécessaire

  const checkQuery = `SELECT * FROM chef_user WHERE id_chef = ${id_chef}`;
  sql.query(checkQuery, (err, res) => {
    if (err) {
      console.log("error checking chef_user table:", err);
      result(null, err);
      return;
    }

    const existingIds = res.map(entry => entry.id_user);
    const idsToDelete = existingIds.filter(id => !id_users.includes(id));

    if (idsToDelete.length > 0) {
      const deleteQuery = `DELETE FROM chef_user WHERE id_chef = ${id_chef} AND id_user IN (${idsToDelete.join(',')})`;
      sql.query(deleteQuery, (err, res) => {
        if (err) {
          console.log("error deleting chef_user entries:", err);
          result(null, err);
          return;
        }
        //console.log("Deleted chef_user entries successfully.");
      });
    }

    const newIds = id_users.filter(id => !existingIds.includes(id));
    if (newIds.length > 0) {
      const insertQuery = `INSERT INTO chef_user (id_chef, id_user) VALUES ${newIds.map(id => `(${id_chef}, ${id})`).join(',')}`;
      sql.query(insertQuery, (err, res) => {
        if (err) {
          console.log("error creating chef_user entries:", err);
          result(null, err);
          return;
        }
        //console.log("New chef_user entries created successfully.");
        result(null, "New chef_user entries created successfully.");
      });
    } else {
      //console.log("No new entries to create in chef_user table.");
      result(null, "No new entries to create in chef_user table.");
    }
  });
};



Customer.findUsersByChef = (chefId, result) => {
  sql.query(`
    SELECT utilisateurs.id, utilisateurs.nom_utilisateur
    FROM utilisateurs
    INNER JOIN chef_user ON utilisateurs.id = chef_user.id_user
    WHERE chef_user.id_chef = ?
  `, chefId, (err, res) => {
    if (err) {
      console.log("error retrieving users by chef:", err);
      result(err, null);
      return;
    }

    //console.log("Users belonging to chef retrieved successfully:", res);
    result(null, res);
  });
};



Customer.updateClientColor = (id, color, result) => {
  const query = `UPDATE utilisateurs SET color = ? WHERE id = ?`;

  sql.query(query, [color, id], (err, res) => {
    if (err) {
      console.log("Erreur lors de la mise à jour de la couleur :", err);
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      result({ kind: "not_found" }, null);
      return;
    }

    // Retourne les données mises à jour
    result(null, { id, color });
  });
};




module.exports = Customer; 


