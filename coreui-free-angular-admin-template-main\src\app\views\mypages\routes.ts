import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    data: {
      title: 'My Pages'
    },
    children: [
        {
            path: 'commandes',
            loadChildren: () => import('./commandes/routes').then((m) => m.routes),
            data: {
              title: 'Commandes'
            }
        },
        {
            path: 'inspection',
            loadChildren: () => import('./inspection/routes').then((m) => m.routes),
            data: {
              title: 'Inspection'
            }
        }
    ]
  }
];
