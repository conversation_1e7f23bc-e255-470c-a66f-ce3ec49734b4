const Region = require ("../models/region.model.js") ; 


// create  region  
exports.create = (req, res)=>
{ 
  //console.log('region')
  if(!req.body){
    res.status(400).send({
      message: "Content can not be empty"
    })
  }
 
 
 const region = new Region ({
   id:req.body.id ,
   nom_region:req.body.nom_region ,
   id_ville: req.body.id_ville,
   idVille : req.body.idVille 

 });

 /*//console.log('ttt')
//console.log(conducteur)  */

 // Save Region in the database
 Region.create(region, (err, data) => {
  // //console.log('test');
   if (err)
     res.status(500).send({
       message:
         err.message || "Some error occurred while creating the region ."
     });
   else res.send(data);
 });
}



// Find a single Region with a regionId_ville
exports.findOne = (req, res) => {
  ////console.log(req.params.regionId_ville)

  Region.findById(req.params.regionId_ville, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Region with id_ville ${req.params.regionId_ville}.`
        });
      } else {
        res.status(500).send({
          message: "Error retrieving Region with id " + req.params.regionId_ville
        });
      }
    } else res.send(data);
  });
};






// Retrieve all Region from the database.
exports.findAll = (req, res) => {
  Region.getAll((err, data) => {
   // //console.log('dododododod888888888888888888888888888odod') ;

    ////console.log(req.params.regionid_ville) 
   // //console.log(req.body);
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while retrieving Region."
      });
    else res.send(data);
  });
 
};