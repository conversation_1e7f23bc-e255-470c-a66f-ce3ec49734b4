/**
 * @swagger
 * tags:
 *   name: Prices
 *   description: Opérations liées à la gestion des prix
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Price:
 *       type: object
 *       required:
 *         - distance
 *         - prix_court_distance
 *         - prix_long_distance
 *         - montant
 *         - pourcentage
 *       properties:
 *         id:
 *           type: integer
 *           description: Identifiant unique du prix
 *         distance:
 *           type: number
 *           description: Distance associée au prix
 *         prix_court_distance:
 *           type: number
 *           description: Prix pour les courtes distances
 *         prix_long_distance:
 *           type: number
 *           description: Prix pour les longues distances
 *         montant:
 *           type: number
 *           description: Montant associé
 *         pourcentage:
 *           type: number
 *           description: Pourcentage associé
 */

/**
 * @swagger
 * /api/prices:
 *   get:
 *     tags: [Prices]
 *     summary: Récupérer tous les prix
 *     responses:
 *       200:
 *         description: Liste des prix récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Price'
 *       500:
 *         description: Erreur interne du serveur

 * /api/prices/{id}:
 *   put:
 *     tags: [Prices]
 *     summary: Mettre à jour un prix par ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID du prix à mettre à jour
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Price'
 *     responses:
 *       200:
 *         description: Prix mis à jour avec succès
 *       400:
 *         description: Requête invalide
 *       404:
 *         description: Prix non trouvé
 *       500:
 *         description: Erreur interne du serveur
 */
